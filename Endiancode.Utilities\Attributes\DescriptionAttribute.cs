﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DescriptionAttribute.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Endiancode.Utilities.Attributes
{
    using System;

    [AttributeUsage(AttributeTargets.Field)]
    public sealed class DescriptionAttribute : Attribute
    {
        public DescriptionAttribute(string value)
        {
            Description = value;
        }

        public string Description { get; private set; }
    }
}