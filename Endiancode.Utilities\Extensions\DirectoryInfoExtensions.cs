﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DirectoryInfoExtensions.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Endiancode.Utilities.Extensions
{
    using System.IO;

    public static class DirectoryInfoExtensions
    {
        public static void DeleteDirectoryContent(this DirectoryInfo directory)
        {
            foreach (FileInfo file in directory.GetFiles())
            {
                file.Delete();
            }

            foreach (DirectoryInfo subDirectory in directory.GetDirectories())
            {
                subDirectory.Delete(true);
            }
        }
    }
}