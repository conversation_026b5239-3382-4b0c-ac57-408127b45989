﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IEnumerableExtension.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Endiancode.Utilities.Extensions
{
    using System;
    using System.Collections.Generic;

    public static class IEnumerableExtensions
    {
        public static void ForEach<T>(this IEnumerable<T> source,  Action<T> action)
        {
            foreach (T element in source)
            {
                action(element);
            }
        }
    }
}