﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="StringExtension.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Endiancode.Utilities.Extensions
{
    using System;

    public static class StringExtensions
    {
        public static string TrimEnd(this string input, string suffixToRemove, StringComparison comparisonType)
        {
            if (input == null)
            {
                throw new ArgumentNullException(nameof(input));
            }

            if (suffixToRemove == null)
            {
                throw new ArgumentNullException(nameof(suffixToRemove));
            }

            return input.EndsWith(suffixToRemove, comparisonType) ? input.Substring(0, input.Length - suffixToRemove.Length) : input;
        }

        public static bool IsNullOrEmpty(this string text) => string.IsNullOrEmpty(text);
    }
}