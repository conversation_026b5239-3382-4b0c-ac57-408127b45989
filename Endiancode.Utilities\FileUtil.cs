﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="FileUtil.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Endiancode.Utilities
{
    using System;
    using System.IO;
    using System.Threading.Tasks;

    public static class FileUtil
    {
        public static async Task CopyFileAsync(string sourcePath, string destinationPath)
        {
            using (Stream source = File.Open(sourcePath, FileMode.Open))
            using (Stream destination = File.Create(destinationPath))
            {
                await source.CopyToAsync(destination);
            }
        }

        public static async Task<string> FileToBase64(string path)
        {
            if (path == null)
            {
                throw new ArgumentNullException(nameof(path));
            }

            byte[] result;
            using (FileStream fileStream = File.Open(path, FileMode.Open))
            {
                result = new byte[fileStream.Length];
                await fileStream.ReadAsync(result, 0, (int)fileStream.Length);
            }

            return Convert.ToBase64String(result);
        }
    }
}