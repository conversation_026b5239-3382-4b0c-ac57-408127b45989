﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IAppointmentService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Abstract
{
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.Collections.Generic;

    public abstract class AbstractDevice : IDevice
    {
        public Guid OrderGuid { get; set; }

        public Guid DeviceGuid { get; set; }

        public Guid DeviceCatalogGuid { get; set; }

        public string DeviceNumber { get; set; }

        public string DeviceLabel { get; set; }

        public string DeviceDescription { get; set; }

        public string DeviceNote {get; set;}

        public DateTime DeviceInstallationDate { get; set; }

        public DateTime? DeviceDeinstallationDate { get; set; }

        public DateTime? DeviceCalibrationDate { get; set; }

        public Guid? AmwInfoKeyGuid { get; set; }

        public Room DeviceRoom { get; set; }

        public string DeviceOngoingNumber { get; set; }

        public bool DeviceIsLeased { get; set; }

        public bool IsCreatedByApp { get; set; }

        public IList<DeviceConsumption> DeviceConsumptionState { get; set; }

        public DeviceOrderState DeviceOrderState { get; set; }

        public DeviceUiState DeviceUiState { get; set; }

        public List<DeviceAdditionalArticle> DeviceAdditionalArticles { get; set; }

        public IList<Photo> Photos { get; set; }

        public bool IsDeviceMaintained { get; set; }
    }
}
