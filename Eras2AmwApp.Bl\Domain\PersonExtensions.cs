﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="PersonExtensions.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Domain
{
    using System;
    using System.Linq;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;

    public static class PersonExtensions
    {
        private const string Hausmeister = "Hausmeister";

        public static bool IsHausmeister(this Person person)
        {
            if (person == null)
            {
                throw new ArgumentNullException(nameof(person));
            }

            return person.PersonAbrechnungseinheiten.Any(x => x.BusinessPosition == Hausmeister);
        }

        public static bool HasTelefon(this Person person)
        {
            if (person == null)
            {
                throw new ArgumentNullException(nameof(person));
            }

            return person.PersonCommunications.Any(x => x.CommunicationFeature.Kind == CommunicationKind.Telefon);
        }

        public static CommunicationFeature GetTelefon(this Person person)
        {
            if (person == null)
            {
                throw new ArgumentNullException(nameof(person));
            }

            return person.PersonCommunications.Where(x => x.CommunicationFeature.Kind == CommunicationKind.Telefon).Select(x => x.CommunicationFeature).FirstOrDefault();
        }

        public static void SetTelefon(this Person person, CommunicationFeature communicationFeature)
        {
            if (person == null)
            {
                throw new ArgumentNullException(nameof(person));
            }

            if (communicationFeature == null)
            {
                throw new ArgumentNullException(nameof(communicationFeature));
            }

            // context , attach, save
        }
    }
}