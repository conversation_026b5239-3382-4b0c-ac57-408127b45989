﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceFactory.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Factory
{
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.Linq;

    public static class DeviceFactory
    {
        public static IDevice Create(Device device, DeviceOrderState orderState)
        {
            if (device == null)
            {
                throw new ArgumentNullException(nameof(device));
            }

            DeviceClass deviceKind = GetDeviceKind(device);

            switch (deviceKind)
            {
                case DeviceClass.RM:
                    return CreateRauchmelder(device, orderState);
                case DeviceClass.KWZ:
                    return CreateWatermeter(device, orderState);
                case DeviceClass.WWZ:
                    return CreateWatermeter(device, orderState);
                case DeviceClass.WMZ:
                    return CreateWatermeter(device, orderState);
                case DeviceClass.HKV:
                    return CreateWatermeter(device, orderState);
                case DeviceClass.SZ:
                    return CreateWatermeter(device, orderState);
                default:
                    return CreateRauchmelder(device, orderState);
            }
        }

        private static IRauchmelder CreateRauchmelder(Device device, DeviceOrderState orderState)
        {
            DeviceCatalog deviceCatalog = device.DeviceCatalog;

            IRauchmelder rauchmelder = new Rauchmelder
            {
                OrderGuid = orderState.OrderGuid,
                DeviceGuid = device.Guid,
                DeviceCatalogGuid = device.DeviceCatalogGuid,
                DeviceInstallationDate = device.InstallationDate,
                DeviceCalibrationDate = device.CalibrationDate,
                DeviceDeinstallationDate = device.DeinstallationDate,
                DeviceDescription = device.Description,
                DeviceLabel = deviceCatalog.ArticleName,
                DeviceNote = device.Note,
                DeviceNumber = device.Number,
                DeviceOngoingNumber = device.OngoingNumber,
                DeviceRoom = device.Room,
                IsDeviceMaintained = device.IsMaintained,
                DeviceIsLeased = device.IsLeased,
                DeviceOrderState = orderState,
                DeviceConsumptionState = device.DeviceConsumptions,
                AmwInfoKeyGuid = orderState.AmwInfoKeyGuid,
                Photos = device.Photos,
                DeviceUiState = new DeviceUiState()
                { 
                    DeviceOrderState = orderState,
                    IsMaintained = device.IsMaintained
                },
                DeviceAdditionalArticles = device.DeviceAdditionalArticles.ToList(),
                IsCreatedByApp = device.IsCreatedByApp
            };

            return rauchmelder;
        }

        private static IWatermeter CreateWatermeter(Device device, DeviceOrderState orderState)
        {
            DeviceCatalog deviceCatalog = device.DeviceCatalog;

            IWatermeter watermeter = new Watermeter
            {
                OrderGuid = orderState.OrderGuid,
                DeviceGuid = device.Guid,
                DeviceCatalogGuid = device.DeviceCatalogGuid,
                DeviceInstallationDate = device.InstallationDate,
                DeviceCalibrationDate = device.CalibrationDate,
                DeviceDeinstallationDate = device.DeinstallationDate,
                DeviceDescription = device.Description,
                DeviceLabel = deviceCatalog.ArticleName,
                DeviceNote = device.Note,
                DeviceNumber = device.Number,
                DeviceOngoingNumber = device.OngoingNumber,
                DeviceRoom = device.Room,
                DeviceIsLeased = device.IsLeased,
                DeviceOrderState = orderState,
                DeviceConsumptionState = device.DeviceConsumptions,
                AmwInfoKeyGuid = orderState.AmwInfoKeyGuid,
                Photos = device.Photos,
                IsCreatedByApp = device.IsCreatedByApp,
                DeviceAdditionalArticles = device.DeviceAdditionalArticles.ToList(),
                DeviceUiState = new DeviceUiState()
                {
                    DeviceOrderState = orderState,
                    IsMaintained = device.IsMaintained
                },
                IsDeviceMaintained = device.IsMaintained
            };

            return watermeter;
        }

        private static DeviceClass GetDeviceKind(Device device)
        {
            return device.DeviceCatalog.DeviceKind.Class;
        }
    }
}
