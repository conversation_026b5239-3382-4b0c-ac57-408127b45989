﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IAbrechnungseinheitService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Interfaces
{
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;

    public interface IAbrechnungseinheitService
    {
        PersonAbrechnungseinheit GetAppointmentHausmeister(Abrechnungseinheit abrechnungseinheit);

        Abrechnungseinheit GetAbrechnungseinheit(Guid abrechnungsEinheitGuid);

        PersonAbrechnungseinheit GetPersonAbrechnungseinheit(Guid personAbrechnungseinheitGuid);

        void UpdateAbrechnungseinheit(Abrechnungseinheit abrechnungseinheit);

        void UpdatePersonAbrechnungseinheit(PersonAbrechnungseinheit personAbrechnungseinheit);

        void AddPersonAbrechnungseinheit(PersonAbrechnungseinheit personAbrechnungseinheit);
    }
}
