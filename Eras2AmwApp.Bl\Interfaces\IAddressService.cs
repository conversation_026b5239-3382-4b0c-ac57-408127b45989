﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IAddressService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Interfaces
{
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;

    public interface IAddressService
    {
        void AddAddress(Address address);

        G<PERSON>itAddress(Address address);
    }
}
