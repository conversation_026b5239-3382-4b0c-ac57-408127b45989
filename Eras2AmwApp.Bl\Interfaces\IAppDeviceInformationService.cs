﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IAppointmentService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Interfaces
{
    using Eras2AmwApp.Domain.Eras2App.Database;

    public interface IAppDeviceInformationService
    {
        string DatabaseVersion { get; }

        string AppVersion { get; }

        Customer GetAppCustomer();

        User GetAppUser();

        Webservice GetAppWebservice();

        void UpdateUser(User user);
    }
}
