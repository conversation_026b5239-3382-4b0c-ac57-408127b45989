﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IAppointmentService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Interfaces
{
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.Collections.Generic;

    public interface IAppointmentService
    {
        Appointment GetAppointment(Guid appointmentGuid);

        Appointment GetAppointmentWithNutzeinheitAddresses(Guid appointmentGuid);

        Appointment GetAppointmentDateOrder(DateTime date, Order order);

        List<AppointmentTechnician> GetTechnitianAppointments(Guid technicianGuid);

        List<AppointmentTechnician> GetTechnitianAppointments();

        void SaveAppointmentNutzeinheit(AppointmentNutzeinheit appointmentNutzeinheit);
    }
}
