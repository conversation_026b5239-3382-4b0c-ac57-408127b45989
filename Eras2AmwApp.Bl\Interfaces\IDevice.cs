﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IAppointmentService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Interfaces
{
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.Collections.Generic;

    public interface IDevice
    {
        Guid OrderGuid { get; set; }

        Guid DeviceGuid { get; set; }

        Guid DeviceCatalogGuid { get; set; }

        string DeviceNumber { get; set; }

        string DeviceLabel { get; set; }

        string DeviceDescription { get; set; }

        string DeviceNote { get; set; }
        
        DateTime DeviceInstallationDate { get; set; }

        DateTime? DeviceDeinstallationDate { get; set; }

        DateTime? DeviceCalibrationDate { get; set; }

        Guid? AmwInfoKeyGuid { get; set; }

        Room DeviceRoom { get; set; }

        string DeviceOngoingNumber { get; set; }

        bool DeviceIsLeased { get; set; }

        bool IsCreatedByApp { get; set; }

        IList<DeviceConsumption> DeviceConsumptionState { get; set; }

        DeviceOrderState DeviceOrderState { get; set; }

        List<DeviceAdditionalArticle> DeviceAdditionalArticles { get; set;} 

        IList<Photo> Photos { get; set; }

        bool IsDeviceMaintained { get; set; }
    }
}
