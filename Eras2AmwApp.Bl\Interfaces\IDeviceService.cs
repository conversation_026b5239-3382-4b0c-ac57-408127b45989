﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IDeviceService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Interfaces
{
    using System;
    using System.Collections.Generic;
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Domain.Eras2Amw.Models;

    public interface IDeviceService
    {
        Device GetDevice(Guid deviceGuid);

        void UpdateDevice(Device device);

        void UpdateDeviceConsumption(DeviceConsumption deviceConsumption);

        void AddDevice(Device device);

        DeviceOrderState GetDeviceOrderState(Guid deviceGuid, Guid orderGuid);

        void UpdateDeviceOrderState(DeviceOrderState deviceOrderState);

        void AddDeviceOrderState(DeviceOrderState deviceOrderState);

        List<DeviceKind> GetDeviceKinds();

        List<DeviceCatalog> GetDeviceCatalogList(Guid deviceKindGuid);

        ReadingKind GetRauchmelderReadingKind();

        void UpdateDeviceAdditonalArticle(DeviceAdditionalArticle deviceAdditionalArticle);

        void SaveDeviceAdditonalArticle(DeviceAdditionalArticle deviceAdditionalArticle);

        void RemoveDeviceAdditionalArticle(List<DeviceAdditionalArticle> deviceAdditionalArticles);

        void DeleteDevice(IDevice device);

        string GetPlaceholderOngoingNumber(Guid nutzeinheitGuid);

        string GetPreviousCalibarationDate(Guid nutzeinheitGuid);
    }
}
