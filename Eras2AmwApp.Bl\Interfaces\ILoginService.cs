﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="ILoginService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Interfaces
{
    using System;
    using System.Threading.Tasks;

    using Eras2AmwApp.Domain.Eras2App.Database;

    public interface ILoginService
    {
        bool NetworkAccess { get; set; }

        bool ShouldSyncOrders { get; }

        bool IsUserTimestampValid(User user);

        Task<Guid> LoginAsync(string name, string password);

        Task<Guid> AdminTestLoginAsync(string name, string password);

        User GetAppUser();

        string GetLoginAppTitle();

        void DeleteUsers();

        void WipeDatabase();

        bool GetUserLiveSyncState();

        void UpdateUserLiveSyncState(bool liveSyncState);
    }
}
