﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="ILoginService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------


namespace Eras2AmwApp.BusinessLogic.Interfaces
{
    using Models;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.Collections.Generic;

    public interface INutzeinheitService
    {
        List<NutzeinheitOrderState> GetAllNutzeinheitOrderStates();

        NutzeinheitOrderState GetNutzeinheitOrderState(NutzeinheitOrder nutzeinheitOrder);

        NutzeinheitOrderState SetNutzeinheitOrderStateToUpdating(NutzeinheitOrderState nutzeinheitOrderState);

        Nutzeinheit GetNutzeinheit(Guid nutzeinheitGuid);

        void UpdateNutzeinheit(Nutzeinheit nutzeinheit);

        void SaveNutzeinheit(Nutzeinheit nutzeinheit);

        void DeleteNutzeinheit(Nutzeinheit nutzeinheit);

        void SaveNutzeinheitSignature(Signature signature);

        void UpdateNutzeinheitOrderState(NutzeinheitOrderState nutzeinheitOrderState);

        Nutzeinheit GetNutzeinheitWithDevice(Device device);

        bool DoesUnfinishNutzeinheitExist();

        bool IsNutzeinheitSigned(Nutzeinheit nutzeinheit, Guid orderGuid);

        List<DeviceOrderState> GetNutzeinheitDevicesOrderStates(Nutzeinheit nutzeinheit, Guid orderGuid);

        string GetPlaceholderNutzeinheitNumber(Guid abrechnungseinheitGuid);
    }
}
