﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="INutzerService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Interfaces
{
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;

    public interface INutzerService
    {
        <PERSON><PERSON><PERSON>er(Nutzeinheit nutzeinheit);

        <PERSON><PERSON><PERSON>(Guid nutzerGuid);

        Nutzer GetNutzerForDate(Nutzeinheit nutzeinheit, DateTime date);

        Nutzer GetLoadedNutzerForDate(Nutzeinheit nutzeinheit, DateTime date);

        int GetNutzerCountForNextAppointmentDate(DateTime nextAppointmentDate);

        string GetNutzerTitleAndSalutation(Nutzer nutzer);

        string GetNutzerName(Nutzer nutzer);

        string GetNutzerContactValue(Nutzeinheit nutzeinheit);

        string GetNutzerAddress(Nutzeinheit nutzeinheit);

        void UpdateNutzer(Nutzer nutzer);

        void AddNutzer(Nutzer nutzer);

        void AddNutzerPersonen(NutzerPersonen nutzerPersonen);

        void UpdateNutzerPersonen(NutzerPersonen nutzerPersonen);

        void DeleteNutzerPersonen(NutzerPersonen nutzerPersonen);

        void AddNutzerQuadratmeter(NutzerQuadratmeter nutzerQuadratmeter);

        void UpdateNutzerQuadratmeter(NutzerQuadratmeter nutzerQuadratmeter);

        void DeleteNutzerQuadratmeter(NutzerQuadratmeter nutzerQuadratmeter);

        void UpdateNutzerCommunication(NutzerCommunication nutzerCommunication);

        void SaveNutzerCommunication(NutzerCommunication nutzerCommunication);
    }
}
