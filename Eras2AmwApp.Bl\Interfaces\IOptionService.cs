﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IOptionService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Interfaces
{
    using System.Threading.Tasks;

    public interface IOptionService
    {
        Task SyncOrdersAsync();

        Task LocalBackup();

        Task RemoteBackup();

        void DeleteAmwDatabase();

        void FactoryReset();
    }
}