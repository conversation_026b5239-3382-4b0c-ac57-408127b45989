﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IOrderService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Interfaces
{
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;

    public interface IOrderService
    {
        Order GetOrder(Guid orderGuid);

        Order GetMainOrderFromBackup(Order backupOrder);

        void UpdateOrder(Order order);

        void UpdateOrderState(OrderState orderState);

        Order GetBackupOrderFromMain(Order mainOrder);
    }
}
