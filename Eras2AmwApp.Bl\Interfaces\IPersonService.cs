﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IPersonService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Interfaces
{
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.Collections.Generic;

    public interface IPersonService
    {
        List<Salutation> GetSalutations();

        List<Title> GetTitles();

        Person GetPerson(Guid personGuid);

        void UpdatePerson(Person person);

        string GetPersonMainCommunicationValue(List<NutzerCommunication> nutzerCommunications);

        string GetPersonMainCommunicationValue(List<PersonCommunication> personCommunications);

        Dictionary<CommunicationKind, string> GetPersonContacts(List<NutzerCommunication> nutzerCommunications);

        Dictionary<CommunicationKind, string> GetPersonContacts(List<PersonCommunication> personCommunications);

        void UpdatePersonCommunication(PersonCommunication personCommunication);

        void AddPersonCommunication(PersonCommunication personCommunication);

        CommunicationFeature GetCommunicationFeature(CommunicationKind kind);
        Title GetTitle(int id);
        Salutation GetSalutation(int id);
    }
}
