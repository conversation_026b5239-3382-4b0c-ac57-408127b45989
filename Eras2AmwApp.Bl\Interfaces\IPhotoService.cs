﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IPhotoService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using Eras2AmwApp.Domain.Eras2Amw.Models;
using System;

namespace Eras2AmwApp.BusinessLogic.Interfaces
{
    public interface IPhotoService
    {
        void SavePhotoToDb(Photo photo);

        void UpdatePhoto(Photo photo);

        string SaveNutzeinheitPhoto(string picturePath, Guid nutzeinheitGuid);

        string SaveDevicePhoto(string picturePath, Guid deviceGuid);

        void DeletePhoto(Photo photo);
    }
}
