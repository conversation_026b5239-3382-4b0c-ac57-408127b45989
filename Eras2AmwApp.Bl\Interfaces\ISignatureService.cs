﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="ISignatureService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Interfaces
{
    using System;
    using System.IO;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Models;

    public interface ISignatureService
    {
        void DeleteSignature(Signature signature);
        Signature GetSignatureForOrder(Guid nutzeinheitGuid, Guid orderGuid);
        void RemoveSignatureFromDevice(Signature signature);
        void SaveSignature(NutzeinheitOrder nutzeinheitOrder, Stream image);

        void UpdateSignature(Signature signature);
    }
}