﻿//  <copyright file="IStammdatenService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Interfaces
{
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.Collections.Generic;

    public interface IStammdatenService
    {
        List<AdditionalArticle> GetAdditionalArticles();

        List<AmwInfoKey> GetNutzeinheitInfoKeys();

        List<AmwInfoKey> GetRauchmelderInfoKey();

        List<AmwInfoKey> GetWasserzählerInfoKey();

        List<AmwInfoKey> GetHkvInfoKey();

        List<Room> GetRooms();
    }
}
