﻿namespace Eras2AmwApp.BusinessLogic.Ioc
{
    using Interfaces;
    using Ninject.Modules;
    using Services;
    
    public class NinjectModules : NinjectModule
    {
        public override void Load()
        {
            Bind<ITestService>().To<TestService>();
            Bind<IAppointmentService>().To<AppointmentService>();
            Bind<ILoginService>().To<LoginService>();
            Bind<IOrderService>().To<OrderService>();
            Bind<IAbrechnungseinheitService>().To<AbrechnungseinheitService>();
            Bind<INutzeinheitService>().To<NutzeinheitService>();
            Bind<IPersonService>().To<PersonService>();
            Bind<IAppDeviceInformationService>().To<AppDeviceInformationService>();
            Bind<IOptionService>().To<OptionService>();
            Bind<INutzerService>().To<NutzerService>();
            Bind<IStammdatenService>().To<StammdatenService>();
            Bind<IAddressService>().To<AddressService>();
            Bind<IDeviceService>().To<DeviceService>();
            Bind<ISignatureService>().To<SignatureService>();
            Bind<IPhotoService>().To<PhotoService>();
            Bind<IRegistrationService>().To<RegistrationService>();
        }
    }
}
