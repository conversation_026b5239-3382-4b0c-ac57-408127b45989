//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AbrechnungseinheitService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Services
{
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.Linq;
    using Microsoft.EntityFrameworkCore;
    using Eras2AmwApp.Database.Contexts;

    public class AbrechnungseinheitService : IAbrechnungseinheitService
    {
        private readonly IDbContextFactory contextFactory;

        private const string Hausmeister = "Hausmeister";

        public AbrechnungseinheitService(IDbContextFactory contextFactory)
        {
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
        }

        public PersonAbrechnungseinheit GetAppointmentHausmeister(Abrechnungseinheit abrechnungseinheit)
        {
            if (abrechnungseinheit == null)
            {
                throw new ArgumentNullException(nameof(abrechnungseinheit));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {   
                return context.PersonAbrechnungseinheiten
                .Include(x => x.Person)
                .ThenInclude(x => x.PersonCommunications)
                .ThenInclude(x => x.CommunicationFeature)

                .Include(x => x.Person)
                .ThenInclude(x => x.Salutation)
                .Where(x => x.AbrechnungseinheitGuid == abrechnungseinheit.Guid && x.BusinessPosition == Hausmeister).SingleOrDefault();
            }
        }

        public Abrechnungseinheit GetAbrechnungseinheit(Guid abrechnungsEinheitGuid)
        {
            // Guid is a value type and cannot be null

            using (var context = contextFactory.CreateAmw())
            {
                return context.Abrechnungseinheiten
                    .Include(x => x.PersonAbrechnungseinheiten)
                    .ThenInclude(PersonAbrechnungseinheiten => PersonAbrechnungseinheiten.Person)
                    .ThenInclude(Person => Person.Salutation)

                    .Include(x => x.PersonAbrechnungseinheiten)
                    .ThenInclude(PersonAbrechnungseinheiten => PersonAbrechnungseinheiten.Person)
                    .ThenInclude(Person => Person.Title)

                    .Include(x => x.PersonAbrechnungseinheiten)
                    .ThenInclude(PersonAbrechnungseinheiten => PersonAbrechnungseinheiten.Person)
                    .ThenInclude(Person => Person.PersonCommunications)
                    .ThenInclude(PersonCommunications => PersonCommunications.CommunicationFeature)


                    .Single(x => x.Guid == abrechnungsEinheitGuid);
            }
        }

        public PersonAbrechnungseinheit GetPersonAbrechnungseinheit(Guid personAbrechnungseinheitGuid)
        {
            // Guid is a value type and cannot be null

            using (var context = contextFactory.CreateAmw())
            {
                return context.PersonAbrechnungseinheiten
                    .Include(x => x.Person)
                    .ThenInclude(Person => Person.PersonCommunications)
                    .ThenInclude(PersonCommunications => PersonCommunications.CommunicationFeature)

                    .Include(x => x.Person)
                    .ThenInclude(Person => Person.Salutation)

                    .Include(x => x.Person)
                    .ThenInclude(Person => Person.Title)

                    .Where(x => x.Guid == personAbrechnungseinheitGuid).SingleOrDefault();
            }
        }

        public void UpdateAbrechnungseinheit(Abrechnungseinheit abrechnungseinheit)
        {
            if (abrechnungseinheit == null)
            {
                throw new ArgumentNullException(nameof(abrechnungseinheit));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(abrechnungseinheit);
                context.Entry(abrechnungseinheit).State = EntityState.Modified;

                context.SaveChanges();
            }
        }

        public void UpdatePersonAbrechnungseinheit(PersonAbrechnungseinheit personAbrechnungseinheit)
        {
            if (personAbrechnungseinheit == null)
            {
                throw new ArgumentNullException(nameof(personAbrechnungseinheit));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                
                context.Attach(personAbrechnungseinheit);
                context.Entry(personAbrechnungseinheit).State = EntityState.Modified;
                context.SaveChanges();
            }
        }

        public void AddPersonAbrechnungseinheit(PersonAbrechnungseinheit personAbrechnungseinheit)
        {
            if (personAbrechnungseinheit == null)
            {
                throw new ArgumentNullException(nameof(personAbrechnungseinheit));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.PersonAbrechnungseinheiten.Add(personAbrechnungseinheit);
                context.SaveChanges();
            }
        }
    }
}

