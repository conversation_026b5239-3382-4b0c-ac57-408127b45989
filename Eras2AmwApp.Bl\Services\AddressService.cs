﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AddressService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Services
{
    using Interfaces;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using Eras2AmwApp.Database.Contexts;

    public class AddressService : IAddressService
    {
        private readonly IDbContextFactory contextFactory;

        public AddressService(IDbContextFactory contextFactory)
        {
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
        }

        public void AddAddress(Address address)
        {
            if (address == null)
            {
                throw new ArgumentNullException(nameof(address));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Addresses.Add(address);
                context.SaveChanges();
            }
        }

        public Guid AddNutzeinheitAddress(Address address)
        {
            if (address == null)
            {
                throw new ArgumentNullException(nameof(address));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Addresses.Add(address);
                context.SaveChanges();
                return address.Guid;
            }
        }
    }
}
