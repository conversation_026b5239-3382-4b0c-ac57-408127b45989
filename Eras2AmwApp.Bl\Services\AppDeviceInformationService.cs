﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AppDeviceInformationService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Services
{
    using System;
    using System.Linq;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2App.Database;
    using Microsoft.EntityFrameworkCore;

    public class AppDeviceInformationService : IAppDeviceInformationService
    {
        private readonly IDbContextFactory contextFactory;
        private readonly IAppSettings appSettings;

        public AppDeviceInformationService(IDbContextFactory contextFactory, IAppSettings appSettings)
        {
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
            this.appSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
        }

        public string DatabaseVersion
        {
            get
            {
                using (Eras2AmwContext context = contextFactory.CreateAmw())
                {
                    return context.Database.GetAppliedMigrations().Last();
                }
            }
        }

        public Customer GetAppCustomer()
        {
            using (Eras2AppContext context = contextFactory.CreateApp())
            {
                return context.Customers.FirstOrDefault();
            }
        }

        public User GetAppUser()
        {
            using (Eras2AppContext context = contextFactory.CreateApp())
            {
                return context.Users.Single();
            }
        }

        public Webservice GetAppWebservice()
        {
            using (Eras2AppContext context = contextFactory.CreateApp())
            {
                return context.Webservices.Single();
            }
        }

        public void UpdateUser(User user)
        {
            if (user == null)
            {
                throw new ArgumentNullException(nameof(user));
            }

            using(Eras2AppContext context = contextFactory.CreateApp())
            {
                context.Attach(user);
                context.Entry(user).State = EntityState.Modified;
                context.SaveChanges();
            }
        }

        public string AppVersion
        {
            get
            {
                if(appSettings.IsStandaloneApp)
                {
                    return "ERAS2 RMW";
                }
                else
                {
                    return "ERAS2 AMW";
                }
            }
        }
    }
}
