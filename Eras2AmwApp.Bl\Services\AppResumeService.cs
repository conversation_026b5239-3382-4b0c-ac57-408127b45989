﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AppResumeService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Services
{
    using System.Linq;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Common.Ioc;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2App.Database;

    public class AppResumeService : IAppResumeService
    {
        private readonly IDbContextFactory contextFactory;

        public AppResumeService()
        {
             contextFactory = NinjectKernel.Get<IDbContextFactory>();
        }

        public bool IsQrCodeScanMandatory
        {
            get
            {
                using (Eras2AppContext context = contextFactory.CreateApp())
                {
                    return !context.Customers.Any();
                }
            }
        }

        public bool IsAppLoginMandatory
        {
            get
            {
                var loginService = NinjectKernel.Get<ILoginService>();

                using (Eras2AppContext context = contextFactory.CreateApp())
                {
                    User user = context.Users.SingleOrDefault();

                    return user == null || !loginService.IsUserTimestampValid(user);
                }
            }
        }
    }
}