//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------


namespace Eras2AmwApp.BusinessLogic.Services
{
    using Interfaces;
    using Models;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.Linq;
    using Eras2AmwApp.Domain.Eras2Amw.Extensions;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Microsoft.EntityFrameworkCore;
    using Eras2AmwApp.Database.Contexts;
    using System.Collections.Generic;

    public class NutzeinheitService : INutzeinheitService
    {
        private readonly IDbContextFactory contextFactory;

        public NutzeinheitService(IDbContextFactory contextFactory)
        {
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
        }

        public List<NutzeinheitOrderState> GetAllNutzeinheitOrderStates()
        {
            using (var context = contextFactory.CreateAmw())
            {
                return context.NutzeinheitOrderStates.ToList();
            }
        }

        public bool DoesUnfinishNutzeinheitExist()
        {
            bool isAnyNeUnfinished = false;

            using (var context = contextFactory.CreateAmw())
            {
                List<NutzeinheitOrderState> nutzeinheitenInProgress = GetNutzeinheitInProgress(context).ToList();

                foreach (NutzeinheitOrderState nutzeinheitOrderState in nutzeinheitenInProgress)
                {
                    Nutzeinheit nutzeinheit = context.Nutzeinheiten.Include(x => x.OrderStates)
                                                                    .Include(x => x.Devices)
                                                                    .ThenInclude(Devices => Devices.OrderStates)
                                                                    .Where(x => x.Guid == nutzeinheitOrderState.NutzeinheitGuid).SingleOrDefault();

                    IEnumerable<DeviceOrderState> NutzeinheitDevices = nutzeinheit.Devices.SelectMany(x => x.OrderStates).Where(x => x.OrderGuid == nutzeinheitOrderState.OrderGuid);

                    IEnumerable<DeviceOrderState> deviceOrderStates = NutzeinheitDevices.Where(x => x.IsClosed());

                    if (IsNutzeinheitUnfinished(deviceOrderStates.Count(), NutzeinheitDevices.Count()))
                    {
                        isAnyNeUnfinished = true;
                    } 
                }
                return isAnyNeUnfinished;
            }
        }

        public NutzeinheitOrderState GetNutzeinheitOrderState(NutzeinheitOrder nutzeinheitOrder)
        {
            using (var context = contextFactory.CreateAmw())
            {
                return context.NutzeinheitOrderStates
                    .Include(x => x.Order)
                    .ThenInclude(x => x.OrderState)
                    .Single(x => x.NutzeinheitGuid == nutzeinheitOrder.NutzeinheitGuid && x.OrderGuid == nutzeinheitOrder.OrderGuid);
            }
        }

        public NutzeinheitOrderState SetNutzeinheitOrderStateToUpdating(NutzeinheitOrderState nutzeinheitOrderState)
        {
            if (nutzeinheitOrderState == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheitOrderState));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(nutzeinheitOrderState);
                
                nutzeinheitOrderState.ProcessState = ProcessState.Updating;

                context.SaveChanges();
            }

            return nutzeinheitOrderState;
        }
 
        public Nutzeinheit GetNutzeinheit(Guid nutzeinheitGuid)
        {
            // Guid is a value type and cannot be null

            using (var context = contextFactory.CreateAmw())
            {
                return context.Nutzeinheiten
                    .Include(x => x.Signatures)

                    .Include(x => x.Address)

                    .Include(x => x.OrderStates)

                    .Include(x => x.Photos)

                    .Include(x =>x.Devices)
                    .ThenInclude(Devices => Devices.OrderStates)
                    .ThenInclude(OrderStates => OrderStates.AmwInfoKey)

                    .Include(x => x.Devices)
                    .ThenInclude(Devices => Devices.DeviceCatalog)
                    .ThenInclude(DeviceCatalog => DeviceCatalog.DeviceKind)

                    .Include(x => x.Devices)
                    .ThenInclude(Devices => Devices.DeviceAdditionalArticles)
                    .ThenInclude(DeviceAdditionalArticles => DeviceAdditionalArticles.AdditionalArticle)

                    .Include(x => x.Devices)
                    .ThenInclude(Devices => Devices.Room)

                    .Include(x => x.Devices)
                    .ThenInclude(Devices => Devices.Photos)

                    .Include(x => x.Nutzer)
                    .ThenInclude(Nutzer => Nutzer.Salutation)

                    .Include(x => x.Nutzer)
                    .ThenInclude(Nutzer => Nutzer.Title)

                    .Include(x => x.Nutzer)
                    .ThenInclude(Nutzer => Nutzer.NutzerCommunications)
                    .ThenInclude(NutzerCommunications => NutzerCommunications.CommunicationFeature)

                    .Where(x => x.Guid == nutzeinheitGuid).SingleOrDefault();
            }
        }

        public Nutzeinheit GetNutzeinheitWithDevice(Device device)
        {
            if (device is null)
            {
                throw new ArgumentNullException(nameof(device));
            }

            using (var context = contextFactory.CreateAmw())
            {
                return context.Nutzeinheiten.Where(x => x.Devices.Contains(device)).SingleOrDefault();
            }
        }

        public void UpdateNutzeinheit(Nutzeinheit nutzeinheit)
        {
            if (nutzeinheit == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheit));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(nutzeinheit);
                context.Entry(nutzeinheit).State = EntityState.Modified;
                context.SaveChanges();
            }
        }

        public void UpdateNutzeinheitOrderState(NutzeinheitOrderState nutzeinheitOrderState)
        {
            if (nutzeinheitOrderState == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheitOrderState));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(nutzeinheitOrderState);
                context.Entry(nutzeinheitOrderState).State = EntityState.Modified;
                context.SaveChanges();
            }
        }

        public void SaveNutzeinheit(Nutzeinheit nutzeinheit)
        {
            if (nutzeinheit == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheit));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Nutzeinheiten.Add(nutzeinheit);
                
                context.SaveChanges();
            }
        }

        public void DeleteNutzeinheit(Nutzeinheit nutzeinheit)
        {
            if (nutzeinheit == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheit));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Addresses.Remove(nutzeinheit.Address);
                context.Nutzeinheiten.Remove(nutzeinheit);
                context.SaveChanges();
            }
        }

        public void SaveNutzeinheitSignature(Signature signature)
        {
            if (signature is null)
            {
                throw new ArgumentNullException(nameof(signature));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Signatures.Add(signature);
                context.SaveChanges();
            }
        }

        private bool IsNutzeinheitUnfinished(int closedDeviceCounter, int numberOfNutzeinheitDevices)
        {
            return (closedDeviceCounter > 0) && (closedDeviceCounter < numberOfNutzeinheitDevices);
        }

        private IEnumerable<NutzeinheitOrderState> GetNutzeinheitInProgress(Eras2AmwContext context)
        {
            return context.NutzeinheitOrderStates.Where(x => x.ProcessState == ProcessState.InProgress);
        }

        public bool IsNutzeinheitSigned(Nutzeinheit nutzeinheit, Guid orderGuid)
        {
            Signature signature = nutzeinheit.Signatures.Where(x => x.OrderGuid == orderGuid).SingleOrDefault();
            if (signature != null)
            {
                return true;
            }
            return false;
        }

        public List<DeviceOrderState> GetNutzeinheitDevicesOrderStates(Nutzeinheit nutzeinheit, Guid orderGuid)
        {
            List<Device> allNutzeinheitDevices = nutzeinheit.Devices.ToList();
            List<DeviceOrderState> deviceOrderStates = new List<DeviceOrderState>();
            foreach (Device device in allNutzeinheitDevices)
            {
                var orderState = device.OrderStates.Where(x => x.OrderGuid == orderGuid).SingleOrDefault();
                if (orderState != null)
                {
                    deviceOrderStates.Add(orderState);
                }
            }
            return deviceOrderStates;
        }

        public string GetPlaceholderNutzeinheitNumber(Guid abrechnungseinheitGuid)
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                var nutzeinheitNumberList = context.Nutzeinheiten.Where(y => y.AbrechnungseinheitGuid == abrechnungseinheitGuid);
                int nextNutzeinheitNumber = 10;
                if (nutzeinheitNumberList.Any())
                {
                    int highestNutzeinheitNumber = nutzeinheitNumberList.Max(x => Convert.ToInt32(x.Number));
                    nextNutzeinheitNumber = (highestNutzeinheitNumber % 10 == 0) ? highestNutzeinheitNumber += 10 : (int)Math.Ceiling(highestNutzeinheitNumber / 10.0) * 10;
                }
                return nextNutzeinheitNumber.ToString();
            }
        }
    }
}

