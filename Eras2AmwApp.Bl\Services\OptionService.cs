﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="OptionService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Services
{
    using System;
    using System.IO;
    using System.Linq;
    using System.Threading.Tasks;
    using Endiancode.Utilities.Extensions;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2App.Database;
    using Eras2AmwApp.WebService.Interfaces;
    using Polly;
    using Polly.Timeout;

    public class OptionService : IOptionService
    {
        private readonly IAmwWebservice webservice;

        private readonly IDbContextFactory contextFactory;

        private readonly ILocalisationService localisationService;

        private readonly IAppSettings appSettings;

        private readonly ILoginService loginService;

        private readonly IBackupService backupService;

        public OptionService(
            IAmwWebservice webservice,
            IDbContextFactory contextFactory,
            ILocalisationService localisationService,
            IAppSettings appSettings,
            ILoginService loginService,
            IBackupService backupService
        )
        {
            this.webservice = webservice ?? throw new ArgumentNullException(nameof(webservice));
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
            this.localisationService = localisationService ?? throw new ArgumentNullException(nameof(localisationService));
            this.appSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
            this.loginService = loginService ?? throw new ArgumentNullException(nameof(loginService));
            this.backupService = backupService ?? throw new ArgumentNullException(nameof(backupService));
        }

        public async Task SyncOrdersAsync()
        {
            using (Eras2AppContext context = contextFactory.CreateApp())
            {
                User user = context.Users.Single();

                if (!loginService.IsUserTimestampValid(user))
                {
                    throw new UnauthorizedAccessException(localisationService.Get("UserLoginFailed3"));
                }

                AsyncTimeoutPolicy timeOutPolicy = Policy.TimeoutAsync(30, TimeoutStrategy.Pessimistic);

                Guid userGuid = await timeOutPolicy.ExecuteAsync(async () => await webservice.LoginAsync(user.Name, user.Password).ConfigureAwait(false));

                await backupService.DatabaseDackup();

                await webservice.SyncOrdersAsync(userGuid).ConfigureAwait(false);

                user.LastWebserviceSyncDate = DateTime.Now;

                context.SaveChanges();

                await webservice.LogoutAsync().ConfigureAwait(false);
            }
        }
        
        public async Task LocalBackup()
        {
            await backupService.FullBackup();
        }

        public async Task RemoteBackup()
        {
            await SyncFullAmwAppDatabaseAsync().ConfigureAwait(false);
        }

        public void DeleteAmwDatabase()
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.RecreateDatabase();
            }
        }

        public void FactoryReset()
        {
            DeleteAmwDatabase();

            using (Eras2AppContext context = contextFactory.CreateApp())
            {
                context.RecreateDatabase();
            }

            new DirectoryInfo(appSettings.LogFilesDirectory).DeleteDirectoryContent();
            new DirectoryInfo(appSettings.WebserviceDownloadDirectory).DeleteDirectoryContent();
            new DirectoryInfo(appSettings.WebserviceUploadDirectory).DeleteDirectoryContent();
            new DirectoryInfo(appSettings.BackupDirectory).DeleteDirectoryContent();
            new DirectoryInfo(appSettings.PicturesDirectory).DeleteDirectoryContent();
            new DirectoryInfo(appSettings.SignaturesDirectory).DeleteDirectoryContent();
        }

        private async Task SyncFullAmwAppDatabaseAsync()
        {
            using (Eras2AppContext context = contextFactory.CreateApp())
            {
                User user = context.Users.Single();

                if (!loginService.IsUserTimestampValid(user))
                {
                    throw new UnauthorizedAccessException(localisationService.Get("UserLoginFailed3"));
                }

                AsyncTimeoutPolicy timeOutPolicy = Policy.TimeoutAsync(30, TimeoutStrategy.Pessimistic);

                Guid userGuid = await timeOutPolicy.ExecuteAsync(async () => await webservice.LoginAsync(user.Name, user.Password).ConfigureAwait(false));

                string databasePath = await backupService.FullBackup().ConfigureAwait(false);

                await webservice.SyncFullAmwAppDatabaseAsync(databasePath).ConfigureAwait(false); 

                await webservice.LogoutAsync().ConfigureAwait(false);
            }
        }
    }
}