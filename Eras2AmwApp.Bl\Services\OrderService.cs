//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="OrderService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Services
{
    using Interfaces;
    using Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using System;
    using System.Linq;

    public class OrderService : IOrderService
    {
        private readonly IDbContextFactory contextFactory;

        public OrderService(IDbContextFactory contextFactory)
        {
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
        }

        public Order GetOrder(Guid orderGuid)
        {
            // Guid is a value type and cannot be null

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Orders
                    .Include(x => x.OrderState)
                    .Where(x => x.Guid == orderGuid).SingleOrDefault();
            }
        }

        public Order GetMainOrderFromBackup(Order backupOrder)
        {
            if (backupOrder == null)
            {
                throw new ArgumentNullException(nameof(backupOrder));
            }

            string orderNumber = backupOrder.Number.Substring(0,backupOrder.Number.Length-4);

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Orders.Where(x => x.Number == orderNumber).SingleOrDefault();
            }
        }

        public Order GetBackupOrderFromMain(Order mainOrder)
        {
            if (mainOrder == null)
            {
                throw new ArgumentNullException(nameof(mainOrder));
            }

            string orderNumber = mainOrder.Number + "_#x#";

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Orders.Where(x => x.Number == orderNumber).SingleOrDefault();
            }
        }

        public void UpdateOrder(Order order)
        {
            if (order == null)
            {
                throw new ArgumentNullException(nameof(order));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(order);
                context.Entry(order).State = EntityState.Modified;
                context.SaveChanges();
            }
        }

        public void UpdateOrderState(OrderState orderState)
        {
            if (orderState == null)
            {
                throw new ArgumentNullException(nameof(orderState));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(orderState);
                context.Entry(orderState).State = EntityState.Modified;
                context.SaveChanges();
            }
        }
    }
}

