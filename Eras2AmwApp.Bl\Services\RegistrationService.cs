﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="PersonService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Services
{
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2App.Database;
    using System;
    using System.Linq;

    public class RegistrationService : IRegistrationService
    {
        private readonly IDbContextFactory contextFactory;
        private readonly IAppSettings appSettings;

        public RegistrationService(IDbContextFactory contextFactory, IAppSettings appSettings)
        {
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
            this.appSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
        }

        public void UpdateWebserviceCustomer(WebserviceCustomerConfig config)
        {
            using (var context = contextFactory.CreateApp())
            {
                Webservice webservice = context.Webservices.SingleOrDefault();
                if (webservice == null)
                {
                    webservice = new Webservice()
                    {
                       Customer = new Customer()
                    };
                }
                webservice.Url = config.Url;
                webservice.Customer.Name = config.CustomerName;

                context.Update(webservice);
                context.SaveChanges();
            }
        }

        public string GetAppTitle()
        {
            string apptitle = "ERAS2 AMW";

            if (appSettings.IsStandaloneApp)
            {
                apptitle = "ERAS2 RMW";
            }

            return apptitle;
        }
    }
}
