//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="SignatureService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Services
{
    using System;
    using System.IO;
    using System.Linq;
    using Common.Interfaces;
    using Database.Interfaces;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Interfaces;
    using Microsoft.EntityFrameworkCore;
    using Models;

    public class SignatureService : ISignatureService
    {
        private const string SignatureFileExtension = "png";

        private readonly IDbContextFactory contextFactory;
        private readonly IAppSettings appSettings;

        public SignatureService(IDbContextFactory contextFactory, IAppSettings appSettings)
        {
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
            this.appSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
        }

        public Signature GetSignatureForOrder(Guid nutzeinheitGuid, Guid orderGuid)
        {
            // Guid is a value type and cannot be null, so we don't need null checks here

            using(var context = contextFactory.CreateAmw())
            {
                return context.Signatures.Where(x => x.NutzeinheitGuid == nutzeinheitGuid && x.OrderGuid == orderGuid).SingleOrDefault();
            }
        }

        public void SaveSignature(NutzeinheitOrder nutzeinheitOrder, Stream stream)
        {
            Signature signature = AddSignature(nutzeinheitOrder);
            SaveSignature(signature.Path, stream);
        }

        private void SaveSignature(string path, Stream stream)
        {
            if (path == null)
            {
                throw new ArgumentNullException(nameof(path));
            }

            if (stream == null)
            {
                throw new ArgumentNullException(nameof(stream));
            }

            using (var fielstream = new FileStream(path, FileMode.Create, FileAccess.Write))
            {
                stream.CopyTo(fielstream);
            }
        }

        private Signature AddSignature(NutzeinheitOrder nutzeinheitOrder)
        {
            using (var context = contextFactory.CreateAmw())
            {
                var signatureGuid = Guid.NewGuid();
                var path = Path.Combine(appSettings.SignaturesDirectory, $"{signatureGuid}.{SignatureFileExtension}");

                var signature = new Signature
                {
                    Guid = signatureGuid,
                    NutzeinheitGuid = nutzeinheitOrder.NutzeinheitGuid,
                    OrderGuid = nutzeinheitOrder.OrderGuid,
                    CreatedByApp = true,
                    RecordedDate = DateTime.Now,
                    Kind = SignatureKind.Nutzer,
                    Path = path
                };

                context.Signatures.Add(signature);
                context.SaveChanges();

                return signature;
            }
        }

        public void DeleteSignature(Signature signature)
        {
            if (signature is null)
            {
                throw new ArgumentNullException(nameof(signature));
            }

            using (var context = contextFactory.CreateAmw())
            {
                context.Signatures.Remove(signature);
                context.SaveChanges();
            }
        }

        public void RemoveSignatureFromDevice(Signature signature)
        {
            if (signature is null)
            {
                throw new ArgumentNullException(nameof(signature));
            }

            File.Delete(signature.Path);
        }

        public void UpdateSignature(Signature signature)
        {
            if (signature == null)
            {
                throw new ArgumentNullException(nameof(signature));
            }

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(signature);
                context.Entry(signature).State = EntityState.Modified;
                context.SaveChanges();
            }
        }
    }
}
