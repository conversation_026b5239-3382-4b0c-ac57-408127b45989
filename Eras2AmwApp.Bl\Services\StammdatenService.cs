﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="StammdatenService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Services
{
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.Collections.Generic;
    using System.Linq;

    public class StammdatenService : IStammdatenService
    {
        private readonly IDbContextFactory contextFactory;

        public StammdatenService(IDbContextFactory contextFactory)
        {
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
        }

        public List<AmwInfoKey> GetRauchmelderInfoKey()
        {
            using (var context = contextFactory.CreateAmw())
            {
                return context.AmwInfoKeys.Where(x => x.Key >= 400 && x.Key < 500).ToList();
            }
        }

        public List<AmwInfoKey> GetWasserzählerInfoKey()
        {
            using (var context = contextFactory.CreateAmw())
            {
                return context.AmwInfoKeys.Where(x => x.Key >= 500 && x.Key < 600).ToList();
            }
        }

        public List<AmwInfoKey> GetHkvInfoKey()
        {
            using (var context = contextFactory.CreateAmw())
            {
                return context.AmwInfoKeys.Where(x => x.Key >= 600 && x.Key < 700).ToList();
            }
        }

        public List<AmwInfoKey> GetNutzeinheitInfoKeys()
        {
            using (var context = contextFactory.CreateAmw())
            {
                return context.AmwInfoKeys.Where(x => x.Key >= 200 && x.Key < 300).ToList();
            }
        }

        public List<AdditionalArticle> GetAdditionalArticles()
        {
            using (var context = contextFactory.CreateAmw())
            {
                return context.AdditionalArticles.ToList();
            }
        }

        public List<Room> GetRooms()
        {
            using(var context = contextFactory.CreateAmw())
            {
                return context.Rooms.ToList();
            }
        }

    }
}
