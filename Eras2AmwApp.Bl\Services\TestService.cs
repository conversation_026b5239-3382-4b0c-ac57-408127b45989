﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Test.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.BusinessLogic.Services
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Threading.Tasks;
    using Common.Interfaces;
    using Database.Contexts;
    using Database.Interfaces;
    using Eras2Amw.Service.Server.ServiceModel.Common;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2App.Database;
    using Interfaces;
    using Microsoft.EntityFrameworkCore;
    using Serilog;
    using ServiceStack;
    using WebService.EventArgs;
    using WebService.Interfaces;
    using Dal = Eras2AmwApp.Domain.Eras2Amw.Models;

    public class TestService : ITestService
    {
        //private readonly IServiceLocator serviceLocator;

        //private readonly ILoginService loginService;

        private readonly IAmwWebservice webservice;

        //private readonly IDomainLoader domainLoader;

        private readonly IDbContextFactory contextFactory;

        private readonly ILogger logger;

        public TestService(IServiceLocator serviceLocator, IAmwWebservice webservice,  IDbContextFactory contextFactory)//, ILoginService loginService, IDomainLoader domainLoader,)
        {
            //this.serviceLocator = serviceLocator ?? throw new ArgumentNullException(nameof(serviceLocator));
            //this.loginService = loginService ?? throw new ArgumentNullException(nameof(loginService));
            this.webservice = webservice ?? throw new ArgumentNullException(nameof(webservice));
            //this.domainLoader = domainLoader;
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));

            logger = serviceLocator.Logger;
        }

        //public async Task TestMethodAsync()
        //{
        //    try
        //    {
        //        Guid userGuid = await loginService.LoginAsync("Björn", "TestTest129!");

        //        using (Eras2AmwContext context = contextFactory.CreateAmw())
        //        {
        //            await webservice.DownloadDevicePhotoAsync(userGuid, context.Photos.Find(Guid.Parse("a4e33ebc-e565-432d-9c8e-cc633bf14273")));

        //            await webservice.DownloadNutzeinheitPhotoAsync(userGuid, context.Photos.Find(Guid.Parse("93e33ebc-e565-432d-9c8e-cc633bf14273")));

        //            await webservice.DownloadNutzeinheitSignatureAsync(userGuid, context.Signatures.Find(Guid.Parse("4faed6d2-7643-4665-812e-f6e0230290a9")));
        //        }

        //        await webservice.LogoutAsync();
        //    }
        //    catch (WebException e)
        //    {
        //        logger.Error(e, "TestMethodAsync failed");
        //    }
        //    catch (WebServiceException e)
        //    {
        //        logger.Error(e, "TestMethodAsync failed");
        //    }
        //    catch (Exception e)
        //    {
        //        logger.Error(e, "TestMethodAsync failed");
        //    }
        //}

        public async Task TestMethod2Async()
        {
            try
            {
                Guid userGuid = await webservice.LoginAsync("Björn", "TestTest129!");

                using (Eras2AmwContext context = contextFactory.CreateAmw())
                {
                    var photo = new Dal.Photo()
                                    {
                                        Path = "/storage/emulated/0/Android/data/com.companyname.eras2amwapp/files/Pictures/Photos/Nutzeinheiten/31b088b1-ba32-4040-aada-f79b46ca51ae/EEEEEebc-e565-432d-9c8e-cc633bf14273.jpg",
                                        Name = "EEEEEebc-e565-432d-9c8e-cc633bf14273.jpg",
                                        Guid = Guid.Parse("EEEEEebc-e565-432d-9c8e-cc633bf14273"),
                                        NutzeinheitGuid = Guid.Parse("31b088b1-ba32-4040-aada-f79b46ca51ae"),
                                        CreatedByApp = true,
                                        RecordedDate = DateTime.UtcNow,
                                    };

                    context.Photos.Add(photo);
                    context.SaveChanges();
                }

                await webservice.LogoutAsync();
            }
            catch (WebException e)
            {    
                logger.Error(e,"TestMethodAsync failed");
            }
            catch (WebServiceException e)
            {
                logger.Error(e,"TestMethodAsync failed");
            }
            catch (Exception e)
            {
                logger.Error(e,"TestMethodAsync failed"); 
            }
        }

        //private void AddNutzeinheit()
        //{
            /*
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                Abrechnungseinheit abrechnungseinheit = context.Abrechnungseinheiten.Find(Guid.Parse("caaa472f-5ce8-4496-8d05-faf61b0a792c"));

                var nutzeinheit = new Nutzeinheit
                {
                    Abrechnungseinheit = abrechnungseinheit,
                    Address = new Address
                    {
                        City = "Neustadt",
                        Latitude = "123",
                        Longitude = "666",
                        Street = "NeuStraße",
                        StreetNumber = "123",
                        Zipcode = "12345",
                    },
                    Location = "Erdgeschoss",
                    Number = "Ne-New",
                    WalkSequence = "12",
                    Nutzer = new Nutzer
                    {
                        Kind = NutzerKind.Leerstand,
                        Name1 = "Leerstand", 
                        Salutation = context.Salutations.First(),
                    },
                    OrderStates = new List<NutzeinheitOrderState>
                    {
                        new NutzeinheitOrderState
                        {
                            ProcessState = ProcessState.Creating,
                            OrderGuid = Guid.Parse("fe34422a-e4b7-4565-a286-410ca79740bb"),
                            OrderKinds = new List<NutzeinheitOrderKind> {NutzeinheitOrderKind.Assembly},
                        }
                    }

                };

                context.Add(nutzeinheit);
                context.SaveChanges();
            }
            */
        //}
    }
}