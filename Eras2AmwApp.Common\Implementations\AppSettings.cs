﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AppSettings.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Common.Implementations
{
    using System;
    using System.IO;
    using Interfaces;

    public class AppSettings : IAppSettings
    {
        private const string DatabaseFolder = "Databases";

        private const string WebserviceFolder = "Webservice";

        private const string WebserviceUploadFolder = "Upload";

        private const string WebserviceDownloadFolder = "Download";

        private const string PicturesFolder = "Pictures";

        private const string SignatureFolder = "Signatures";

        private const string LogFilesFolder = "Logfiles";

        private const string BackupFolder = "Backups";

        private readonly IPlatformService platformService;

        public AppSettings(IPlatformService platformService)
        {
            this.platformService = platformService ?? throw new ArgumentNullException(nameof(platformService));
        }

        public virtual string RootDirectory => platformService.RootDirectory;

        public Stream GetAssetsTestStream() => platformService.GetAssetDatabaseStream();

        public string DatabaseDirectory => Path.Combine(RootDirectory, DatabaseFolder);

        public string DownloadDirectery => platformService.GetDownloadFolderPath;

        public string LogFilesDirectory => Path.Combine(RootDirectory, LogFilesFolder);

        public string LogFileName { get; } = "eras2_amw_app.log";

        public string BackupDirectory => platformService.ExistsSdCard ? Path.Combine(platformService.SdCardPath, BackupFolder) : Path.Combine(platformService.ExternalPath, BackupFolder);

        public int UserLoginValidLocalLoginDays { get; } = 7;

        public string WebserviceDirectory => Path.Combine(RootDirectory, WebserviceFolder);

        public string WebserviceUploadDirectory => Path.Combine(RootDirectory, WebserviceFolder, WebserviceUploadFolder);

        public string WebserviceDownloadDirectory => Path.Combine(RootDirectory, WebserviceFolder, WebserviceDownloadFolder);
       
        public virtual string PicturesDirectory => Path.Combine(RootDirectory, PicturesFolder);

        public virtual string SignaturesDirectory => Path.Combine(RootDirectory, SignatureFolder);

        public virtual string AndroidAppCenterKey => (!IsStandaloneApp) ? "1b2a7c14-e8f8-407c-85e5-a92da1d04552" : "b3cec778-c330-4a0b-8b2f-57961f6512fb";
        
        public virtual string IosAppCenterKey { get; } = string.Empty;

        public string SyncfusionLicenceKey { get; } = "Mzg0OTUyMEAzMjM5MmUzMDJlMzAzYjMzMzczYktvZVFBc2h0MzBnM0JJdnVWZWFKaTlCUU9YM2k0eWpqVkk5ZWRLUm5HNzA9";

        public string AzureBackupAccountName { get; } = string.Empty;

        public string AzureBackupAccountKey { get; } = string.Empty;

        public string AzureBackupShareName => "backups";

        public bool IsStandaloneApp
        {
            get
            {
#if STANDALONE_APP
                return true;
#else
                return false;
#endif
            }
        }

        public bool IsDevelopment
        {
            get
            {
#if DEVELOPMENT
                return true;
#else
                return false;
#endif
            }
        }

        public bool IsProductive => !IsDevelopment;
    }
}