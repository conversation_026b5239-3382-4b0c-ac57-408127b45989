﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="ServiceLocator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Common.Implementations
{
    using System;
    using System.Collections.Generic;
    using AutoMapper;
    using Interfaces;
    using Microsoft.Extensions.DependencyInjection;
    using Serilog;

    public class ServiceLocator : IServiceLocator
    {
        private readonly IServiceProvider _serviceProvider;

        public ServiceLocator(IAppSettings appSettings, ILogger logger, ILocalisationService localisationService, IMapper mapper, IResourceService resourceService, IServiceProvider serviceProvider)
        {
            ResourceService = resourceService ?? throw new ArgumentNullException(nameof(resourceService));
            AppSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
            Logger = logger ?? throw new ArgumentNullException(nameof(logger));
            LocalisationService = localisationService ?? throw new ArgumentNullException(nameof(localisationService));
            Mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }

        public IAppSettings AppSettings { get; }

        public ILogger Logger { get; }

        public ILocalisationService LocalisationService { get; }

        public IMapper Mapper { get; }

        public IResourceService ResourceService { get; }

        public T GetService<T>() where T : class
        {
            try
            {
                return _serviceProvider.GetService<T>() ??
                    throw new KeyNotFoundException($"Service of type {typeof(T).Name} is not registered");
            }
            catch (KeyNotFoundException ex)
            {
                throw new ApplicationException("The requested service is not registered", ex);
            }
        }
    }
}