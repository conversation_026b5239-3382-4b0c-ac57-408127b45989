﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="SimpleFileLogger.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Common.Implementations
{
    using System;
    using System.IO;
    using Interfaces;
    using Serilog;
    using Serilog.Exceptions;
    using Serilog.Formatting.Compact;

    public class SimpleFileLoggerFactory : ISimpleFileLoggerFactory
    {
        private readonly IAppSettings appSettings;
        
        private readonly IPlatformService platformService;

        private ILogger logger;

        public SimpleFileLoggerFactory(IAppSettings appSettings, IPlatformService platformService)
        {
            this.appSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
            this.platformService = platformService ?? throw new ArgumentNullException(nameof(platformService));
        }

        public ILogger Create() => logger ?? (appSettings.IsProductive ? logger = CreateProductiveLogger() : logger = CreateDevelopmentLogger());

        private ILogger CreateDevelopmentLogger() =>
            CreateBaseConfiguration()
                .MinimumLevel.Verbose()
                .WriteTo.Trace()
                .CreateLogger();

        private LoggerConfiguration CreateBaseConfiguration() =>
            new LoggerConfiguration()
                .Enrich.WithExceptionDetails()
                .WriteTo.File(new CompactJsonFormatter(), Path.Combine(appSettings.LogFilesDirectory, appSettings.LogFileName), rollingInterval: RollingInterval.Day)
                .Enrich.WithProperty("App Version", platformService.AppVersion)
                .Enrich.FromLogContext();
                
        private ILogger CreateProductiveLogger() =>
            CreateBaseConfiguration()
                .MinimumLevel.Debug()
                .CreateLogger();
    }
}