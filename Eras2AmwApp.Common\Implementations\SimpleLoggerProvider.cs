﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="SimpleLoggerProvider.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Common.Implementations
{
    using System;
    using System.Collections.Concurrent;

    using Microsoft.Extensions.Logging;

    public class SimpleLoggerProvider : ILoggerProvider
    {
        private readonly ConcurrentDictionary<string, ILogger> loggers = new ConcurrentDictionary<string, ILogger>();
        
        private Func<string, LogLevel, bool> filter;

        public SimpleLoggerProvider(Func<string, LogLevel, bool> filter)
        {
            this.filter = filter ?? throw new ArgumentNullException(nameof(filter));
        }
            
        public void Dispose() 
        {
        }

        public ILogger CreateLogger(string categoryName) => loggers.GetOrAdd(categoryName, CreateLoggerImplementation);

        private ILogger CreateLoggerImplementation(string name) => new SimpleLogger(name, filter);
    }
}