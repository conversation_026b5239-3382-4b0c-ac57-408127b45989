﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IBackupService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Common.Interfaces
{
    using System.Threading;
    using System.Threading.Tasks;

    public interface IBackupService
    {
        Task<string> FullBackup(CancellationToken cancellationToken = default(CancellationToken));

        Task DatabaseDackup(CancellationToken cancellationToken = default(CancellationToken));
    }
}