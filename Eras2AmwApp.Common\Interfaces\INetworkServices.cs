﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="INtpClient.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Common.Interfaces
{
    using System;

    public interface INetworkServices
    {
        TimeSpan MaxNetworkTimeDeviation { get; }

        DateTime GetNetworkTime();
    }
}