﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IPlatformService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using System.IO;

namespace Eras2AmwApp.Common.Interfaces
{
    public interface IPlatformService
    {
        string RootDirectory { get; }

        string AppVersion { get; }

        bool ExistsSdCard { get; }

        string ExternalPath { get; }

        string SdCardPath { get; }

        string GetDownloadFolderPath { get; }

        Stream GetAssetDatabaseStream();
    }
}