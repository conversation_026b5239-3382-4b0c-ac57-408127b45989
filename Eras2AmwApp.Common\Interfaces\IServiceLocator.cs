﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IServiceLocator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Common.Interfaces
{
    using AutoMapper;
    using Serilog;
    
    public interface IServiceLocator
    {
        IAppSettings AppSettings { get; }

        ILogger Logger { get; }

        ILocalisationService LocalisationService { get; }

        IResourceService ResourceService { get; }

        IMapper Mapper { get; }

        T GetService<T>() where T : class;
    }
}