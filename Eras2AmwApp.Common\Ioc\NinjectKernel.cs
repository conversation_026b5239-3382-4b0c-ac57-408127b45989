﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DependencyResolver.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Common.Ioc
{
    using System;
    using System.Reflection;
    using Ninject;

    public static class NinjectKernel
    {
        private static IKernel Kernel { get; set; }

        public static void Initialize()
        {
            var settings = new NinjectSettings
            {
                LoadExtensions = false,
                UseReflectionBasedInjection = true
            };

            Kernel = new StandardKernel(settings);

            try
            {
                Kernel.Load(AppDomain.CurrentDomain.GetAssemblies());
            }
            catch (ReflectionTypeLoadException ex)
            {
                foreach (var loaderException in ex.LoaderExceptions)
                {
                    Console.WriteLine(loaderException.Message);
                }
                throw;
            }
        }

        public static T Get<T>() where T : class => Kernel.Get<T>();
    }
}