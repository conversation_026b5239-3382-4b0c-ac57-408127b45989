﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Eras2AmwApp.Common.Ressources.Localisation {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class de_DE {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal de_DE() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Eras2AmwApp.Common.Ressources.Localisation.de_DE", typeof(de_DE).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hinzufügen.
        /// </summary>
        internal static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gerät hinzufügen.
        /// </summary>
        internal static string AddDevice {
            get {
                return ResourceManager.GetString("AddDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adresse.
        /// </summary>
        internal static string Address {
            get {
                return ResourceManager.GetString("Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adresszusatz.
        /// </summary>
        internal static string AddressAdditional {
            get {
                return ResourceManager.GetString("AddressAdditional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abrechnungseinheit Kontaktpersonen.
        /// </summary>
        internal static string AeContactPeople {
            get {
                return ResourceManager.GetString("AeContactPeople", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abrechnungseinheit Notiz:.
        /// </summary>
        internal static string AeNote {
            get {
                return ResourceManager.GetString("AeNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abrechnungseinheit Notiz wurden aktualisiert..
        /// </summary>
        internal static string AeNoteUpdated {
            get {
                return ResourceManager.GetString("AeNoteUpdated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auftrags Information.
        /// </summary>
        internal static string AppointmentDetails {
            get {
                return ResourceManager.GetString("AppointmentDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auftrag Notiz wurden aktualisiert..
        /// </summary>
        internal static string AuftragNoteUpdated {
            get {
                return ResourceManager.GetString("AuftragNoteUpdated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ihr System unterstützt die Funktion nicht. Bitte überprüfen Sie die Kamerafunktion!.
        /// </summary>
        internal static string BarcodeFehlar {
            get {
                return ResourceManager.GetString("BarcodeFehlar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bearbeiten.
        /// </summary>
        internal static string Bearbeiten {
            get {
                return ResourceManager.GetString("Bearbeiten", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eichdatum/Batterie.
        /// </summary>
        internal static string CalibrationDate {
            get {
                return ResourceManager.GetString("CalibrationDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abbrechen.
        /// </summary>
        internal static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ort.
        /// </summary>
        internal static string City {
            get {
                return ResourceManager.GetString("City", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kontakt.
        /// </summary>
        internal static string Contact {
            get {
                return ResourceManager.GetString("Contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Möchten Sie diesen Kontakt bearbeiten ?.
        /// </summary>
        internal static string ContactEdit {
            get {
                return ResourceManager.GetString("ContactEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kontakt Typ.
        /// </summary>
        internal static string ContactType {
            get {
                return ResourceManager.GetString("ContactType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kundenname.
        /// </summary>
        internal static string CustomerName {
            get {
                return ResourceManager.GetString("CustomerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Datenbank Version.
        /// </summary>
        internal static string DbVersion {
            get {
                return ResourceManager.GetString("DbVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ausbaudatum.
        /// </summary>
        internal static string DeinstallationDate {
            get {
                return ResourceManager.GetString("DeinstallationDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Löschen.
        /// </summary>
        internal static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sind Sie sicher, dass Sie die Nutzeinheit löschen wollen?.
        /// </summary>
        internal static string DeleteConfirmation {
            get {
                return ResourceManager.GetString("DeleteConfirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Datenbank löschen.
        /// </summary>
        internal static string DeleteDb {
            get {
                return ResourceManager.GetString("DeleteDb", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Diese Aktion würde dazu führen, dass die momentane Unterschrift gelöscht wird. Wollen Sie trotzdem Fortfahren ?.
        /// </summary>
        internal static string DeleteSignatureWarning {
            get {
                return ResourceManager.GetString("DeleteSignatureWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bezeichnung.
        /// </summary>
        internal static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gerät.
        /// </summary>
        internal static string Device {
            get {
                return ResourceManager.GetString("Device", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eichdatum: {0}.
        /// </summary>
        internal static string DeviceCalibrationDate {
            get {
                return ResourceManager.GetString("DeviceCalibrationDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ausbaudatum: {0}.
        /// </summary>
        internal static string DeviceDeinstallationDate {
            get {
                return ResourceManager.GetString("DeviceDeinstallationDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gerät bearbeiten.
        /// </summary>
        internal static string DeviceEdit {
            get {
                return ResourceManager.GetString("DeviceEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gerät Information.
        /// </summary>
        internal static string DeviceInfo {
            get {
                return ResourceManager.GetString("DeviceInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Einbaudatum: {0}.
        /// </summary>
        internal static string DeviceInstalationDate {
            get {
                return ResourceManager.GetString("DeviceInstalationDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Geräte Liste:.
        /// </summary>
        internal static string DeviceList {
            get {
                return ResourceManager.GetString("DeviceList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gerät Modell.
        /// </summary>
        internal static string DeviceModel {
            get {
                return ResourceManager.GetString("DeviceModel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gerätenummer.
        /// </summary>
        internal static string DeviceNumber {
            get {
                return ResourceManager.GetString("DeviceNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Laufende Nummer: {0}.
        /// </summary>
        internal static string DeviceOngoingNumber {
            get {
                return ResourceManager.GetString("DeviceOngoingNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Raum: {0}.
        /// </summary>
        internal static string DeviceRoom {
            get {
                return ResourceManager.GetString("DeviceRoom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gerätetyp.
        /// </summary>
        internal static string DeviceTyp {
            get {
                return ResourceManager.GetString("DeviceTyp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Die Gerätedaten wurde aktualisiert..
        /// </summary>
        internal static string DeviceUpdatedConfirmation {
            get {
                return ResourceManager.GetString("DeviceUpdatedConfirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Editieren.
        /// </summary>
        internal static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sind Sie sicher, dass Sie die Nutzeinheit editieren wollen?.
        /// </summary>
        internal static string EditConfirmation {
            get {
                return ResourceManager.GetString("EditConfirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fehler.
        /// </summary>
        internal static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gerät zurücksetzen.
        /// </summary>
        internal static string FactoryReset {
            get {
                return ResourceManager.GetString("FactoryReset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vorname.
        /// </summary>
        internal static string FirstName {
            get {
                return ResourceManager.GetString("FirstName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Es wurde bereits ein Nutzer in der Zukunft festgelegt..
        /// </summary>
        internal static string FutureNutzerExists {
            get {
                return ResourceManager.GetString("FutureNutzerExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hinweis.
        /// </summary>
        internal static string Hint {
            get {
                return ResourceManager.GetString("Hint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hausnummer.
        /// </summary>
        internal static string HouseNumber {
            get {
                return ResourceManager.GetString("HouseNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Information.
        /// </summary>
        internal static string Information {
            get {
                return ResourceManager.GetString("Information", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Einbaudatum.
        /// </summary>
        internal static string InstallactionDate {
            get {
                return ResourceManager.GetString("InstallactionDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nutzer Typ.
        /// </summary>
        internal static string Kind {
            get {
                return ResourceManager.GetString("Kind", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lease-Status: {0}.
        /// </summary>
        internal static string LeaseStatus {
            get {
                return ResourceManager.GetString("LeaseStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lage.
        /// </summary>
        internal static string Location {
            get {
                return ResourceManager.GetString("Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LockNutzer.
        /// </summary>
        internal static string LockNutzer {
            get {
                return ResourceManager.GetString("LockNutzer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login.
        /// </summary>
        internal static string Login {
            get {
                return ResourceManager.GetString("Login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login....
        /// </summary>
        internal static string LoginInProgress {
            get {
                return ResourceManager.GetString("LoginInProgress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lokales Backup.
        /// </summary>
        internal static string LokalBackup {
            get {
                return ResourceManager.GetString("LokalBackup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Postfach.
        /// </summary>
        internal static string Mailbox {
            get {
                return ResourceManager.GetString("Mailbox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sind Sie sicher, dass die Wartung für alle Geräte auf einmal erfolgen soll ?.
        /// </summary>
        internal static string maintainAllWarning {
            get {
                return ResourceManager.GetString("maintainAllWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wartung.
        /// </summary>
        internal static string Maintenance {
            get {
                return ResourceManager.GetString("Maintenance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wartungs-Status: {0}.
        /// </summary>
        internal static string MaintenanceStatus {
            get {
                return ResourceManager.GetString("MaintenanceStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hersteller.
        /// </summary>
        internal static string Manufacturer {
            get {
                return ResourceManager.GetString("Manufacturer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Einzugsdatum.
        /// </summary>
        internal static string MoveInDate {
            get {
                return ResourceManager.GetString("MoveInDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auszugsdatum.
        /// </summary>
        internal static string MoveOutDate {
            get {
                return ResourceManager.GetString("MoveOutDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nutzer editieren.
        /// </summary>
        internal static string NeEdit {
            get {
                return ResourceManager.GetString("NeEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nutzeinheit Hinzufügen.
        /// </summary>
        internal static string NeHinzufügen {
            get {
                return ResourceManager.GetString("NeHinzufügen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Es ist keine Netzwerkverbindung vorhanden. Die Aufträge konnten nicht synchronisiert werden..
        /// </summary>
        internal static string NetworkUnavailableForSyncOrders {
            get {
                return ResourceManager.GetString("NetworkUnavailableForSyncOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Neue Nutzeinheit.
        /// </summary>
        internal static string NewNe {
            get {
                return ResourceManager.GetString("NewNe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Neuer Nutzer.
        /// </summary>
        internal static string NewNutzer {
            get {
                return ResourceManager.GetString("NewNutzer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ein Login ist nicht möglich.Die Uhrzeit des Gerätes weicht zu stark von der tatsächlichen Uhrzeit ab.Bitte rekonfigurieren Sie die Uhrzeit des Gerätes..
        /// </summary>
        internal static string NtpToLocalTimeDeviationFailed {
            get {
                return ResourceManager.GetString("NtpToLocalTimeDeviationFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auftrag nicht ausgeführt weil:.
        /// </summary>
        internal static string NutzeinheitAmwInfoWarning {
            get {
                return ResourceManager.GetString("NutzeinheitAmwInfoWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nutzeinheit Geräte.
        /// </summary>
        internal static string NutzeinheitDevices {
            get {
                return ResourceManager.GetString("NutzeinheitDevices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nutzeinheiten.
        /// </summary>
        internal static string Nutzeinheiten {
            get {
                return ResourceManager.GetString("Nutzeinheiten", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sind Sie sicher, dass Sie die Nutzeinheit verlassen möchten ? Nicht gespeicherte Daten gehen verloren!.
        /// </summary>
        internal static string NutzeinheitLostWarning {
            get {
                return ResourceManager.GetString("NutzeinheitLostWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nutzerwechsel.
        /// </summary>
        internal static string NutzerChange {
            get {
                return ResourceManager.GetString("NutzerChange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nutzerwechsel nicht möglich.
        /// </summary>
        internal static string NutzerChangeImpossible {
            get {
                return ResourceManager.GetString("NutzerChangeImpossible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nutzer Details.
        /// </summary>
        internal static string NutzerDetails {
            get {
                return ResourceManager.GetString("NutzerDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Akzeptieren.
        /// </summary>
        internal static string Ok {
            get {
                return ResourceManager.GetString("Ok", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vorheriger Nutzer.
        /// </summary>
        internal static string OldNutzer {
            get {
                return ResourceManager.GetString("OldNutzer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Laufende Nummer.
        /// </summary>
        internal static string OngoingNumber {
            get {
                return ResourceManager.GetString("OngoingNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auftrag Information:.
        /// </summary>
        internal static string OrderInfo {
            get {
                return ResourceManager.GetString("OrderInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auftrag Notiz:.
        /// </summary>
        internal static string OrderNote {
            get {
                return ResourceManager.GetString("OrderNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Person Notiz.
        /// </summary>
        internal static string PersonNote {
            get {
                return ResourceManager.GetString("PersonNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sind Sie sicher, dass Sie das Foto löschen wollen?.
        /// </summary>
        internal static string PhotoDeleteConfirmation {
            get {
                return ResourceManager.GetString("PhotoDeleteConfirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sind Sie sicher, dass Sie {0} Fotos herunterladen möchten?.
        /// </summary>
        internal static string PhotoDownloadConfirmation {
            get {
                return ResourceManager.GetString("PhotoDownloadConfirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Position.
        /// </summary>
        internal static string Position {
            get {
                return ResourceManager.GetString("Position", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Postleitzahl.
        /// </summary>
        internal static string PostCode {
            get {
                return ResourceManager.GetString("PostCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remote Backup.
        /// </summary>
        internal static string RemoteBackup {
            get {
                return ResourceManager.GetString("RemoteBackup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Raum.
        /// </summary>
        internal static string Room {
            get {
                return ResourceManager.GetString("Room", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Anrede.
        /// </summary>
        internal static string Salutation {
            get {
                return ResourceManager.GetString("Salutation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Speichern.
        /// </summary>
        internal static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sind Sie sicher, dass Sie die Person speichern wollen ?.
        /// </summary>
        internal static string SaveConfirmation {
            get {
                return ResourceManager.GetString("SaveConfirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sind Sie sicher, dass Sie die Nutzeinheit speichern wollen ? Das speichern der Daten erzeugt einen Leerstand..
        /// </summary>
        internal static string SaveConfirmationWithLeerstand {
            get {
                return ResourceManager.GetString("SaveConfirmationWithLeerstand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nachname.
        /// </summary>
        internal static string SecondName {
            get {
                return ResourceManager.GetString("SecondName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fehler auswählen:.
        /// </summary>
        internal static string SelectError {
            get {
                return ResourceManager.GetString("SelectError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Die Unterschrift wurde gespeichert.
        /// </summary>
        internal static string SignatureConfirmation {
            get {
                return ResourceManager.GetString("SignatureConfirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Straße.
        /// </summary>
        internal static string Street {
            get {
                return ResourceManager.GetString("Street", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daten Synchronisieren.
        /// </summary>
        internal static string Synchronize {
            get {
                return ResourceManager.GetString("Synchronize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bitte vergessen Sie nicht ihre Aufträge zu synchronisieren..
        /// </summary>
        internal static string SyncOrdersHint {
            get {
                return ResourceManager.GetString("SyncOrdersHint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Titel.
        /// </summary>
        internal static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ein oder mehrere Nutzeinheiten wurden nicht abgeschlossen. Eine Daten-Synchronisation würde zum Verlust der eingegebenen Daten dieser Nutzeinheiten führen. Wollen Sie trotzdem fortfahren ?.
        /// </summary>
        internal static string UnfinishedNutzeinheit {
            get {
                return ResourceManager.GetString("UnfinishedNutzeinheit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nutzer Email.
        /// </summary>
        internal static string UserEmail {
            get {
                return ResourceManager.GetString("UserEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Information.
        /// </summary>
        internal static string UserInfo {
            get {
                return ResourceManager.GetString("UserInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Die lokale Anmeldung ist nicht möglich, da der Webservice nicht erreichbar ist und bisher keine erfolgreiche Anmeldung durchgeführt wurde..
        /// </summary>
        internal static string UserLoginFailed1 {
            get {
                return ResourceManager.GetString("UserLoginFailed1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Die lokale Anmeldung ist nicht möglich, da der Benutzer und/oder das Passwort falsch ist..
        /// </summary>
        internal static string UserLoginFailed2 {
            get {
                return ResourceManager.GetString("UserLoginFailed2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Die lokale Anmeldung ist nicht möglich, da der Benutzer Zeitstempel abgelaufen ist..
        /// </summary>
        internal static string UserLoginFailed3 {
            get {
                return ResourceManager.GetString("UserLoginFailed3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Die Anmeldung ist nicht möglich, da der Benutzer und/oder das Passwort falsch ist..
        /// </summary>
        internal static string UserLoginFailed4 {
            get {
                return ResourceManager.GetString("UserLoginFailed4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Die Datensynchronisierung konnte nicht oder nur teilweise durchgeführt werden..
        /// </summary>
        internal static string UserLoginFailed5 {
            get {
                return ResourceManager.GetString("UserLoginFailed5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Die Anmeldung ist nicht möglich, da ein Benutzerwechsel nur mit einer Internetverbindung durchgeführt werden kann..
        /// </summary>
        internal static string UserLoginFailed6 {
            get {
                return ResourceManager.GetString("UserLoginFailed6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Die Anmeldung ist nicht möglich. Eine Netzwerkverbindung ist vorhanden, der Webservice ist jedoch nicht erreichbar..
        /// </summary>
        internal static string UserLoginFailed7 {
            get {
                return ResourceManager.GetString("UserLoginFailed7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nutzername.
        /// </summary>
        internal static string Username {
            get {
                return ResourceManager.GetString("Username", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Begehungsreihenfolge.
        /// </summary>
        internal static string WalkSequence {
            get {
                return ResourceManager.GetString("WalkSequence", resourceCulture);
            }
        }
    }
}
