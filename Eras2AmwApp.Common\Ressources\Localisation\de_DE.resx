﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Cancel" xml:space="preserve">
    <value>Abbrechen</value>
  </data>
  <data name="CustomerName" xml:space="preserve">
    <value>Kundenname</value>
  </data>
  <data name="DbVersion" xml:space="preserve">
    <value>Datenbank Version</value>
  </data>
  <data name="DeleteDb" xml:space="preserve">
    <value>Datenbank löschen</value>
  </data>
  <data name="DeviceInfo" xml:space="preserve">
    <value>Gerät Information</value>
  </data>
  <data name="DeviceModel" xml:space="preserve">
    <value>Gerät Modell</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Fehler</value>
  </data>
  <data name="FactoryReset" xml:space="preserve">
    <value>Gerät zurücksetzen</value>
  </data>
  <data name="Hint" xml:space="preserve">
    <value>Hinweis</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="LoginInProgress" xml:space="preserve">
    <value>Login...</value>
  </data>
  <data name="LokalBackup" xml:space="preserve">
    <value>Lokales Backup</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Hersteller</value>
  </data>
  <data name="NutzeinheitDevices" xml:space="preserve">
    <value>Nutzeinheit Geräte</value>
  </data>
  <data name="NutzerDetails" xml:space="preserve">
    <value>Nutzer Details</value>
  </data>
  <data name="NetworkUnavailableForSyncOrders" xml:space="preserve">
    <value>Es ist keine Netzwerkverbindung vorhanden. Die Aufträge konnten nicht synchronisiert werden.</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>Akzeptieren</value>
  </data>
  <data name="RemoteBackup" xml:space="preserve">
    <value>Remote Backup</value>
  </data>
  <data name="Synchronize" xml:space="preserve">
    <value>Daten Synchronisieren</value>
  </data>
  <data name="SyncOrdersHint" xml:space="preserve">
    <value>Bitte vergessen Sie nicht ihre Aufträge zu synchronisieren.</value>
  </data>
  <data name="UserEmail" xml:space="preserve">
    <value>Nutzer Email</value>
  </data>
  <data name="UserInfo" xml:space="preserve">
    <value>User Information</value>
  </data>
  <data name="UserLoginFailed1" xml:space="preserve">
    <value>Die lokale Anmeldung ist nicht möglich, da der Webservice nicht erreichbar ist und bisher keine erfolgreiche Anmeldung durchgeführt wurde.</value>
  </data>
  <data name="UserLoginFailed2" xml:space="preserve">
    <value>Die lokale Anmeldung ist nicht möglich, da der Benutzer und/oder das Passwort falsch ist.</value>
  </data>
  <data name="UserLoginFailed3" xml:space="preserve">
    <value>Die lokale Anmeldung ist nicht möglich, da der Benutzer Zeitstempel abgelaufen ist.</value>
  </data>
  <data name="UserLoginFailed4" xml:space="preserve">
    <value>Die Anmeldung ist nicht möglich, da der Benutzer und/oder das Passwort falsch ist.</value>
  </data>
  <data name="UserLoginFailed5" xml:space="preserve">
    <value>Die Datensynchronisierung konnte nicht oder nur teilweise durchgeführt werden.</value>
  </data>
  <data name="UserLoginFailed6" xml:space="preserve">
    <value>Die Anmeldung ist nicht möglich, da ein Benutzerwechsel nur mit einer Internetverbindung durchgeführt werden kann.</value>
  </data>
  <data name="UserLoginFailed7" xml:space="preserve">
    <value>Die Anmeldung ist nicht möglich. Eine Netzwerkverbindung ist vorhanden, der Webservice ist jedoch nicht erreichbar.</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>Nutzername</value>
  </data>
  <data name="LeaseStatus" xml:space="preserve">
    <value>Lease-Status: {0}</value>
  </data>
  <data name="MaintenanceStatus" xml:space="preserve">
    <value>Wartungs-Status: {0}</value>
  </data>
  <data name="DeviceCalibrationDate" xml:space="preserve">
    <value>Eichdatum: {0}</value>
  </data>
  <data name="DeviceDeinstallationDate" xml:space="preserve">
    <value>Ausbaudatum: {0}</value>
  </data>
  <data name="DeviceInstalationDate" xml:space="preserve">
    <value>Einbaudatum: {0}</value>
  </data>
  <data name="DeviceOngoingNumber" xml:space="preserve">
    <value>Laufende Nummer: {0}</value>
  </data>
  <data name="DeviceRoom" xml:space="preserve">
    <value>Raum: {0}</value>
  </data>
  <data name="DeviceEdit" xml:space="preserve">
    <value>Gerät bearbeiten</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="NeHinzufügen" xml:space="preserve">
    <value>Nutzeinheit Hinzufügen</value>
  </data>
  <data name="NewNe" xml:space="preserve">
    <value>Neue Nutzeinheit</value>
  </data>
  <data name="NewNutzer" xml:space="preserve">
    <value>Neuer Nutzer</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Adresse</value>
  </data>
  <data name="NutzerChange" xml:space="preserve">
    <value>Nutzerwechsel</value>
  </data>
  <data name="OldNutzer" xml:space="preserve">
    <value>Vorheriger Nutzer</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Hinzufügen</value>
  </data>
  <data name="AddressAdditional" xml:space="preserve">
    <value>Adresszusatz</value>
  </data>
  <data name="AeContactPeople" xml:space="preserve">
    <value>Abrechnungseinheit Kontaktpersonen</value>
  </data>
  <data name="AeNote" xml:space="preserve">
    <value>Abrechnungseinheit Notiz:</value>
  </data>
  <data name="AppointmentDetails" xml:space="preserve">
    <value>Auftrags Information</value>
  </data>
  <data name="Bearbeiten" xml:space="preserve">
    <value>Bearbeiten</value>
  </data>
  <data name="CalibrationDate" xml:space="preserve">
    <value>Eichdatum/Batterie</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>Ort</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Kontakt</value>
  </data>
  <data name="ContactEdit" xml:space="preserve">
    <value>Möchten Sie diesen Kontakt bearbeiten ?</value>
  </data>
  <data name="ContactType" xml:space="preserve">
    <value>Kontakt Typ</value>
  </data>
  <data name="DeinstallationDate" xml:space="preserve">
    <value>Ausbaudatum</value>
  </data>
  <data name="DeleteConfirmation" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie die Nutzeinheit löschen wollen?</value>
  </data>
  <data name="Device" xml:space="preserve">
    <value>Gerät</value>
  </data>
  <data name="DeviceList" xml:space="preserve">
    <value>Geräte Liste:</value>
  </data>
  <data name="DeviceUpdatedConfirmation" xml:space="preserve">
    <value>Die Gerätedaten wurde aktualisiert.</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Editieren</value>
  </data>
  <data name="EditConfirmation" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie die Nutzeinheit editieren wollen?</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>Vorname</value>
  </data>
  <data name="FutureNutzerExists" xml:space="preserve">
    <value>Es wurde bereits ein Nutzer in der Zukunft festgelegt.</value>
  </data>
  <data name="HouseNumber" xml:space="preserve">
    <value>Hausnummer</value>
  </data>
  <data name="InstallactionDate" xml:space="preserve">
    <value>Einbaudatum</value>
  </data>
  <data name="Kind" xml:space="preserve">
    <value>Nutzer Typ</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Lage</value>
  </data>
  <data name="LockNutzer" xml:space="preserve">
    <value>LockNutzer</value>
  </data>
  <data name="Mailbox" xml:space="preserve">
    <value>Postfach</value>
  </data>
  <data name="Maintenance" xml:space="preserve">
    <value>Wartung</value>
  </data>
  <data name="MoveInDate" xml:space="preserve">
    <value>Einzugsdatum</value>
  </data>
  <data name="MoveOutDate" xml:space="preserve">
    <value>Auszugsdatum</value>
  </data>
  <data name="NeEdit" xml:space="preserve">
    <value>Nutzer editieren</value>
  </data>
  <data name="AeNoteUpdated" xml:space="preserve">
    <value>Abrechnungseinheit Notiz wurden aktualisiert.</value>
  </data>
  <data name="Nutzeinheiten" xml:space="preserve">
    <value>Nutzeinheiten</value>
  </data>
  <data name="NutzerChangeImpossible" xml:space="preserve">
    <value>Nutzerwechsel nicht möglich</value>
  </data>
  <data name="OrderInfo" xml:space="preserve">
    <value>Auftrag Information:</value>
  </data>
  <data name="OrderNote" xml:space="preserve">
    <value>Auftrag Notiz:</value>
  </data>
  <data name="PersonNote" xml:space="preserve">
    <value>Person Notiz</value>
  </data>
  <data name="Position" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="PostCode" xml:space="preserve">
    <value>Postleitzahl</value>
  </data>
  <data name="Room" xml:space="preserve">
    <value>Raum</value>
  </data>
  <data name="Salutation" xml:space="preserve">
    <value>Anrede</value>
  </data>
  <data name="SaveConfirmation" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie die Person speichern wollen ?</value>
  </data>
  <data name="SaveConfirmationWithLeerstand" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie die Nutzeinheit speichern wollen ? Das speichern der Daten erzeugt einen Leerstand.</value>
  </data>
  <data name="SecondName" xml:space="preserve">
    <value>Nachname</value>
  </data>
  <data name="SelectError" xml:space="preserve">
    <value>Fehler auswählen:</value>
  </data>
  <data name="Street" xml:space="preserve">
    <value>Straße</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Titel</value>
  </data>
  <data name="WalkSequence" xml:space="preserve">
    <value>Begehungsreihenfolge</value>
  </data>
  <data name="AddDevice" xml:space="preserve">
    <value>Gerät hinzufügen</value>
  </data>
  <data name="DeviceNumber" xml:space="preserve">
    <value>Gerätenummer</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Bezeichnung</value>
  </data>
  <data name="DeviceTyp" xml:space="preserve">
    <value>Gerätetyp</value>
  </data>
  <data name="OngoingNumber" xml:space="preserve">
    <value>Laufende Nummer</value>
  </data>
  <data name="SignatureConfirmation" xml:space="preserve">
    <value>Die Unterschrift wurde gespeichert</value>
  </data>
  <data name="BarcodeFehlar" xml:space="preserve">
    <value>Ihr System unterstützt die Funktion nicht. Bitte überprüfen Sie die Kamerafunktion!</value>
  </data>
  <data name="UnfinishedNutzeinheit" xml:space="preserve">
    <value>Ein oder mehrere Nutzeinheiten wurden nicht abgeschlossen. Eine Daten-Synchronisation würde zum Verlust der eingegebenen Daten dieser Nutzeinheiten führen. Wollen Sie trotzdem fortfahren ?</value>
  </data>
  <data name="PhotoDeleteConfirmation" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie das Foto löschen wollen?</value>
  </data>
  <data name="PhotoDownloadConfirmation" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie {0} Fotos herunterladen möchten?</value>
  </data>
  <data name="NutzeinheitLostWarning" xml:space="preserve">
    <value>Sind Sie sicher, dass Sie die Nutzeinheit verlassen möchten ? Nicht gespeicherte Daten gehen verloren!</value>
  </data>
  <data name="maintainAllWarning" xml:space="preserve">
    <value>Sind Sie sicher, dass die Wartung für alle Geräte auf einmal erfolgen soll ?</value>
  </data>
  <data name="NtpToLocalTimeDeviationFailed" xml:space="preserve">
    <value>Ein Login ist nicht möglich.Die Uhrzeit des Gerätes weicht zu stark von der tatsächlichen Uhrzeit ab.Bitte rekonfigurieren Sie die Uhrzeit des Gerätes.</value>
  </data>
  <data name="DeleteSignatureWarning" xml:space="preserve">
    <value>Diese Aktion würde dazu führen, dass die momentane Unterschrift gelöscht wird. Wollen Sie trotzdem Fortfahren ?</value>
  </data>
  <data name="NutzeinheitAmwInfoWarning" xml:space="preserve">
    <value>Auftrag nicht ausgeführt weil:</value>
  </data>
  <data name="AuftragNoteUpdated" xml:space="preserve">
    <value>Auftrag Notiz wurden aktualisiert.</value>
  </data>
</root>