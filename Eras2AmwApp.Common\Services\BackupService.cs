﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="BackupService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Common.Services
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.IO.Compression;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;

    using Endiancode.Utilities.Extensions;

    using Eras2AmwApp.Common.Interfaces;

    public class BackupService : IBackupService
    {
        private const string BackupFilename = "eras2_amw_backup_{0}.zip";

        private const string FullBackupFilename = "eras2_full_amw_backup_{0}.zip";

        private readonly IAppSettings appSettings;

        private List<string> whiteListOfDirectories;

        public BackupService(IAppSettings appSettings)
        {
            this.appSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
        }

        public async Task<string> FullBackup(CancellationToken cancellationToken = default)
        {
            whiteListOfDirectories = new List<string>
            {
                appSettings.DatabaseDirectory,
                appSettings.LogFilesDirectory,
                appSettings.PicturesDirectory,
                appSettings.WebserviceDirectory,
                appSettings.SignaturesDirectory
            };

            string destinationPath = Path.Combine(appSettings.BackupDirectory, string.Format(FullBackupFilename, DateTime.Now.ToStringTimestamp()));

            await CreateArchive(destinationPath).ConfigureAwait(false);

            // Get the path to the downloads folder
            string downloadsFolderPath = Path.Combine(appSettings.DownloadDirectery);

            string destinationFilePath = Path.Combine(downloadsFolderPath, string.Format(FullBackupFilename, DateTime.Now.ToStringTimestamp()));

            File.Copy(destinationPath, destinationFilePath);

            return destinationFilePath;
        }

        public async Task DatabaseDackup(CancellationToken cancellationToken = default)
        {
            whiteListOfDirectories = new List<string>
            {
                appSettings.DatabaseDirectory,
            };

            string destinationPath = Path.Combine(appSettings.BackupDirectory, string.Format(BackupFilename, DateTime.Now.ToStringTimestamp()));

            await CreateArchive(destinationPath).ConfigureAwait(false);
        }

        private async Task CreateArchive(string destinationPath)
        {
            if (!ExistsBackupDirectory(destinationPath))
            {
                CreateBackupDirectory(destinationPath);
            }

            using (var zipFileStream = new FileStream(destinationPath, FileMode.Create))
            using (var archive = new ZipArchive(zipFileStream, ZipArchiveMode.Create))
            {
                foreach (string sourceDirectory in whiteListOfDirectories.Select(directory => Path.Combine(appSettings.RootDirectory, directory)))
                {
                    await ZipDirectory(archive, sourceDirectory).ConfigureAwait(false);
                }
            }
        }

        private bool ExistsBackupDirectory(string path)
        {
            if (path == null)
            {
                throw new ArgumentNullException(nameof(path));
            }

            var directory = Path.GetDirectoryName(path);

            return Directory.Exists(directory);
        }

        private void CreateBackupDirectory(string path)
        {
            if (path == null)
            {
                throw new ArgumentNullException(nameof(path));
            }

            string directory = Path.GetDirectoryName(path);

            Directory.CreateDirectory(directory);
        }

        private async Task ZipDirectory(ZipArchive archive, string directory)
        {
            if (directory == null)
            {
                throw new ArgumentNullException(nameof(directory));
            }

            foreach (var file in Directory.EnumerateFiles(directory, "*", SearchOption.AllDirectories))
            {
                try
                {
                    archive.CreateEntryFromFile(file, file, CompressionLevel.Fastest);
                }
                catch (IOException)
                {
                    string tmpFilePath = await CopyFile(file).ConfigureAwait(false);
                    archive.CreateEntryFromFile(tmpFilePath, file, CompressionLevel.Fastest);
                    File.Delete(tmpFilePath);
                }
            }
        }

        private async Task<string> CopyFile(string sourceFile)
        {
            if (sourceFile == null)
            {
                throw new ArgumentNullException(nameof(sourceFile));
            }

            string destinationFilename = Path.GetTempFileName();

            using (FileStream sourceStream = File.Open(sourceFile, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
            using (FileStream destinationStream = File.Create(destinationFilename))
            {
                await sourceStream.CopyToAsync(destinationStream).ConfigureAwait(false);
            }

            return destinationFilename;
        }
    }
}