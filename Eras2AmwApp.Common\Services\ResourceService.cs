﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="ResourceService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Common.Services
{
    using System;
    using System.IO;
    using Interfaces;

    public class ResourceService : IResourceService
    {
        private const string NutzeinheitenDirectoryName = "Nutzeinheiten";
        private const string DevicesDirectoryName = "Devices";
        private const string SignaturesDirectory = "Signatures";
        private const string FotosBaseDirectory = "Photos";

        private readonly IAppSettings appSettings;

        public ResourceService(IAppSettings appSettings)
        {
            this.appSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
        }

        private string SignaturesPath => Path.Combine(appSettings.PicturesDirectory, SignaturesDirectory);

        private string FotosPath => Path.Combine(appSettings.PicturesDirectory, FotosBaseDirectory);

        public DirectoryInfo GetNutzeinheitPhotoDirectory(Guid nutzeinheitGuid)
        {
            var targetPath = Path.Combine(FotosPath, NutzeinheitenDirectoryName, nutzeinheitGuid.ToString());

            return new DirectoryInfo(targetPath);
        }

        public DirectoryInfo GetDeviceFotoDirectory(Guid deviceGuid)
        {
            var targetPath = Path.Combine(FotosPath, DevicesDirectoryName, deviceGuid.ToString());

            return new DirectoryInfo(targetPath);
        }

        public DirectoryInfo GetSignatureDirectory(Guid nutzeinheitGuid)
        {
            var targetPath = Path.Combine(SignaturesPath, nutzeinheitGuid.ToString());

            return new DirectoryInfo(targetPath);
        }
    }
}