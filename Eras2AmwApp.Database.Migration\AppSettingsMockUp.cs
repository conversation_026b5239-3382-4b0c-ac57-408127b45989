﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AppSettingsMockUp.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

// ReSharper disable UnassignedGetOnlyAutoProperty
namespace Eras2AmwApp.Database.Migration
{
    using System;
    using System.IO;
    using Common.Interfaces;

    public class AppSettingsMockUp : IAppSettings
    {
        public string RootDirectory => Environment.GetFolderPath(Environment.SpecialFolder.Personal);

        public string DatabaseDirectory => RootDirectory;

        public string LogFilesDirectory { get; }

        public string LogFileName { get; }

        public bool IsDevelopment { get; }

        public bool IsProductive { get; }

        public int UserLoginValidLocalLoginDays { get; }

        public string WebserviceDirectory { get; }

        public string WebserviceUploadDirectory { get; }

        public string WebserviceDownloadDirectory { get; }

        public string PicturesDirectory { get; }

        public string SignaturesDirectory { get; }

        public string AndroidAppCenterKey { get; }

        public string IosAppCenterKey { get; }

        public string SyncfusionLicenceKey { get; }

        public string BackupDirectory { get; }

        public string AzureBackupAccountName { get; }

        public string AzureBackupAccountKey { get; }

        public string AzureBackupShareName { get; }

        public bool IsStandaloneApp { get; }

        public string Eras2AmwWebserviceUrl { get; }

        public string DownloadDirectery  { get; }
}
}