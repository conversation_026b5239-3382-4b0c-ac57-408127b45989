﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DesignTimeEras2DocAppConfigContextFactory.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Migration
{
    using Contexts;
    using Implementations;
    using Microsoft.EntityFrameworkCore.Design;

    public class DesignTimeAppContextFactory : IDesignTimeDbContextFactory<Eras2AppContext>
    {
        public Eras2AppContext CreateDbContext(string[] args)
        {
            var contextFactory = new DbContextFactory(new AppSettingsMockUp());

            return contextFactory.CreateApp();
        }
    }
}