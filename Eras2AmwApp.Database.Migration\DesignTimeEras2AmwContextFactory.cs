﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DesignTimeEras2DocRecordsContextFactory.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Migration
{
    using Contexts;
    using Implementations;
    using Microsoft.EntityFrameworkCore.Design;

    public class DesignTimeEras2AmwContextFactory : IDesignTimeDbContextFactory<Eras2AmwContext>
    {
        public Eras2AmwContext CreateDbContext(string[] args)
        {
            var contextFactory = new DbContextFactory(new AppSettingsMockUp());

            return contextFactory.CreateAmw(new DatabaseSeeder());
        }
    }
}