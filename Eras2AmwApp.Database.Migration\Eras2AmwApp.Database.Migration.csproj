﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ApplicationIcon />
		<OutputType>Exe</OutputType>
		<StartupObject />
		<Configurations>Debug;Release;StandaloneDevelopment;Development;StandaloneRelease</Configurations>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<TreatWarningsAsErrors>false</TreatWarningsAsErrors>
		<WarningsAsErrors />
		<DefineConstants>DEBUG;TRACE</DefineConstants>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='StandaloneDevelopment|AnyCPU'">
		<Optimize>true</Optimize>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='StandaloneRelease|AnyCPU'">
		<Optimize>true</Optimize>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development|AnyCPU'">
		<TreatWarningsAsErrors>false</TreatWarningsAsErrors>
		<WarningsAsErrors />
		<Optimize>true</Optimize>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Eras2AmwApp.Common\Eras2AmwApp.Common.csproj" />
		<ProjectReference Include="..\Eras2AmwApp.Database\Eras2AmwApp.Database.csproj" />
	</ItemGroup>

</Project>
