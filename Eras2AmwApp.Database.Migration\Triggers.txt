﻿Trigger for initial migration
=========================

Up
==

           var updateLastModifiedAbrechnungseinheiten = @"
                CREATE TRIGGER [after_update_last_modified_abrechnungseinheiten]
                AFTER UPDATE
                ON abrechnungseinheiten
                FOR EACH ROW
                    BEGIN
                UPDATE abrechnungseinheiten SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedAbrechnungseinheiten);

            var updateLastModifiedPersonsAbrechnungseinheiten= @"
                CREATE TRIGGER [after_update_last_modified_persons_abrechnungseinheiten]
                AFTER UPDATE
                ON persons_abrechnungseinheiten
                FOR EACH ROW
                    BEGIN
                UPDATE persons_abrechnungseinheiten SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedPersonsAbrechnungseinheiten);

            var updateLastModifiedPersons = @"
                CREATE TRIGGER [after_update_last_modified_persons]
                AFTER UPDATE
                ON persons
                FOR EACH ROW
                    BEGIN
                UPDATE persons SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedPersons);

            var updateLastModifiedPersonCommunications = @"
                CREATE TRIGGER [after_update_last_modified_persons_communications]
                AFTER UPDATE
                ON persons_communications
                FOR EACH ROW
                    BEGIN
                UPDATE persons_communications SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedPersonCommunications);

            var updateLastModifiedNutzeinheiten = @"
                CREATE TRIGGER [after_update_last_modified_nutzeinheiten]
                AFTER UPDATE
                ON nutzeinheiten
                FOR EACH ROW
                    BEGIN
                UPDATE nutzeinheiten SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedNutzeinheiten);

             var updateLastModifiedNutzer = @"
                CREATE TRIGGER [after_update_last_modified_nutzer]
                AFTER UPDATE
                ON nutzer
                FOR EACH ROW
                    BEGIN
                UPDATE nutzer SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedNutzer);

            var updateLastModifiedNutzerCommunications = @"
                CREATE TRIGGER [after_update_last_modified_nutzer_communications]
                AFTER UPDATE
                ON nutzer_communications
                FOR EACH ROW
                    BEGIN
                UPDATE nutzer_communications SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedNutzerCommunications);

            var updateLastModifiedDevices = @"
                CREATE TRIGGER [after_update_last_modified_devices]
                AFTER UPDATE
                ON devices
                FOR EACH ROW
                    BEGIN
                UPDATE devices SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedDevices);

            var updateLastModifiedDeviceConsumtions = @"
                CREATE TRIGGER [after_update_last_modified_device_consumptions]
                AFTER UPDATE
                ON device_consumptions
                FOR EACH ROW
                    BEGIN
                UPDATE device_consumptions SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedDeviceConsumtions);

            var updateLastModifiedOrders = @"
                CREATE TRIGGER [after_update_last_modified_orders]
                AFTER UPDATE
                ON orders
                FOR EACH ROW
                    BEGIN
                UPDATE orders SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedOrders);

            var updateLastModifiedOrderPositions = @"
                CREATE TRIGGER [after_update_last_modified_order_positions]
                AFTER UPDATE
                ON order_positions
                FOR EACH ROW
                    BEGIN
                UPDATE order_positions SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedOrderPositions);

            var updateLastModifiedOrdersStates = @"
                CREATE TRIGGER [after_update_last_modified_orders_states]
                AFTER UPDATE
                ON orders_states
                FOR EACH ROW
                    BEGIN
                UPDATE orders_states SET last_modified = CURRENT_TIMESTAMP WHERE order_guid = old.order_guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedOrdersStates);

            var updateLastModifiedNutzeinheitenOrderStates = @"
                CREATE TRIGGER [after_update_last_modified_nutzeinheiten_order_states]
                AFTER UPDATE
                ON nutzeinheiten_order_states
                FOR EACH ROW
                    BEGIN
                UPDATE nutzeinheiten_order_states SET last_modified = CURRENT_TIMESTAMP WHERE nutzeinheit_guid = old.nutzeinheit_guid AND order_guid = old.order_guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedNutzeinheitenOrderStates);

            var updateLastModifiedDevicesOrderStates = @"
                CREATE TRIGGER [after_update_last_modified_devices_order_states]
                AFTER UPDATE
                ON devices_order_states
                FOR EACH ROW
                    BEGIN
                UPDATE devices_order_states SET last_modified = CURRENT_TIMESTAMP WHERE device_guid = old.device_guid AND order_guid = old.order_guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedDevicesOrderStates);

            var updateLastModifiedDevicesAdditionalArticles = @"
                CREATE TRIGGER [after_update_last_modified_devices_additional_articles]
                AFTER UPDATE
                ON devices_additional_articles
                FOR EACH ROW
                    BEGIN
                UPDATE devices_additional_articles SET last_modified = CURRENT_TIMESTAMP WHERE device_guid = old.device_guid AND additional_article_guid = old.additional_article_guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedDevicesAdditionalArticles);



Down
====

migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_abrechnungseinheiten");
migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_persons_abrechnungseinheiten"); 
migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_persons");
migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_persons_communications"); 


migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_nutzeinheiten");
migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_nutzer"); 
migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_nutzer_communications");
migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_devices");
migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_device_consumptions"); 


migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_orders");
migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_order_positions");
migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_orders_states");
migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_nutzeinheiten_order_states");
migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_devices_order_states");

migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_devices_additional_articles");