﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AbrechnungseinheitConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class AbrechnungseinheitConfiguration : IEntityTypeConfiguration<Abrechnungseinheit>
    {
        public void Configure(EntityTypeBuilder<Abrechnungseinheit> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("abrechnungseinheiten");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Number).HasColumnName("number").IsRequired();
            builder.Property(p => p.Note).HasColumnName("note");
            builder.Property(p => p.NoteInternal).HasColumnName("note_internal");
            builder.Property(p => p.NoteLegionella).HasColumnName("note_legionella");
            builder.Property(p => p.IPAddress).HasColumnName("ip_address");
            builder.Property(p => p.BilligPeriodEnd).HasColumnName("billing_period_end");

            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAdd().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.HasOne(p => p.Customer).WithMany(a => a.Abrechnungseinheiten).HasForeignKey(f => f.CustomerGuid).HasConstraintName("fk_customers_abrechnungseinheiten_customer_guid");
            builder.Property(p => p.CustomerGuid).HasColumnName("customer_guid").IsRequired();

            builder.HasOne(p => p.Address).WithMany(a => a.Abrechnungseinheiten).HasForeignKey(f => f.AddressGuid).HasConstraintName("fk_addresses_abrechnungseinheiten_address_guid");
            builder.Property(p => p.AddressGuid).HasColumnName("address_guid").IsRequired();

            builder.HasMany(p => p.Nutzeinheiten).WithOne(a => a.Abrechnungseinheit).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.PersonAbrechnungseinheiten).WithOne(a => a.Abrechnungseinheit).OnDelete(DeleteBehavior.Cascade);
        }
    }
}