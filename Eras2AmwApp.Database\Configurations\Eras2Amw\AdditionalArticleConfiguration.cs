﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AdditionalArticleConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;

    using Eras2AmwApp.Domain.Eras2Amw.Models;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class AdditionalArticleConfiguration : IEntityTypeConfiguration<AdditionalArticle>
    {
        public void Configure(EntityTypeBuilder<AdditionalArticle> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("additional_articles");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Number).HasColumnName("number").IsRequired();
            builder.Property(p => p.Label).HasColumnName("label");
            builder.Property(p => p.Note).HasColumnName("note");
            
            builder.HasMany(p => p.DeviceAdditionalArticles).WithOne(a => a.AdditionalArticle).OnDelete(DeleteBehavior.Restrict);
        }
    }
}