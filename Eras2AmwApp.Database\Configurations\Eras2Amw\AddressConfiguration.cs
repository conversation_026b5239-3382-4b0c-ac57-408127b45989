﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AddressConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class AddressConfiguration : IEntityTypeConfiguration<Address>
    {
        public void Configure(EntityTypeBuilder<Address> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("addresses");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Street).HasColumnName("street");
            builder.Property(p => p.Street2).HasColumnName("street2");
            builder.Property(p => p.StreetNumber).HasColumnName("street_number");
            builder.Property(p => p.Zipcode).HasColumnName("zipcode");
            builder.Property(p => p.City).HasColumnName("city").IsRequired();
            builder.Property(p => p.Mailbox).HasColumnName("mailbox");
            builder.Property(p => p.Additional).HasColumnName("additional");
            builder.Property(p => p.Latitude).HasColumnName("latitude").IsRequired();
            builder.Property(p => p.Longitude).HasColumnName("longitude").IsRequired();

            builder.HasMany(p => p.Nutzeinheiten).WithOne(a => a.Address).OnDelete(DeleteBehavior.Restrict);
            builder.HasMany(p => p.Abrechnungseinheiten).WithOne(a => a.Address).OnDelete(DeleteBehavior.Restrict);
        }
    }
}