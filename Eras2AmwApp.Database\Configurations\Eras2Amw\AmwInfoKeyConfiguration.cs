﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AmwInfoKeyConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;

    using Eras2AmwApp.Domain.Eras2Amw.Models;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class AmwInfoKeyConfiguration : IEntityTypeConfiguration<AmwInfoKey>
    {
        public void Configure(EntityTypeBuilder<AmwInfoKey> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("amw_info_keys");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Key).HasColumnName("key").IsRequired();
            builder.Property(p => p.Info).HasColumnName("info").IsRequired();

            builder.HasMany(p => p.DeviceOrderStates).WithOne(a => a.AmwInfoKey).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.NutzeinheitOrderStates).WithOne(a => a.AmwInfoKey).OnDelete(DeleteBehavior.Cascade);
        }
    }
}