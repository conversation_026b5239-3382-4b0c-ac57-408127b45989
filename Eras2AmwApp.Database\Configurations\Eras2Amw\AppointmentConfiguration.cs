﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AppointmentConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class AppointmentConfiguration : IEntityTypeConfiguration<Appointment>
    {
        public void Configure(EntityTypeBuilder<Appointment> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("appointments");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.From).HasColumnName("from").IsRequired();
            builder.Property(p => p.To).HasColumnName("to").IsRequired();

            builder.HasOne(p => p.Order).WithMany(a => a.Appointments).HasForeignKey(f => f.OrderGuid).HasConstraintName("fk_appointments_orders_order_guid");
            builder.Property(p => p.OrderGuid).HasColumnName("order_guid").IsRequired();

            builder.HasMany(p => p.AppointmentStoreUsers).WithOne(a => a.Appointment).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.AppointmentNutzeinheiten).WithOne(a => a.Appointment).OnDelete(DeleteBehavior.Cascade);
        }
    }
}