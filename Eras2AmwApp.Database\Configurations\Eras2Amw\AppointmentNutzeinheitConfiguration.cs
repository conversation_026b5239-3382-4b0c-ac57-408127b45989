﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AppointmentNutzeinheitConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;

    using Eras2AmwApp.Domain.Eras2Amw.Models;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class AppointmentNutzeinheitConfiguration : IEntityTypeConfiguration<AppointmentNutzeinheit>
    {
        public void Configure(EntityTypeBuilder<AppointmentNutzeinheit> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("appointment_nutzeinheiten");
            builder.HasKey(p => new { p.AppointmentGuid, p.NutzeinheitGuid });

            builder.HasOne(p => p.Appointment).WithMany(a => a.AppointmentNutzeinheiten).HasForeignKey(f => f.AppointmentGuid).HasConstraintName("fk_appointments_appointment_nutzeinheiten_appointment_guid");
            builder.Property(p => p.AppointmentGuid).HasColumnName("appointment_guid").IsRequired();

            builder.HasOne(p => p.Nutzeinheit).WithMany(a => a.AppointmentNutzeinheiten).HasForeignKey(f => f.NutzeinheitGuid).HasConstraintName("fk_appointments_appointment_nutzeinheiten_nutzeinheit_guid");
            builder.Property(p => p.NutzeinheitGuid).HasColumnName("nutzeinheit_guid").IsRequired();
        }
    }
}