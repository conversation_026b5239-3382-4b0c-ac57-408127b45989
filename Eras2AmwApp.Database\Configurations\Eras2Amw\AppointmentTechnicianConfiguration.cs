﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AppointmentStoreUserConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class AppointmentTechnicianConfiguration : IEntityTypeConfiguration<AppointmentTechnician>
    {
        public void Configure(EntityTypeBuilder<AppointmentTechnician> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("appointments_technician");
            builder.HasKey(p => new { p.TechnicianGuid, p.AppointmentGuid });

            builder.HasOne(p => p.Appointment).WithMany(a => a.AppointmentStoreUsers).HasForeignKey(f => f.AppointmentGuid).HasConstraintName("fk_appointments_appointment_technician_appointment_guid");
            builder.Property(p => p.AppointmentGuid).HasColumnName("appointment_guid").IsRequired();
        }
    }
}