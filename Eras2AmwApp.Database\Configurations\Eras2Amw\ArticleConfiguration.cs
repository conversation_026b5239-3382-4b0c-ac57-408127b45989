﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="ArticelConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class ArticleConfiguration : IEntityTypeConfiguration<Article>
    {
        public void Configure(EntityTypeBuilder<Article> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("articles");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Number).HasColumnName("number").IsRequired();
            builder.Property(p => p.Label).HasColumnName("label").IsRequired();
            builder.Property(p => p.Note).HasColumnName("note");

            builder.HasMany(p => p.OrderPositions).WithOne(a => a.Article).OnDelete(DeleteBehavior.Restrict);
        }
    }
}