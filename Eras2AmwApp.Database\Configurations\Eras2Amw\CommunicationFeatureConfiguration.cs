﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Communication_capacities.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class CommunicationFeatureConfiguration : IEntityTypeConfiguration<CommunicationFeature>
    {
        public void Configure(EntityTypeBuilder<CommunicationFeature> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("communication_features");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Feature).HasColumnName("feature").IsRequired();
            builder.Property(p => p.Note).HasColumnName("note");
            builder.Property(p => p.Kind).HasColumnName("kind").IsRequired();
            builder.Property(p => p.IsPreset).HasColumnName("is_preset").IsRequired();

            builder.HasMany(p => p.NutzerCommunications).WithOne(a => a.CommunicationFeature).OnDelete(DeleteBehavior.Restrict);
            builder.HasMany(p => p.PersonCommunications).WithOne(a => a.CommunicationFeature).OnDelete(DeleteBehavior.Restrict);
        }
    }
}