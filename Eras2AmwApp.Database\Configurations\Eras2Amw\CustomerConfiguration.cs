﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="CustomerConfigurations.cs" company="Endiancode GmbH">
//  </copyright>
//  <summary>
//    
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class CustomerConfiguration : IEntityTypeConfiguration<Customer>
    {
        public void Configure(EntityTypeBuilder<Customer> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("customers");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Name).HasColumnName("name").IsRequired();
            builder.Property(p => p.Name2).HasColumnName("name_2");
            builder.Property(p => p.Name3).HasColumnName("name_3");
            builder.Property(p => p.Number).HasColumnName("number").IsRequired();
            builder.Property(p => p.Note).HasColumnName("note");

            builder.HasOne(p => p.Salutation).WithMany(a => a.Customers).HasForeignKey(f => f.SalutationId).HasConstraintName("fk_salutations_customers_salutation_id");
            builder.Property(p => p.SalutationId).HasColumnName("salutation_id").IsRequired();

            builder.HasMany(p => p.Abrechnungseinheiten).WithOne(a => a.Customer).OnDelete(DeleteBehavior.Cascade);
        }
    }
}