﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceAdditionalArticleConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;

    using Eras2AmwApp.Domain.Eras2Amw.Models;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class DeviceAdditionalArticleConfiguration : IEntityTypeConfiguration<DeviceAdditionalArticle>
    {
        public void Configure(EntityTypeBuilder<DeviceAdditionalArticle> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("devices_additional_articles");
            builder.HasKey(x => new { x.DeviceGuid, x.AdditionalArticleGuid });
            builder.Property(p => p.DeviceGuid).HasColumnName("device_guid").IsRequired();
            builder.Property(p => p.AdditionalArticleGuid).HasColumnName("additional_article_guid").IsRequired();

            builder.Property(p => p.IsCreatedByApp).HasColumnName("is_created_by_app").IsRequired();

            builder.Property(p => p.Quantity).HasColumnName("quantity").HasColumnType("decimal(8, 2)").IsRequired();
            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAdd().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.HasOne(p => p.Device).WithMany(a => a.DeviceAdditionalArticles).HasForeignKey(f => f.DeviceGuid).HasConstraintName("fk_devices_devices_additional_articles_device_guid");
            builder.Property(p => p.DeviceGuid).HasColumnName("device_guid").IsRequired();

            builder.HasOne(p => p.AdditionalArticle).WithMany(a => a.DeviceAdditionalArticles).HasForeignKey(f => f.AdditionalArticleGuid).HasConstraintName("fk_additional_articles_devices_additional_articles_additional_article_guid");
            builder.Property(p => p.AdditionalArticleGuid).HasColumnName("additional_article_guid");
        }
    }
}