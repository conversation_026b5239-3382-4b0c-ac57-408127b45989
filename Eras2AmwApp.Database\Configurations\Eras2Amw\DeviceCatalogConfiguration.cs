﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceCatalogConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class DeviceCatalogConfiguration : IEntityTypeConfiguration<DeviceCatalog>
    {
        public void Configure(EntityTypeBuilder<DeviceCatalog> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("device_catalogs");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.ArticleName).HasColumnName("article_name").IsRequired();
            builder.Property(p => p.ArticleNumber).HasColumnName("article_number").IsRequired();
            builder.Property(p => p.ArticleDescription).HasColumnName("article_description");
            builder.Property(p => p.ArticleNumberManufacturer).HasColumnName("article_number_manufacturer");
            builder.Property(p => p.Label).HasColumnName("label");
            builder.Property(p => p.Subtraction).HasColumnName("subtraction").IsRequired();
            builder.Property(p => p.Hkvv).HasColumnName("hkvv").IsRequired();
            builder.Property(p => p.Radio).HasColumnName("radio").IsRequired();
            builder.Property(p => p.OrderCode).HasColumnName("order_code");
            builder.Property(p => p.InstallationLength).HasColumnName("installation_length");
            builder.Property(p => p.Connector).HasColumnName("connector");
            builder.Property(p => p.Diameter).HasColumnName("diameter");
            builder.Property(p => p.SontexRadioIdentifier).HasColumnName("sontex_radio_identifier");
            builder.Property(p => p.SontexHkvIdentifier).HasColumnName("sontex_hkv_identifier");
            builder.Property(p => p.Note).HasColumnName("note");

            builder.HasOne(p => p.DeviceKind).WithMany(a => a.DeviceCatalogs).HasForeignKey(f => f.DeviceKindGuid).HasConstraintName("fk_device_kinds_devices_catologs_device_kind_guid");
            builder.Property(p => p.DeviceKindGuid).HasColumnName("device_kind_guid").IsRequired();

            builder.HasOne(p => p.Manufacturer).WithMany(a => a.DeviceCatalogs).HasForeignKey(f => f.ManufacturerGuid).HasConstraintName("fk_manufacturers_devices_catologs_manufacturer_guid");
            builder.Property(p => p.ManufacturerGuid).HasColumnName("manufacturer_guid");

            builder.HasMany(p => p.Devices).WithOne(a => a.DeviceCatalog).OnDelete(DeleteBehavior.Restrict);
        }
    }
}