﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class DeviceConfiguration : IEntityTypeConfiguration<Device>
    {
        public void Configure(EntityTypeBuilder<Device> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("devices");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Number).HasColumnName("number").IsRequired();
            builder.Property(p => p.Label).HasColumnName("label");
            builder.Property(p => p.Description).HasColumnName("description");
            builder.Property(p => p.InstallationDate).HasColumnName("installation_date").IsRequired();
            builder.Property(p => p.DeinstallationDate).HasColumnName("deinstallation_date");
            builder.Property(p => p.CalibrationDate).HasColumnName("calibration_date");
            builder.Property(p => p.Unit).HasColumnName("unit");
            builder.Property(p => p.Note).HasColumnName("note");
            builder.Property(p => p.Subtraction).HasColumnName("subtraction").IsRequired();
            builder.Property(p => p.GroupIdentifier).HasColumnName("group_identifier").HasMaxLength(5);
            builder.Property(p => p.Estimation).HasColumnName("estimation").IsRequired();
            builder.Property(p => p.FirstActivation).HasColumnName("first_activation");
            builder.Property(p => p.FirmwareVersion).HasColumnName("firmware_version");
            builder.Property(p => p.OngoingNumber).HasColumnName("ongoing_number").IsRequired();
            builder.Property(p => p.IsLeased).HasColumnName("is_leased").IsRequired();
            builder.Property(p => p.IsMaintained).HasColumnName("is_maintained").IsRequired();
            builder.Property(p => p.IsCreatedByApp).HasColumnName("is_created_by_app").IsRequired();

            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAdd().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.HasOne(p => p.Nutzeinheit).WithMany(a => a.Devices).HasForeignKey(f => f.NutzeinheitGuid).HasConstraintName("fk_nutzeinheiten_devices_nutzeinheit_guid");
            builder.Property(p => p.NutzeinheitGuid).HasColumnName("nutzeinheit_guid").IsRequired();

            builder.HasOne(p => p.DeviceCatalog).WithMany(a => a.Devices).HasForeignKey(f => f.DeviceCatalogGuid).HasConstraintName("fk_device_catalogs_devices_device_catalog_guid");
            builder.Property(p => p.DeviceCatalogGuid).HasColumnName("device_catalog_guid").IsRequired();

            builder.HasOne(p => p.Room).WithMany(a => a.Devices).HasForeignKey(f => f.RoomGuid).HasConstraintName("fk_rooms_devices_room_guid");
            builder.Property(p => p.RoomGuid).HasColumnName("room_guid").IsRequired();

            builder.HasMany(p => p.DeviceConsumptions).WithOne(a => a.Device).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.Photos).WithOne(a => a.Device).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.OrderStates).WithOne(a => a.Device).OnDelete(DeleteBehavior.Cascade);

            /**
             * builder.HasMany(p => p.DeviceRadiators).WithOne(a => a.Device).OnDelete(DeleteBehavior.Restrict);
             */
        }
    }
}