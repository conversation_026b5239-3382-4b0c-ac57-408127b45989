﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceConsumption.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class DeviceConsumptionConfiguration : IEntityTypeConfiguration<DeviceConsumption>
    {
        public void Configure(EntityTypeBuilder<DeviceConsumption> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("device_consumptions");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.ReadingDate).HasColumnName("reading_date");
            builder.Property(p => p.Factor).HasColumnName("factor").IsRequired();
            builder.Property(p => p.Reading).HasColumnName("reading");
            builder.Property(p => p.Consumption).HasColumnName("consumption");
            builder.Property(p => p.ChangeDate).HasColumnName("change_date");
            builder.Property(p => p.Estimation).HasColumnName("estimation_kind").IsRequired();
            builder.Property(p => p.State).HasColumnName("state").IsRequired();
            builder.Property(p => p.Note).HasColumnName("note");
            builder.Property(p => p.IsReconstructed).HasColumnName("is_reconstructed").IsRequired();
            builder.Property(p => p.Manual).HasColumnName("manual");
            builder.Property(p => p.Origin).HasColumnName("origin").IsRequired();
            builder.Property(p => p.AirTemperature).HasColumnName("air_temperature").HasColumnType("decimal(4,1)");
            builder.Property(p => p.RadiatorTemperature).HasColumnName("radiator_temperature").HasColumnType("decimal(5,1)");
            builder.Property(p => p.LastErrorDate).HasColumnName("last_error_date");
            builder.Property(p => p.Error).HasColumnName("error");
            builder.Property(p => p.AmpouleColour).HasColumnName("ampoule_colour");

            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAdd().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.HasOne(p => p.Device).WithMany(a => a.DeviceConsumptions).HasForeignKey(f => f.DeviceGuid).HasConstraintName("fk_devices_devices_consumptions_device_guid");
            builder.Property(p => p.DeviceGuid).HasColumnName("device_guid").IsRequired();

            builder.HasOne(p => p.ReadingKind).WithMany(a => a.DeviceConsumptions).HasForeignKey(f => f.ReadindKindGuid).HasConstraintName("fk_reading_kinds_device_consumptions_reading_kind_guid");
            builder.Property(p => p.ReadindKindGuid).HasColumnName("readind_kind_guid").IsRequired();
        }
    }
}