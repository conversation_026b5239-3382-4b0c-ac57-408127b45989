﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceKindConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class DeviceKindConfiguration : IEntityTypeConfiguration<DeviceKind>
    {
        public void Configure(EntityTypeBuilder<DeviceKind> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("device_kinds");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.LabelShort).HasColumnName("label_short").IsRequired();
            builder.Property(p => p.LabelLong).HasColumnName("label_long");
            builder.Property(p => p.Readable).HasColumnName("readable").IsRequired();
            builder.Property(p => p.Class).HasColumnName("class").IsRequired();

            builder.HasMany(p => p.DeviceCatalogs).WithOne(a => a.DeviceKind).OnDelete(DeleteBehavior.Restrict);
        }
    }
}