﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceOrderStateConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class DeviceOrderStateConfiguration : IEntityTypeConfiguration<DeviceOrderState>
    {
        public void Configure(EntityTypeBuilder<DeviceOrderState> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("devices_order_states");
            builder.HasKey(x => new { x.OrderGuid, x.DeviceGuid });

            builder.Property(p => p.OrderKind).HasColumnName("order_kind").IsRequired();
            builder.Property(p => p.ProcessState).HasColumnName("process_state").IsRequired();
            builder.Property(p => p.CompletedDate).HasColumnName("completed_date");

            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAdd().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.HasOne(p => p.Device).WithMany(a => a.OrderStates).HasForeignKey(x => x.DeviceGuid).HasConstraintName("fk_devices_devices_order_states_device_guid");
            builder.Property(p => p.DeviceGuid).HasColumnName("device_guid").IsRequired();

            builder.HasOne(p => p.Order).WithMany(a => a.DeviceOrderStates).HasForeignKey(f => f.OrderGuid).HasConstraintName("fk_orders_devices_order_states_order_guid");
            builder.Property(p => p.OrderGuid).HasColumnName("order_guid").IsRequired();

            builder.HasOne(p => p.AmwInfoKey).WithMany(a => a.DeviceOrderStates).HasForeignKey(f => f.AmwInfoKeyGuid).HasConstraintName("fk_amw_info_keys_device_order_states_amw_info_key_guid");
            builder.Property(p => p.AmwInfoKeyGuid).HasColumnName("amw_info_key_guid");
        }
    }
}