﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="ManufacturerConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class ManufacturerConfiguration : IEntityTypeConfiguration<Manufacturer>
    {
        public void Configure(EntityTypeBuilder<Manufacturer> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("manufacturers");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Name).HasColumnName("name").IsRequired();
            builder.Property(p => p.Note).HasColumnName("note");
            
            builder.HasMany(p => p.DeviceCatalogs).WithOne(a => a.Manufacturer).OnDelete(DeleteBehavior.Restrict);
        }
    }
}