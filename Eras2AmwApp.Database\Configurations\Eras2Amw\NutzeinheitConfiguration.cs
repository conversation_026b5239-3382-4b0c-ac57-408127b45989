﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class NutzeinheitConfiguration : IEntityTypeConfiguration<Nutzeinheit>
    {
        public void Configure(EntityTypeBuilder<Nutzeinheit> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("nutzeinheiten");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Number).HasColumnName("number").IsRequired();
            builder.Property(p => p.Location).HasColumnName("location");
            builder.Property(p => p.WalkSequence).HasColumnName("walk_sequence");
            builder.Property(p => p.Note).HasColumnName("note");
            builder.Property(p => p.IsCreatedByApp).HasColumnName("is_created_by_app").IsRequired();
            builder.Property(p => p.IPAddress).HasColumnName("ip_address");

            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAdd().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.HasOne(p => p.Abrechnungseinheit).WithMany(a => a.Nutzeinheiten).HasForeignKey(f => f.AbrechnungseinheitGuid).HasConstraintName("fk_abrechnungseinheiten_nutzeinheiten_abrechnungseinheit_guid");
            builder.Property(p => p.AbrechnungseinheitGuid).HasColumnName("abrechnungseinheit_guid").IsRequired();

            builder.HasOne(p => p.Address).WithMany(a => a.Nutzeinheiten).HasForeignKey(f => f.AddressGuid).HasConstraintName("fk_addresses_nutzeinheiten_address_guid");
            builder.Property(p => p.AddressGuid).HasColumnName("address_guid").IsRequired();

            builder.HasMany(p => p.Devices).WithOne(a => a.Nutzeinheit).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.NutzeinheitOrderPositions).WithOne(a => a.Nutzeinheit).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.Signatures).WithOne(a => a.Nutzeinheit).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.Photos).WithOne(a => a.Nutzeinheit).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.OrderStates).WithOne(a => a.Nutzeinheit).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.Nutzer).WithOne(a => a.Nutzeinheit).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.AppointmentNutzeinheiten).WithOne(a => a.Nutzeinheit).OnDelete(DeleteBehavior.Cascade);
        }
    }
}