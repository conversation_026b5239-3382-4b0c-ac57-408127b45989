﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitOrderPositionConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class NutzeinheitOrderPositionConfiguration : IEntityTypeConfiguration<NutzeinheitOrderPosition>
    {
        public void Configure(EntityTypeBuilder<NutzeinheitOrderPosition> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("nutzeinheiten_order_positions");
            builder.HasKey(x => new { x.OrderPositionGuid, x.NutzeinheitGuid });
            
            builder.HasOne(p => p.Nutzeinheit).WithMany(a => a.NutzeinheitOrderPositions).HasForeignKey(f => f.NutzeinheitGuid).HasConstraintName("fk_nutzeinheiten_nutzeinheiten_order_positions_nutzeinheit_guid");
            builder.Property(p => p.NutzeinheitGuid).HasColumnName("nutzeinheit_guid").IsRequired();

            builder.HasOne(p => p.OrderPosition).WithMany(a => a.NutzeinheitOrderPositions).HasForeignKey(f => f.OrderPositionGuid).HasConstraintName("fk_nutzeinheiten_order_positions_order_position_guid");
            builder.Property(p => p.OrderPositionGuid).HasColumnName("order_position_guid").IsRequired();
        }
    }
}