﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitOrderStateConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using System.Collections.Generic;
    using Domain.Eras2Amw.Enums;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;
    using Newtonsoft.Json;

    public class NutzeinheitOrderStateConfiguration : IEntityTypeConfiguration<NutzeinheitOrderState>
    {
        public void Configure(EntityTypeBuilder<NutzeinheitOrderState> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("nutzeinheiten_order_states");
            builder.HasKey(x => new { x.OrderGuid, x.NutzeinheitGuid });

            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAdd().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.Property(e => e.OrderKinds).HasColumnName("order_kinds").HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<IList<NutzeinheitOrderKind>>(v));

            builder.Property(p => p.ProcessState).HasColumnName("process_state").IsRequired();
            builder.Property(p => p.CompletedDate).HasColumnName("completed_date");
            builder.Property(p => p.OsPlatform).HasColumnName("os_platform");
            builder.Property(p => p.OsVersion).HasColumnName("os_version");
            builder.Property(p => p.AppVersion).HasColumnName("app_version");
            builder.Property(p => p.AppDeviceNumber).HasColumnName("app_device_number");

            builder.HasOne(p => p.Nutzeinheit).WithMany(a => a.OrderStates).HasForeignKey(f => f.NutzeinheitGuid).HasConstraintName("fk_nutzeinheiten_nutzeinheiten_order_states_nutzeinheit_guid");
            builder.Property(p => p.NutzeinheitGuid).HasColumnName("nutzeinheit_guid").IsRequired();

            builder.HasOne(p => p.Order).WithMany(a => a.NutzeinheitOrderStates).HasForeignKey(f => f.OrderGuid).HasConstraintName("fk_orders_nutzeinheiten_order_states_order_guid");
            builder.Property(p => p.OrderGuid).HasColumnName("order_guid");

            builder.HasOne(p => p.AmwInfoKey).WithMany(a => a.NutzeinheitOrderStates).HasForeignKey(f => f.AmwInfoKeyGuid).HasConstraintName("fk_amw_info_keys_nutzeinheit_order_states_amw_info_key_guid");
            builder.Property(p => p.AmwInfoKeyGuid).HasColumnName("amw_info_key_guid");
        }
    }
}