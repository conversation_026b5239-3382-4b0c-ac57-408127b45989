﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzerCoOwnershipConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//    
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class NutzerCoOwnershipConfiguration : IEntityTypeConfiguration<NutzerCoOwnership>
    {
        public void Configure(EntityTypeBuilder<NutzerCoOwnership> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("nutzer_coownership");
            builder.HasKey(p => p.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAddOrUpdate().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.Property(p => p.RangeFrom).HasColumnName("range_from").IsRequired();
            builder.Property(p => p.RangeTo).HasColumnName("range_to");
            builder.Property(p => p.ShareValue).HasColumnName("share_value");

            builder.HasOne(p => p.Nutzer).WithMany(a => a.NutzerCoOwnership).HasForeignKey(f => f.NutzerGuid).HasConstraintName("fk_nutzercoownership_nutzer_guid");
            builder.Property(p => p.NutzerGuid).HasColumnName("nutzer_guid").IsRequired();
            builder.Property(p => p.IsCreatedByApp).HasColumnName("is_created_by_app").IsRequired();
        }
    }
}
