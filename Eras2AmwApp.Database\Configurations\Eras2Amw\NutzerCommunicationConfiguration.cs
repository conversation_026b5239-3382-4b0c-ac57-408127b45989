﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzerCommunicationConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class NutzerCommunicationConfiguration : IEntityTypeConfiguration<NutzerCommunication>
    {
        public void Configure(EntityTypeBuilder<NutzerCommunication> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("nutzer_communications");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Address).HasColumnName("address").IsRequired();
            builder.Property(p => p.Note).HasColumnName("note");

            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAdd().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.HasOne(p => p.Nutzer).WithMany(a => a.NutzerCommunications).HasForeignKey(f => f.NutzerGuid).HasConstraintName("fk_nutzer_nutzer_communications_nutzer_guid");
            builder.Property(p => p.NutzerGuid).HasColumnName("nutzer_guid");

            builder.HasOne(p => p.CommunicationFeature).WithMany(a => a.NutzerCommunications).HasForeignKey(f => f.CommunicationFeatureGuid).HasConstraintName("fk_communication_features_nutzer_communications_communication_feature_guid");
            builder.Property(p => p.CommunicationFeatureGuid).HasColumnName("communication_feature_guid").IsRequired();
        }
    }
}