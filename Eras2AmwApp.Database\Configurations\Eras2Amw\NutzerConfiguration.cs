﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzerConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class NutzerConfiguration : IEntityTypeConfiguration<Nutzer>
    {
        public void Configure(EntityTypeBuilder<Nutzer> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("nutzer");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Name1).HasColumnName("name1");
            builder.Property(p => p.Name2).HasColumnName("name2");
            builder.Property(p => p.Name3).HasColumnName("name3");
            builder.Property(p => p.Name4).HasColumnName("name4");
            builder.Property(p => p.MoveInDate).HasColumnName("move_in_date").IsRequired();
            builder.Property(p => p.MoveOutDate).HasColumnName("move_out_date");

            builder.Property(p => p.NextAppointmentDate).HasColumnName("next_appointment_date");

            builder.Property(p => p.Note).HasColumnName("note");
            builder.Property(p => p.IsCreatedByApp).HasColumnName("is_created_by_app").IsRequired();
            builder.Property(p => p.Kind).HasColumnName("kind").IsRequired();

            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAdd().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.HasOne(p => p.Title).WithMany(a => a.Nutzer).HasForeignKey(f => f.TitleId).HasConstraintName("fk_titles_nutzer_title_id");
            builder.Property(p => p.TitleId).HasColumnName("title_id");

            builder.HasOne(p => p.Salutation).WithMany(a => a.Nutzer).HasForeignKey(f => f.SalutationId).HasConstraintName("fk_salutations_nutzer_salutation_id");
            builder.Property(p => p.SalutationId).HasColumnName("salutation_id").IsRequired();

            builder.HasOne(p => p.Nutzeinheit).WithMany(a => a.Nutzer).HasForeignKey(f => f.NutzeinheitGuid).HasConstraintName("fk_nutzeinheiten_nutzer_nutzeinheit_guid");
            builder.Property(p => p.NutzeinheitGuid).HasColumnName("nutzeinheit_guid").IsRequired();
            
            builder.HasMany(p => p.NutzerCommunications).WithOne(a => a.Nutzer).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.NutzerPersonen).WithOne(a => a.Nutzer).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.NutzerCoOwnership).WithOne(a => a.Nutzer).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.NutzerQuadratmeter).WithOne(a => a.Nutzer).OnDelete(DeleteBehavior.Cascade);
        }
    }
}