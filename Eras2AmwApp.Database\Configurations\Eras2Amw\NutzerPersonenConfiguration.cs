﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzerConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class NutzerPersonenConfiguration : IEntityTypeConfiguration<NutzerPersonen>
    {
        public void Configure(EntityTypeBuilder<NutzerPersonen> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("nutzer_personen");
            builder.HasKey(p => p.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAdd().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.Property(p => p.RangeFrom).HasColumnName("range_from").IsRequired();
            builder.Property(p => p.RangeTo).HasColumnName("range_to");
            builder.Property(p => p.NumberOfPeople).HasColumnName("number_of_people");

            builder.HasOne(p => p.Nutzer).WithMany(a => a.NutzerPersonen).HasForeignKey(f => f.NutzerGuid).HasConstraintName("fk_nutzerpersonen_nutzer_guid");
            builder.Property(p => p.NutzerGuid).HasColumnName("nutzer_guid").IsRequired();
            builder.Property(p => p.IsCreatedByApp).HasColumnName("is_created_by_app").IsRequired();
        }
    }
}
