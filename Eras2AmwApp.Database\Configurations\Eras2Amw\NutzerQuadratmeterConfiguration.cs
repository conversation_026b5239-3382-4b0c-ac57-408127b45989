﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzerQuadratmeterConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//    
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class NutzerQuadratmeterConfiguration : IEntityTypeConfiguration<NutzerQuadratmeter>
    {
        public void Configure(EntityTypeBuilder<NutzerQuadratmeter> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("nutzer_quadratmeter");
            builder.HasKey(p => p.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAddOrUpdate().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.Property(p => p.RangeFrom).HasColumnName("range_from").IsRequired();
            builder.Property(p => p.RangeTo).HasColumnName("range_to");
            builder.Property(p => p.SquareMeters_Hzg).HasColumnName("square_meters_hzg");
            builder.Property(p => p.SquareMeters_Ww).HasColumnName("square_meters_ww");
            builder.Property(p => p.SquareMeters_Nk).HasColumnName("square_meters_nk");

            builder.HasOne(p => p.Nutzer).WithMany(a => a.NutzerQuadratmeter).HasForeignKey(f => f.NutzerGuid).HasConstraintName("fk_nutzerquadratmeter_nutzer_guid");
            builder.Property(p => p.NutzerGuid).HasColumnName("nutzer_guid").IsRequired();
            builder.Property(p => p.IsCreatedByApp).HasColumnName("is_created_by_app").IsRequired();
        }
    }
}
