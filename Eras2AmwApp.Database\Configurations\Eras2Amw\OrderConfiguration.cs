﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="OrderConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class OrderConfiguration : IEntityTypeConfiguration<Order>
    {
        public void Configure(EntityTypeBuilder<Order> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("orders");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Number).HasColumnName("number").IsRequired();
            builder.Property(p => p.Label).HasColumnName("label").IsRequired();
            builder.Property(p => p.IsSignatureRequired).HasColumnName("is_signature_required").IsRequired();
            builder.Property(p => p.Note).HasColumnName("note");
            builder.Property(p => p.CreatedDate).HasColumnName("created_date").IsRequired();
            builder.Property(p => p.IPAddress).HasColumnName("ip_address");

            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAdd().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.HasOne(p => p.Abrechnungseinheit).WithMany(a => a.Orders).HasForeignKey(f => f.AbrechnungseinheitGuid).HasConstraintName("fk_abrechnungseinheiten_orders_order_guid");
            builder.Property(p => p.AbrechnungseinheitGuid).HasColumnName("abrechnungseinheit_guid").IsRequired();

            builder.HasOne(p => p.Orderer).WithMany(a => a.Orders).HasForeignKey(f => f.OrdererGuid).HasConstraintName("fk_orders_orderers_orderer_guid");
            builder.Property(p => p.OrdererGuid).HasColumnName("orderer_guid").IsRequired();

            builder.HasMany(p => p.Appointments).WithOne(a => a.Order).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.OrderPositions).WithOne(a => a.Order).OnDelete(DeleteBehavior.Cascade);
            builder.HasOne(p => p.OrderState).WithOne(a => a.Order).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.DeviceOrderStates).WithOne(a => a.Order).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.NutzeinheitOrderStates).WithOne(a => a.Order).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.Signatures).WithOne(a => a.Order).OnDelete(DeleteBehavior.Cascade);
        }
    }
}