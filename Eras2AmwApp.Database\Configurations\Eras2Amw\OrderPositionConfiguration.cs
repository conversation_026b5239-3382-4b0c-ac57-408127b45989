﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="OrderPositionConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class OrderPositionConfiguration : IEntityTypeConfiguration<OrderPosition>
    {
        public void Configure(EntityTypeBuilder<OrderPosition> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("order_positions");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Note).HasColumnName("note");
            builder.Property(p => p.ActualQuantity).HasColumnName("actual_quantity").IsRequired();
            builder.Property(p => p.RequiredQuantity).HasColumnName("required_quantity").IsRequired();

            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAdd().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.HasOne(p => p.Order).WithMany(a => a.OrderPositions).HasForeignKey(f => f.OrderGuid).HasConstraintName("fk_orders_order_positions_order_guid");
            builder.Property(p => p.OrderGuid).HasColumnName("order_guid").IsRequired();

            builder.HasMany(p => p.NutzeinheitOrderPositions).WithOne(a => a.OrderPosition).OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(p => p.Article).WithMany(a => a.OrderPositions).HasForeignKey(f => f.ArticleGuid).HasConstraintName("fk_articles_order_positions_article_guid");
            builder.Property(p => p.ArticleGuid).HasColumnName("article_guid").IsRequired();
        }
    }
}