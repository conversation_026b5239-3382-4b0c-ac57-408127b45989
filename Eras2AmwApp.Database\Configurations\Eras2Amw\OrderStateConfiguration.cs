﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="OrderStateConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using System.Collections.Generic;
    using Domain.Eras2Amw.Enums;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;
    using Newtonsoft.Json;

    public class OrderStateConfiguration : IEntityTypeConfiguration<OrderState>
    {
        public void Configure(EntityTypeBuilder<OrderState> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("orders_states");
            builder.HasKey(x => x.OrderGuid);
            builder.Property(p => p.OrderGuid).HasColumnName("order_guid").IsRequired();

            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAdd().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.Property(e => e.Kinds).HasColumnName("kinds").HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<IList<OrderKind>>(v)).IsRequired();

            builder.Property(p => p.ProcessState).HasColumnName("process_state").IsRequired();
            builder.Property(p => p.CompletedDate).HasColumnName("completed_date");

            builder.HasOne(p => p.Order).WithOne(a => a.OrderState).HasForeignKey<OrderState>(x => x.OrderGuid).HasConstraintName("fk_orders_orders_states_order_guid");
            builder.Property(p => p.OrderGuid).HasColumnName("order_guid").IsRequired();
        }
    }
}