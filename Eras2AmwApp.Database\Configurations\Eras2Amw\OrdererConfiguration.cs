﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="OrdererConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class OrdererConfiguration : IEntityTypeConfiguration<Orderer>
    {
        public void Configure(EntityTypeBuilder<Orderer> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("orderers");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.BusinessPosition).HasColumnName("business_position").IsRequired();

            builder.HasOne(p => p.Person).WithMany(a => a.Orderers).HasForeignKey(f => f.PersonGuid).HasConstraintName("fk_persons_orderers_person_guid");
            builder.Property(p => p.PersonGuid).HasColumnName("person_guid").IsRequired();

            builder.HasMany(p => p.Orders).WithOne(a => a.Orderer).OnDelete(DeleteBehavior.Cascade);
        }
    }
}