﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="PersonAbrechnungseinheitConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class PersonAbrechnungseinheitConfiguration : IEntityTypeConfiguration<PersonAbrechnungseinheit>
    {
        public void Configure(EntityTypeBuilder<PersonAbrechnungseinheit> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("persons_abrechnungseinheiten");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Note).HasColumnName("note");
            builder.Property(p => p.BusinessPosition).HasColumnName("business_position").IsRequired();
            builder.Property(p => p.IsCreatedByApp).HasColumnName("is_created_by_app").IsRequired();

            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAdd().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.HasOne(p => p.Abrechnungseinheit).WithMany(a => a.PersonAbrechnungseinheiten).HasForeignKey(f => f.AbrechnungseinheitGuid).HasConstraintName("fk_abrechnungseinheiten_persons_abrechnungseinheiten_abrechnungseinheit_guid");
            builder.Property(p => p.AbrechnungseinheitGuid).HasColumnName("abrechnungseinheit_guid").IsRequired();

            builder.HasOne(p => p.Person).WithMany(a => a.PersonAbrechnungseinheiten).HasForeignKey(f => f.PersonGuid).HasConstraintName("fk_persons_persons_abrechnungseinheiten_person_guid");
            builder.Property(p => p.PersonGuid).HasColumnName("person_guid").IsRequired();
        }
    }
}