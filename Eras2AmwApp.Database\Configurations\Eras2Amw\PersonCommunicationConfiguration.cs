﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="PersonCommunicationConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class PersonCommunicationConfiguration : IEntityTypeConfiguration<PersonCommunication>
    {
        public void Configure(EntityTypeBuilder<PersonCommunication> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("persons_communications");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Address).HasColumnName("address").IsRequired();
            builder.Property(p => p.Note).HasColumnName("note");

            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAdd().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.HasOne(p => p.Person).WithMany(a => a.PersonCommunications).HasForeignKey(f => f.PersonGuid).HasConstraintName("fk_persons_persons_communications_person_guid");
            builder.Property(p => p.PersonGuid).HasColumnName("person_guid").IsRequired();

            builder.HasOne(p => p.CommunicationFeature).WithMany(a => a.PersonCommunications).HasForeignKey(f => f.CommunicationFeatureGuid).HasConstraintName("fk_communication_features_persons_communications_communication_feature_guid");
            builder.Property(p => p.CommunicationFeatureGuid).HasColumnName("communication_feature_guid");
        }
    }
}