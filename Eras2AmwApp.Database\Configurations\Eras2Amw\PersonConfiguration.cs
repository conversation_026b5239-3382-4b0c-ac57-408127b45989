﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="PersonConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class PersonConfiguration : IEntityTypeConfiguration<Person>
    {
        public void Configure(EntityTypeBuilder<Person> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("persons");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Note).HasColumnName("note");

            builder.Property(p => p.Lastname1).HasColumnName("lastname_1").IsRequired();
            builder.Property(p => p.Lastname2).HasColumnName("lastname_2");
            builder.Property(p => p.Lastname3).HasColumnName("lastname_3");
            builder.Property(p => p.Firstname1).HasColumnName("firstname_1");
            builder.Property(p => p.Firstname2).HasColumnName("firstname_2");

            builder.Property(p => p.LastModified).HasColumnName("last_modified").ValueGeneratedOnAdd().HasDefaultValueSql("datetime('now', 'localtime')");

            builder.HasOne(p => p.Salutation).WithMany(a => a.Persons).HasForeignKey(f => f.SalutationId).HasConstraintName("fk_salutations_persons_id");
            builder.Property(p => p.SalutationId).HasColumnName("salutation_id").IsRequired();

            builder.HasOne(p => p.Title).WithMany(a => a.Persons).HasForeignKey(f => f.TitleId).HasConstraintName("fk_titles_persons_id");
            builder.Property(p => p.TitleId).HasColumnName("title_id");

            builder.HasMany(p => p.PersonCommunications).WithOne(a => a.Person).OnDelete(DeleteBehavior.Cascade);
            builder.HasMany(p => p.Orderers).WithOne(a => a.Person).OnDelete(DeleteBehavior.Restrict);
        }
    }
}