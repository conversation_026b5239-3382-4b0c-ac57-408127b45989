﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="PersonConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class PhotoConfiguration : IEntityTypeConfiguration<Photo>
    {
        public void Configure(EntityTypeBuilder<Photo> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("photos");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Name).HasColumnName("name");
            builder.Property(p => p.Path).HasColumnName("path");
            builder.Property(p => p.RecordedDate).HasColumnName("recorded_date").IsRequired();
            builder.Property(p => p.CreatedByApp).HasColumnName("created_by_app").IsRequired();

            builder.HasOne(p => p.Nutzeinheit).WithMany(a => a.Photos).HasForeignKey(f => f.NutzeinheitGuid).HasConstraintName("fk_nutzeinheiten_photos_nutzeinheit_guid");
            builder.Property(p => p.NutzeinheitGuid).HasColumnName("nutzeinheit_guid");

            builder.HasOne(p => p.Device).WithMany(a => a.Photos).HasForeignKey(f => f.DeviceGuid).HasConstraintName("fk_devices_photos_device_guid");
            builder.Property(p => p.DeviceGuid).HasColumnName("device_guid");
        }
    }
}