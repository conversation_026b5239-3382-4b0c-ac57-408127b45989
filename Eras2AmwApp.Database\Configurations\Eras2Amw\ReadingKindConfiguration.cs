﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="ReadingKindConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class ReadingKindConfiguration : IEntityTypeConfiguration<ReadingKind>
    {
        public void Configure(EntityTypeBuilder<ReadingKind> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("reading_kinds");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.LabelShort).HasColumnName("label_short").IsRequired();
            builder.Property(p => p.LabelLong).HasColumnName("label_long");
   
            builder.HasMany(p => p.DeviceConsumptions).WithOne(a => a.ReadingKind).OnDelete(DeleteBehavior.Restrict);
        }
    }
}