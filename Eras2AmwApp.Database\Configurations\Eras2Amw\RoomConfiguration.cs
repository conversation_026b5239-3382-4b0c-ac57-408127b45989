﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="RoomConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;

    using Eras2AmwApp.Domain.Eras2Amw.Models;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class RoomConfiguration : IEntityTypeConfiguration<Room>
    {
        public void Configure(EntityTypeBuilder<Room> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("rooms");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").IsRequired();

            builder.Property(p => p.LabelShort).HasColumnName("label_short").IsRequired();
            builder.Property(p => p.Label).HasColumnName("label");
   
            builder.HasMany(p => p.Devices).WithOne(a => a.Room).OnDelete(DeleteBehavior.Restrict);
        }
    }
}