﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="SalutationConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class SalutationConfiguration : IEntityTypeConfiguration<Salutation>
    {
        public void Configure(EntityTypeBuilder<Salutation> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("salutations");
            builder.HasKey(x => x.Id);
            builder.Property(p => p.Id).HasColumnName("id").ValueGeneratedOnAdd();
            builder.Property(p => p.Label).HasColumnName("label").IsRequired();
            
            builder.HasMany(p => p.Customers).WithOne(a => a.Salutation).OnDelete(DeleteBehavior.Restrict);
            builder.HasMany(p => p.Nutzer).WithOne(a => a.Salutation).OnDelete(DeleteBehavior.Restrict);
            builder.HasMany(p => p.Persons).WithOne(a => a.Salutation).OnDelete(DeleteBehavior.Restrict);
        }
    }
}