﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="PersonConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class SignatureConfiguration : IEntityTypeConfiguration<Signature>
    {
        public void Configure(EntityTypeBuilder<Signature> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("signatures");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid").ValueGeneratedOnAdd();

            builder.Property(p => p.Path).HasColumnName("path");
            builder.Property(p => p.RecordedDate).HasColumnName("recorded_date").IsRequired();
            builder.Property(p => p.Kind).HasColumnName("signature_kind").IsRequired();
            builder.Property(p => p.CreatedByApp).HasColumnName("created_by_app").IsRequired();

            builder.HasOne(p => p.Nutzeinheit).WithMany(a => a.Signatures).HasForeignKey(f => f.NutzeinheitGuid).HasConstraintName("fk_nutzeinheiten_signatures_signature_guid");
            builder.Property(p => p.NutzeinheitGuid).HasColumnName("nutzeinheit_guid").IsRequired();

            builder.HasOne(p => p.Order).WithMany(a => a.Signatures).HasForeignKey(f => f.OrderGuid).HasConstraintName("fk_orders_signatures_signature_guid");
            builder.Property(p => p.OrderGuid).HasColumnName("order_guid").IsRequired();
        }
    }
}