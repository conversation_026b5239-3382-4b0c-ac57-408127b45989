﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="TitleConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2Amw
{
    using System;
    using Domain.Eras2Amw.Models;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class TitleConfiguration : IEntityTypeConfiguration<Title>
    {
        public void Configure(EntityTypeBuilder<Title> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("titles");
            builder.HasKey(x => x.Id);
            builder.Property(p => p.Id).HasColumnName("id").ValueGeneratedOnAdd();

            builder.Property(p => p.Label).HasColumnName("label").IsRequired();
            builder.Property(p => p.Note).HasColumnName("note");
            
            builder.HasMany(p => p.Nutzer).WithOne(a => a.Title).OnDelete(DeleteBehavior.Restrict);
            builder.HasMany(p => p.Persons).WithOne(a => a.Title).OnDelete(DeleteBehavior.Restrict);
        }
    }
}