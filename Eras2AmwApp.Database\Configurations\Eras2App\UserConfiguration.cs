﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AddressConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2App
{
    using System;
    using Domain.Eras2App.Database;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class UserConfiguration : IEntityTypeConfiguration<User>
    {
        public void Configure(EntityTypeBuilder<User> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("users");
            builder.HasKey(x => x.Guid);
            builder.Property(p => p.Guid).HasColumnName("guid");

            builder.Property(p => p.Name).HasColumnName("name").IsRequired();
            builder.Property(p => p.Password).HasColumnName("password").IsRequired();
            builder.Property(p => p.Email).HasColumnName("email");
            builder.Property(p => p.LastWebserviceLoginDate).HasColumnName("last_webservice_login_date").IsRequired();
            builder.Property(p => p.LastWebserviceSyncDate).HasColumnName("last_webservice_sync_date");
            
            builder.HasOne(p => p.Customer).WithMany(a => a.Users).HasForeignKey(f => f.CustomerId).HasConstraintName("fk_customers_users_customer_id");
            builder.Property(p => p.CustomerId).HasColumnName("customer_id").IsRequired();
            builder.Property(p => p.IsLiveSyncEnabled).HasColumnName("is_live_sync_enabled").IsRequired();
        }
    }
}