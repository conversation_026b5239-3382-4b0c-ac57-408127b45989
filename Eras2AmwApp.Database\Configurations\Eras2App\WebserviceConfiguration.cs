﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AddressConfiguration.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Configurations.Eras2App
{
    using System;
    using Domain.Eras2App.Database;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.EntityFrameworkCore.Metadata.Builders;

    public class WebserviceConfiguration : IEntityTypeConfiguration<Webservice>
    {
        public void Configure(EntityTypeBuilder<Webservice> builder)
        {
            if (builder == null)
            {
                throw new ArgumentNullException(nameof(builder));
            }

            builder.ToTable("webservices");
            builder.HasKey(x => x.Id);
            builder.Property(p => p.Id).HasColumnName("id");

            builder.HasOne(p => p.Customer).WithOne(a => a.Webservice).HasForeignKey<Webservice>(x => x.CustomerId).HasConstraintName("fk_customers_webservices_customer_id");
            builder.Property(p => p.CustomerId).HasColumnName("customer_id").IsRequired();
        }
    }
}