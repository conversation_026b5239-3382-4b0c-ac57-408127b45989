﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Eras2AmwContext.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Contexts
{
    using System;
    using Common.Interfaces;
    using Configurations.Eras2Amw;
    using Domain.Eras2Amw.Models;
    using Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class Eras2AmwContext : AbstractContext
    {
        private readonly IDatabaseSeeder seeder;

        internal Eras2AmwContext(IAppSettings appSettings, IDatabaseSeeder seeder = null)
            : base(appSettings)
        {
            this.seeder = seeder;
        }

        public DbSet<Customer> Customers { get; set; }

        public DbSet<Address> Addresses { get; set; }

        public DbSet<Person> Persons { get; set; }

        public DbSet<Nutzer> Nutzer { get; set; }

        public DbSet<NutzerPersonen> NutzerPersonen { get; set; }

        public DbSet<NutzerQuadratmeter> NutzerQuadratmeter { get; set; }

        public DbSet<NutzerCoOwnership> NutzerCoOwnership { get; set; }

        public DbSet<NutzerCommunication> NutzerCommunication { get; set; }

        public DbSet<Nutzeinheit> Nutzeinheiten { get; set; }

        public DbSet<NutzeinheitOrderState> NutzeinheitOrderStates { get; set; }

        public DbSet<Abrechnungseinheit> Abrechnungseinheiten { get; set; }

        public DbSet<Orderer> Orderer { get; set; }

        public DbSet<Order> Orders { get; set; }

        public DbSet<Salutation> Salutations { get; set; }

        public DbSet<Title> Titles { get; set; }

        public DbSet<Device> Devices { get; set; }

        public DbSet<DeviceOrderState> DeviceOrderStates { get; set; }

        public DbSet<DeviceConsumption> DeviceConsumptions { get; set; }

        public DbSet<ReadingKind> ReadingKinds { get; set; }

        public DbSet<DeviceCatalog> DeviceCatalog { get; set; }

        public DbSet<CommunicationFeature> CommunicationFeatures { get; set; }

        public DbSet<PersonAbrechnungseinheit> PersonAbrechnungseinheiten { get; set; }

        public DbSet<PersonCommunication> PersonCommunications { get; set; }

        public DbSet<Appointment> Appointments { get; set; }

        public DbSet<AppointmentTechnician> AppointmentUsers { get; set; }

        public DbSet<AppointmentNutzeinheit> AppointmentNutzeinheiten { get; set; }

        public DbSet<OrderPosition> OrderPositions { get; set; }

        public DbSet<Article> Articles { get; set; }

        public DbSet<Photo> Photos { get; set; }

        public DbSet<Signature> Signatures { get; set; }

        public DbSet<Manufacturer> Manufacturers { get; set; }

        public DbSet<DeviceKind> DeviceKinds { get; set; }

        public DbSet<AmwInfoKey> AmwInfoKeys { get; set; }

        public DbSet<AdditionalArticle> AdditionalArticles { get; set; }

        public DbSet<DeviceAdditionalArticle> DeviceAdditionalArticles { get; set; }

        public DbSet<Room> Rooms { get; set; }

        protected override string DatabaseName { get; set; } = "eras2_amw.sqlite3";

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            if (modelBuilder == null)
            {
                throw new ArgumentNullException(nameof(modelBuilder));
            }

            base.OnModelCreating(modelBuilder);

            modelBuilder.ApplyConfiguration(new CustomerConfiguration());
            modelBuilder.ApplyConfiguration(new TitleConfiguration());
            modelBuilder.ApplyConfiguration(new SalutationConfiguration());
            modelBuilder.ApplyConfiguration(new OrderConfiguration());
            modelBuilder.ApplyConfiguration(new OrdererConfiguration());
            modelBuilder.ApplyConfiguration(new OrderStateConfiguration());
            modelBuilder.ApplyConfiguration(new OrderPositionConfiguration());

            modelBuilder.ApplyConfiguration(new AppointmentConfiguration());
            modelBuilder.ApplyConfiguration(new AppointmentTechnicianConfiguration());
            modelBuilder.ApplyConfiguration(new AppointmentNutzeinheitConfiguration());
            modelBuilder.ApplyConfiguration(new AbrechnungseinheitConfiguration());
            modelBuilder.ApplyConfiguration(new NutzeinheitConfiguration());
            modelBuilder.ApplyConfiguration(new NutzeinheitOrderPositionConfiguration());
            modelBuilder.ApplyConfiguration(new NutzeinheitOrderStateConfiguration());

            modelBuilder.ApplyConfiguration(new SignatureConfiguration());
            modelBuilder.ApplyConfiguration(new PhotoConfiguration());

            modelBuilder.ApplyConfiguration(new NutzerConfiguration());
            modelBuilder.ApplyConfiguration(new NutzerPersonenConfiguration());
            modelBuilder.ApplyConfiguration(new NutzerQuadratmeterConfiguration());
            modelBuilder.ApplyConfiguration(new NutzerCoOwnershipConfiguration());

            modelBuilder.ApplyConfiguration(new AddressConfiguration());
            modelBuilder.ApplyConfiguration(new DeviceConfiguration());
            modelBuilder.ApplyConfiguration(new DeviceConsumptionConfiguration());
            modelBuilder.ApplyConfiguration(new DeviceAdditionalArticleConfiguration());
            modelBuilder.ApplyConfiguration(new DeviceOrderStateConfiguration());

            modelBuilder.ApplyConfiguration(new NutzerCommunicationConfiguration());
            modelBuilder.ApplyConfiguration(new CommunicationFeatureConfiguration());
            modelBuilder.ApplyConfiguration(new PersonConfiguration());
            modelBuilder.ApplyConfiguration(new PersonCommunicationConfiguration());
            modelBuilder.ApplyConfiguration(new PersonAbrechnungseinheitConfiguration());

            modelBuilder.ApplyConfiguration(new DeviceCatalogConfiguration());
            modelBuilder.ApplyConfiguration(new DeviceKindConfiguration());
            modelBuilder.ApplyConfiguration(new ManufacturerConfiguration());
            modelBuilder.ApplyConfiguration(new ArticleConfiguration());
            modelBuilder.ApplyConfiguration(new ReadingKindConfiguration());
            modelBuilder.ApplyConfiguration(new AmwInfoKeyConfiguration());
            modelBuilder.ApplyConfiguration(new AdditionalArticleConfiguration());
            modelBuilder.ApplyConfiguration(new RoomConfiguration());

            seeder?.Seed(modelBuilder);
        }
    }
}