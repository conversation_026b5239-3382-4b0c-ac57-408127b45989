﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AppContext.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Contexts
{
    using System;
    using Common.Interfaces;
    using Configurations.Eras2App;
    using Domain.Eras2App.Database;
    using Microsoft.EntityFrameworkCore;

    public class Eras2AppContext : AbstractContext
    {
        internal Eras2AppContext(IAppSettings appSettings)
            : base(appSettings)
        {
        }

        public DbSet<User> Users { get; set; }

        public DbSet<Webservice> Webservices { get; set; }

        public DbSet<Customer> Customers { get; set; }

        protected override string DatabaseName { get; set; } = "app.sqlite3";

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            if (modelBuilder == null)
            {
                throw new ArgumentNullException(nameof(modelBuilder));
            }

            base.OnModelCreating(modelBuilder);

            modelBuilder.ApplyConfiguration(new UserConfiguration());
            modelBuilder.ApplyConfiguration(new WebserviceConfiguration());
            modelBuilder.ApplyConfiguration(new CustomerConfiguration());
        }
    }
}