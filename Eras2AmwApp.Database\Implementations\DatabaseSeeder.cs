﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DatabaseSeeder.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Implementations
{
    using System;
    using Domain.Eras2Amw.Models;
    using Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class DatabaseSeeder : IDatabaseSeeder
    {
        public void Seed(ModelBuilder modelBuilder)
        {
            if (modelBuilder == null)
            {
                throw new ArgumentNullException(nameof(modelBuilder));
            }

            modelBuilder.Entity<Title>().HasData(
                new { Id = 1, Label = "Dr." },
                new { Id = 2, Label = "Prof." }
            );

            modelBuilder.Entity<Salutation>().HasData(
                new { Id = 1, Label = "Herr/Frau/Firma" },
                new { Id = 2, Label = "Herr/Frau" },
                new { Id = 3, Label = "Herr" },
                new { Id = 4, Label = "Frau" },
                new { Id = 5, Label = "Firma" },
                new { Id = 6, Label = string.Empty }
            );
        }
    }
}