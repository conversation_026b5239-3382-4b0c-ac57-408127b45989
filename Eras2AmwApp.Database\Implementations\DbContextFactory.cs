﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DatabaseContextFactory.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Implementations
{
    using Common.Interfaces;
    using Contexts;
    using Interfaces;

    public class DbContextFactory : IDbContextFactory
    {
        private readonly IAppSettings appSettings;

        public DbContextFactory(IAppSettings appSettings)
        {
            this.appSettings = appSettings;
        }

        public Eras2AmwContext CreateAmw() => new Eras2AmwContext(appSettings);

        public Eras2AmwContext CreateAmw(IDatabaseSeeder seeder) => new Eras2AmwContext(appSettings, seeder);

        public Eras2AppContext CreateApp() => new Eras2AppContext(appSettings);
    }
}