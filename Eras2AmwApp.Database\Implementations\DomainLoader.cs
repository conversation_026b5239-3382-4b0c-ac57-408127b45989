﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DomainLoader.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Implementations
{
    using System;
    using System.Linq;
    using Contexts;
    using Domain.Eras2Amw.Models;
    using Interfaces;
    using Microsoft.EntityFrameworkCore;

    public class DomainLoader : IDomainLoader
    {
        private readonly IDbContextFactory contextFactory;

        public DomainLoader(IDbContextFactory contextFactory)
        {
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
        }

        public Abrechnungseinheit LoadAbrechnungseinheit(Guid guid)
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Abrechnungseinheiten
                    .Include(x => x.Address)
                    .Include(x => x.Customer)
                        .ThenInclude(x => x.Salutation)
                    .Include(x => x.PersonAbrechnungseinheiten)
                        .ThenInclude(x => x.Person)
                            .ThenInclude(x => x.Salutation)
                    .Include(x => x.PersonAbrechnungseinheiten)
                        .ThenInclude(x => x.Person)
                            .ThenInclude(x => x.Title)
                    .Include(x => x.PersonAbrechnungseinheiten)
                        .ThenInclude(x => x.Person)
                            .ThenInclude(x => x.PersonCommunications)
                    .Single(x => x.Guid == guid);
            }
        }

        public Nutzeinheit LoadNutzeinheitWithDevices(Guid guid)
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Nutzeinheiten
                    .Include(x => x.Address)
                    .Include(x => x.Nutzer)
                        .ThenInclude(x => x.NutzerCommunications)
                    .Include(x => x.Nutzer)
                        .ThenInclude(x => x.Salutation)
                    .Include(x => x.Nutzer)
                        .ThenInclude(x => x.NutzerPersonen)
                    .Include(x => x.Nutzer)
                        .ThenInclude(x => x.NutzerQuadratmeter)
                    .Include(x => x.Nutzer)
                        .ThenInclude(x => x.NutzerCoOwnership)
                    .Include(x => x.Nutzer)
                        .ThenInclude(x => x.Title)                        
                    .Include(x => x.Devices)
                        .ThenInclude(x => x.DeviceConsumptions)
                    .Include(x => x.Devices)
                        .ThenInclude(x => x.Photos)
                    .Include(x => x.Devices)
                        .ThenInclude(x => x.DeviceAdditionalArticles)
                    .Include(x => x.Photos)
                    .Include(x => x.Signatures)
                    .Single(x => x.Guid == guid);
            }
        }

        public Nutzeinheit LoadNutzeinheit(Guid guid)
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Nutzeinheiten
                    .Include(x => x.Address)
                    .Include(x => x.Nutzer)
                        .ThenInclude(x => x.NutzerCommunications)
                    .Include(x => x.Nutzer)
                        .ThenInclude(x => x.NutzerPersonen)
                    .Include(x => x.Nutzer)
                        .ThenInclude(x => x.NutzerQuadratmeter)
                    .Include(x => x.Nutzer)
                        .ThenInclude(x => x.NutzerCoOwnership)
                    .Include(x => x.Nutzer)
                        .ThenInclude(x => x.Salutation)
                    .Include(x => x.Nutzer)
                        .ThenInclude(x => x.Title)      
                    .Include(x => x.Photos)
                    .Include(x => x.Signatures)
                    .Single(x => x.Guid == guid);
            }
        }

        public Order LoadOrder(Guid guid)
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                return context.Orders
                    .Include(x => x.Orderer)
                        .ThenInclude(x => x.Person)
                            .ThenInclude(x => x.Title)
                    .Include(x => x.Orderer)
                        .ThenInclude(x => x.Person)
                            .ThenInclude(x => x.Salutation)
                    .Include(x => x.OrderState)
                    .Include(x => x.Appointments)
                        .ThenInclude(x => x.AppointmentStoreUsers)
                    .Include(x => x.Appointments)
                            .ThenInclude(x => x.AppointmentNutzeinheiten)
                    .Include(x => x.OrderPositions)
                        .ThenInclude(x => x.NutzeinheitOrderPositions)
                    .Include(x => x.NutzeinheitOrderStates)
                    .Include(x => x.DeviceOrderStates)
                    .Include(x => x.Signatures)
                    .Single(x => x.Guid == guid);
            }
        }
    }
}