﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IDatabaseContextFactory.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Database.Interfaces
{
    using Contexts;

    public interface IDbContextFactory
    {
        Eras2AmwContext CreateAmw();

        Eras2AppContext CreateApp();

        Eras2AmwContext CreateAmw(IDatabaseSeeder seeder);
    }
}