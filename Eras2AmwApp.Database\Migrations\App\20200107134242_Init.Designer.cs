﻿// <auto-generated />
using System;
using Eras2AmwApp.Database.Contexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace Eras2AmwApp.Database.Migrations.App
{
    [DbContext(typeof(Eras2AppContext))]
    [Migration("20200107134242_Init")]
    partial class Init
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "2.2.6-servicing-10079");

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2App.Database.Customer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnName("name");

                    b.<PERSON>("Id");

                    b.ToTable("customers");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2App.Database.User", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<int>("CustomerId")
                        .HasColumnName("customer_id");

                    b.Property<string>("Email")
                        .HasColumnName("email");

                    b.Property<DateTime>("LastWebserviceLoginDate")
                        .HasColumnName("last_webservice_login_date");

                    b.Property<DateTime?>("LastWebserviceSyncDate")
                        .HasColumnName("last_webservice_sync_date");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnName("name");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnName("password");

                    b.HasKey("Guid");

                    b.HasIndex("CustomerId");

                    b.ToTable("users");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2App.Database.Webservice", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("id");

                    b.Property<int>("CustomerId")
                        .HasColumnName("customer_id");

                    b.Property<string>("Url");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId")
                        .IsUnique();

                    b.ToTable("webservices");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2App.Database.User", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2App.Database.Customer", "Customer")
                        .WithMany("Users")
                        .HasForeignKey("CustomerId")
                        .HasConstraintName("fk_customers_users_customer_id")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2App.Database.Webservice", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2App.Database.Customer", "Customer")
                        .WithOne("Webservice")
                        .HasForeignKey("Eras2AmwApp.Domain.Eras2App.Database.Webservice", "CustomerId")
                        .HasConstraintName("fk_customers_webservices_customer_id")
                        .OnDelete(DeleteBehavior.Cascade);
                });
#pragma warning restore 612, 618
        }
    }
}
