﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Eras2AmwApp.Database.Migrations.Eras2Amw
{
    public partial class Init : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "additional_articles",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    number = table.Column<string>(nullable: false),
                    label = table.Column<string>(nullable: true),
                    note = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_additional_articles", x => x.guid);
                });

            migrationBuilder.CreateTable(
                name: "addresses",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    street = table.Column<string>(nullable: true),
                    street2 = table.Column<string>(nullable: true),
                    street_number = table.Column<string>(nullable: true),
                    zipcode = table.Column<string>(nullable: true),
                    city = table.Column<string>(nullable: false),
                    mailbox = table.Column<string>(nullable: true),
                    additional = table.Column<string>(nullable: true),
                    latitude = table.Column<string>(nullable: false),
                    longitude = table.Column<string>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_addresses", x => x.guid);
                });

            migrationBuilder.CreateTable(
                name: "amw_info_keys",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    key = table.Column<int>(nullable: false),
                    info = table.Column<string>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_amw_info_keys", x => x.guid);
                });

            migrationBuilder.CreateTable(
                name: "articles",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    number = table.Column<string>(nullable: false),
                    label = table.Column<string>(nullable: false),
                    note = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_articles", x => x.guid);
                });

            migrationBuilder.CreateTable(
                name: "communication_features",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    feature = table.Column<string>(nullable: false),
                    note = table.Column<string>(nullable: true),
                    kind = table.Column<int>(nullable: false),
                    is_preset = table.Column<bool>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_communication_features", x => x.guid);
                });

            migrationBuilder.CreateTable(
                name: "device_kinds",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    label_long = table.Column<string>(nullable: true),
                    label_short = table.Column<string>(nullable: false),
                    readable = table.Column<bool>(nullable: false),
                    @class = table.Column<int>(name: "class", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_device_kinds", x => x.guid);
                });

            migrationBuilder.CreateTable(
                name: "manufacturers",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    name = table.Column<string>(nullable: false),
                    note = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_manufacturers", x => x.guid);
                });

            migrationBuilder.CreateTable(
                name: "reading_kinds",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    label_short = table.Column<string>(nullable: false),
                    label_long = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_reading_kinds", x => x.guid);
                });

            migrationBuilder.CreateTable(
                name: "rooms",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    label_short = table.Column<string>(nullable: false),
                    label = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_rooms", x => x.guid);
                });

            migrationBuilder.CreateTable(
                name: "salutations",
                columns: table => new
                {
                    id = table.Column<int>(nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    label = table.Column<string>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_salutations", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "titles",
                columns: table => new
                {
                    id = table.Column<int>(nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    label = table.Column<string>(nullable: false),
                    note = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_titles", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "device_catalogs",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    article_name = table.Column<string>(nullable: false),
                    article_number = table.Column<string>(nullable: false),
                    article_description = table.Column<string>(nullable: true),
                    article_number_manufacturer = table.Column<string>(nullable: true),
                    label = table.Column<string>(nullable: true),
                    subtraction = table.Column<bool>(nullable: false),
                    hkvv = table.Column<bool>(nullable: false),
                    radio = table.Column<bool>(nullable: false),
                    order_code = table.Column<string>(nullable: true),
                    installation_length = table.Column<string>(nullable: true),
                    connector = table.Column<string>(nullable: true),
                    diameter = table.Column<string>(nullable: true),
                    sontex_radio_identifier = table.Column<string>(nullable: true),
                    sontex_hkv_identifier = table.Column<string>(nullable: true),
                    note = table.Column<string>(nullable: true),
                    device_kind_guid = table.Column<Guid>(nullable: false),
                    manufacturer_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_device_catalogs", x => x.guid);
                    table.ForeignKey(
                        name: "fk_device_kinds_devices_catologs_device_kind_guid",
                        column: x => x.device_kind_guid,
                        principalTable: "device_kinds",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_manufacturers_devices_catologs_manufacturer_guid",
                        column: x => x.manufacturer_guid,
                        principalTable: "manufacturers",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "customers",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    name = table.Column<string>(nullable: false),
                    name_2 = table.Column<string>(nullable: true),
                    name_3 = table.Column<string>(nullable: true),
                    number = table.Column<string>(nullable: false),
                    note = table.Column<string>(nullable: true),
                    salutation_id = table.Column<int>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_customers", x => x.guid);
                    table.ForeignKey(
                        name: "fk_salutations_customers_salutation_id",
                        column: x => x.salutation_id,
                        principalTable: "salutations",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "persons",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    lastname_1 = table.Column<string>(nullable: false),
                    lastname_2 = table.Column<string>(nullable: true),
                    lastname_3 = table.Column<string>(nullable: true),
                    firstname_1 = table.Column<string>(nullable: true),
                    firstname_2 = table.Column<string>(nullable: true),
                    note = table.Column<string>(nullable: true),
                    salutation_id = table.Column<int>(nullable: false),
                    title_id = table.Column<int>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_persons", x => x.guid);
                    table.ForeignKey(
                        name: "fk_salutations_persons_id",
                        column: x => x.salutation_id,
                        principalTable: "salutations",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_titles_persons_id",
                        column: x => x.title_id,
                        principalTable: "titles",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "abrechnungseinheiten",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    ip_address = table.Column<string>(nullable: true),
                    last_modified = table.Column<DateTime>(nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    number = table.Column<string>(nullable: false),
                    note = table.Column<string>(nullable: true),
                    note_internal = table.Column<string>(nullable: true),
                    note_legionella = table.Column<string>(nullable: true),
                    customer_guid = table.Column<Guid>(nullable: false),
                    address_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_abrechnungseinheiten", x => x.guid);
                    table.ForeignKey(
                        name: "fk_addresses_abrechnungseinheiten_address_guid",
                        column: x => x.address_guid,
                        principalTable: "addresses",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_customers_abrechnungseinheiten_customer_guid",
                        column: x => x.customer_guid,
                        principalTable: "customers",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "orderers",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    person_guid = table.Column<Guid>(nullable: false),
                    business_position = table.Column<string>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_orderers", x => x.guid);
                    table.ForeignKey(
                        name: "fk_persons_orderers_person_guid",
                        column: x => x.person_guid,
                        principalTable: "persons",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "persons_communications",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    address = table.Column<string>(nullable: false),
                    note = table.Column<string>(nullable: true),
                    person_guid = table.Column<Guid>(nullable: false),
                    communication_feature_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_persons_communications", x => x.guid);
                    table.ForeignKey(
                        name: "fk_communication_features_persons_communications_communication_feature_guid",
                        column: x => x.communication_feature_guid,
                        principalTable: "communication_features",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_persons_persons_communications_person_guid",
                        column: x => x.person_guid,
                        principalTable: "persons",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "nutzeinheiten",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    ip_address = table.Column<string>(nullable: true),
                    last_modified = table.Column<DateTime>(nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    number = table.Column<string>(nullable: false),
                    location = table.Column<string>(nullable: true),
                    walk_sequence = table.Column<string>(nullable: true),
                    note = table.Column<string>(nullable: true),
                    is_created_by_app = table.Column<bool>(nullable: false),
                    abrechnungseinheit_guid = table.Column<Guid>(nullable: false),
                    address_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_nutzeinheiten", x => x.guid);
                    table.ForeignKey(
                        name: "fk_abrechnungseinheiten_nutzeinheiten_abrechnungseinheit_guid",
                        column: x => x.abrechnungseinheit_guid,
                        principalTable: "abrechnungseinheiten",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_addresses_nutzeinheiten_address_guid",
                        column: x => x.address_guid,
                        principalTable: "addresses",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "persons_abrechnungseinheiten",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    note = table.Column<string>(nullable: true),
                    business_position = table.Column<string>(nullable: false),
                    is_created_by_app = table.Column<bool>(nullable: false),
                    abrechnungseinheit_guid = table.Column<Guid>(nullable: false),
                    person_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_persons_abrechnungseinheiten", x => x.guid);
                    table.ForeignKey(
                        name: "fk_abrechnungseinheiten_persons_abrechnungseinheiten_abrechnungseinheit_guid",
                        column: x => x.abrechnungseinheit_guid,
                        principalTable: "abrechnungseinheiten",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_persons_persons_abrechnungseinheiten_person_guid",
                        column: x => x.person_guid,
                        principalTable: "persons",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "orders",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    ip_address = table.Column<string>(nullable: true),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    number = table.Column<string>(nullable: false),
                    label = table.Column<string>(nullable: false),
                    note = table.Column<string>(nullable: true),
                    is_signature_required = table.Column<bool>(nullable: false),
                    created_date = table.Column<string>(nullable: false),
                    abrechnungseinheit_guid = table.Column<Guid>(nullable: false),
                    orderer_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_orders", x => x.guid);
                    table.ForeignKey(
                        name: "fk_abrechnungseinheiten_orders_order_guid",
                        column: x => x.abrechnungseinheit_guid,
                        principalTable: "abrechnungseinheiten",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_orders_orderers_orderer_guid",
                        column: x => x.orderer_guid,
                        principalTable: "orderers",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "devices",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    number = table.Column<string>(nullable: false),
                    label = table.Column<string>(nullable: true),
                    description = table.Column<string>(nullable: true),
                    installation_date = table.Column<DateTime>(nullable: false),
                    deinstallation_date = table.Column<DateTime>(nullable: true),
                    calibration_date = table.Column<DateTime>(nullable: true),
                    unit = table.Column<int>(nullable: true),
                    note = table.Column<string>(nullable: true),
                    subtraction = table.Column<bool>(nullable: false),
                    group_identifier = table.Column<string>(maxLength: 5, nullable: true),
                    estimation = table.Column<int>(nullable: false),
                    first_activation = table.Column<DateTime>(nullable: true),
                    firmware_version = table.Column<string>(nullable: true),
                    nutzeinheit_guid = table.Column<Guid>(nullable: false),
                    room_guid = table.Column<Guid>(nullable: false),
                    ongoing_number = table.Column<string>(nullable: false),
                    is_leased = table.Column<bool>(nullable: false),
                    is_maintained = table.Column<bool>(nullable: false),
                    is_created_by_app = table.Column<bool>(nullable: false),
                    device_catalog_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_devices", x => x.guid);
                    table.ForeignKey(
                        name: "fk_device_catalogs_devices_device_catalog_guid",
                        column: x => x.device_catalog_guid,
                        principalTable: "device_catalogs",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_nutzeinheiten_devices_nutzeinheit_guid",
                        column: x => x.nutzeinheit_guid,
                        principalTable: "nutzeinheiten",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_rooms_devices_room_guid",
                        column: x => x.room_guid,
                        principalTable: "rooms",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "nutzer",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    kind = table.Column<int>(nullable: false),
                    move_in_date = table.Column<DateTime>(nullable: false),
                    move_out_date = table.Column<DateTime>(nullable: true),
                    name1 = table.Column<string>(nullable: true),
                    name2 = table.Column<string>(nullable: true),
                    name3 = table.Column<string>(nullable: true),
                    name4 = table.Column<string>(nullable: true),
                    note = table.Column<string>(nullable: true),
                    next_appointment_date = table.Column<DateTime>(nullable: true),
                    is_created_by_app = table.Column<bool>(nullable: false),
                    nutzeinheit_guid = table.Column<Guid>(nullable: false),
                    title_id = table.Column<int>(nullable: true),
                    salutation_id = table.Column<int>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_nutzer", x => x.guid);
                    table.ForeignKey(
                        name: "fk_nutzeinheiten_nutzer_nutzeinheit_guid",
                        column: x => x.nutzeinheit_guid,
                        principalTable: "nutzeinheiten",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_salutations_nutzer_salutation_id",
                        column: x => x.salutation_id,
                        principalTable: "salutations",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_titles_nutzer_title_id",
                        column: x => x.title_id,
                        principalTable: "titles",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "appointments",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    from = table.Column<DateTime>(nullable: false),
                    to = table.Column<DateTime>(nullable: false),
                    order_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_appointments", x => x.guid);
                    table.ForeignKey(
                        name: "fk_appointments_orders_order_guid",
                        column: x => x.order_guid,
                        principalTable: "orders",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "nutzeinheiten_order_states",
                columns: table => new
                {
                    nutzeinheit_guid = table.Column<Guid>(nullable: false),
                    order_guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    order_kinds = table.Column<string>(nullable: true),
                    process_state = table.Column<int>(nullable: false),
                    completed_date = table.Column<DateTime>(nullable: true),
                    os_version = table.Column<string>(nullable: true),
                    app_version = table.Column<string>(nullable: true),
                    os_platform = table.Column<string>(nullable: true),
                    app_device_number = table.Column<string>(nullable: true),
                    amw_info_key_guid = table.Column<Guid>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_nutzeinheiten_order_states", x => new { x.order_guid, x.nutzeinheit_guid });
                    table.ForeignKey(
                        name: "fk_amw_info_keys_nutzeinheit_order_states_amw_info_key_guid",
                        column: x => x.amw_info_key_guid,
                        principalTable: "amw_info_keys",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_nutzeinheiten_nutzeinheiten_order_states_nutzeinheit_guid",
                        column: x => x.nutzeinheit_guid,
                        principalTable: "nutzeinheiten",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_orders_nutzeinheiten_order_states_order_guid",
                        column: x => x.order_guid,
                        principalTable: "orders",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "order_positions",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    note = table.Column<string>(nullable: true),
                    required_quantity = table.Column<int>(nullable: false),
                    actual_quantity = table.Column<int>(nullable: false),
                    order_guid = table.Column<Guid>(nullable: false),
                    article_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_order_positions", x => x.guid);
                    table.ForeignKey(
                        name: "fk_articles_order_positions_article_guid",
                        column: x => x.article_guid,
                        principalTable: "articles",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_orders_order_positions_order_guid",
                        column: x => x.order_guid,
                        principalTable: "orders",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "orders_states",
                columns: table => new
                {
                    order_guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    kinds = table.Column<string>(nullable: false),
                    process_state = table.Column<int>(nullable: false),
                    completed_date = table.Column<DateTime>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_orders_states", x => x.order_guid);
                    table.ForeignKey(
                        name: "fk_orders_orders_states_order_guid",
                        column: x => x.order_guid,
                        principalTable: "orders",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "signatures",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    path = table.Column<string>(nullable: true),
                    signature_kind = table.Column<int>(nullable: false),
                    created_by_app = table.Column<bool>(nullable: false),
                    recorded_date = table.Column<DateTime>(nullable: false),
                    nutzeinheit_guid = table.Column<Guid>(nullable: false),
                    order_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_signatures", x => x.guid);
                    table.ForeignKey(
                        name: "fk_nutzeinheiten_signatures_signature_guid",
                        column: x => x.nutzeinheit_guid,
                        principalTable: "nutzeinheiten",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_orders_signatures_signature_guid",
                        column: x => x.order_guid,
                        principalTable: "orders",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "device_consumptions",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    reading_date = table.Column<DateTime>(nullable: true),
                    factor = table.Column<double>(nullable: false),
                    reading = table.Column<double>(nullable: true),
                    consumption = table.Column<double>(nullable: true),
                    change_date = table.Column<DateTime>(nullable: true),
                    estimation_kind = table.Column<int>(nullable: false),
                    state = table.Column<int>(nullable: false),
                    note = table.Column<string>(nullable: true),
                    is_reconstructed = table.Column<bool>(nullable: false),
                    manual = table.Column<double>(nullable: true),
                    origin = table.Column<int>(nullable: false),
                    air_temperature = table.Column<decimal>(type: "decimal(4,1)", nullable: true),
                    radiator_temperature = table.Column<decimal>(type: "decimal(5,1)", nullable: true),
                    last_error_date = table.Column<DateTime>(nullable: true),
                    error = table.Column<string>(nullable: true),
                    ampoule_colour = table.Column<string>(nullable: true),
                    device_guid = table.Column<Guid>(nullable: false),
                    readind_kind_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_device_consumptions", x => x.guid);
                    table.ForeignKey(
                        name: "fk_devices_devices_consumptions_device_guid",
                        column: x => x.device_guid,
                        principalTable: "devices",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_reading_kinds_device_consumptions_reading_kind_guid",
                        column: x => x.readind_kind_guid,
                        principalTable: "reading_kinds",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "devices_additional_articles",
                columns: table => new
                {
                    device_guid = table.Column<Guid>(nullable: false),
                    additional_article_guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    quantity = table.Column<decimal>(type: "decimal(8, 2)", nullable: false),
                    is_created_by_app = table.Column<bool>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_devices_additional_articles", x => new { x.device_guid, x.additional_article_guid });
                    table.ForeignKey(
                        name: "fk_additional_articles_devices_additional_articles_additional_article_guid",
                        column: x => x.additional_article_guid,
                        principalTable: "additional_articles",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_devices_devices_additional_articles_device_guid",
                        column: x => x.device_guid,
                        principalTable: "devices",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "devices_order_states",
                columns: table => new
                {
                    device_guid = table.Column<Guid>(nullable: false),
                    order_guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    order_kind = table.Column<int>(nullable: false),
                    process_state = table.Column<int>(nullable: false),
                    completed_date = table.Column<DateTime>(nullable: true),
                    amw_info_key_guid = table.Column<Guid>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_devices_order_states", x => new { x.order_guid, x.device_guid });
                    table.ForeignKey(
                        name: "fk_amw_info_keys_device_order_states_amw_info_key_guid",
                        column: x => x.amw_info_key_guid,
                        principalTable: "amw_info_keys",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_devices_devices_order_states_device_guid",
                        column: x => x.device_guid,
                        principalTable: "devices",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_orders_devices_order_states_order_guid",
                        column: x => x.order_guid,
                        principalTable: "orders",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "photos",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    name = table.Column<string>(nullable: true),
                    path = table.Column<string>(nullable: true),
                    recorded_date = table.Column<DateTime>(nullable: false),
                    created_by_app = table.Column<bool>(nullable: false),
                    nutzeinheit_guid = table.Column<Guid>(nullable: true),
                    device_guid = table.Column<Guid>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_photos", x => x.guid);
                    table.ForeignKey(
                        name: "fk_devices_photos_device_guid",
                        column: x => x.device_guid,
                        principalTable: "devices",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_nutzeinheiten_photos_nutzeinheit_guid",
                        column: x => x.nutzeinheit_guid,
                        principalTable: "nutzeinheiten",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "nutzer_communications",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    address = table.Column<string>(nullable: false),
                    note = table.Column<string>(nullable: true),
                    nutzer_guid = table.Column<Guid>(nullable: false),
                    communication_feature_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_nutzer_communications", x => x.guid);
                    table.ForeignKey(
                        name: "fk_communication_features_nutzer_communications_communication_feature_guid",
                        column: x => x.communication_feature_guid,
                        principalTable: "communication_features",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_nutzer_nutzer_communications_nutzer_guid",
                        column: x => x.nutzer_guid,
                        principalTable: "nutzer",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "appointment_nutzeinheiten",
                columns: table => new
                {
                    appointment_guid = table.Column<Guid>(nullable: false),
                    nutzeinheit_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_appointment_nutzeinheiten", x => new { x.appointment_guid, x.nutzeinheit_guid });
                    table.ForeignKey(
                        name: "fk_appointments_appointment_nutzeinheiten_appointment_guid",
                        column: x => x.appointment_guid,
                        principalTable: "appointments",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_appointments_appointment_nutzeinheiten_nutzeinheit_guid",
                        column: x => x.nutzeinheit_guid,
                        principalTable: "nutzeinheiten",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "appointments_technician",
                columns: table => new
                {
                    appointment_guid = table.Column<Guid>(nullable: false),
                    TechnicianGuid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_appointments_technician", x => new { x.TechnicianGuid, x.appointment_guid });
                    table.ForeignKey(
                        name: "fk_appointments_appointment_technician_appointment_guid",
                        column: x => x.appointment_guid,
                        principalTable: "appointments",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "nutzeinheiten_order_positions",
                columns: table => new
                {
                    nutzeinheit_guid = table.Column<Guid>(nullable: false),
                    order_position_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_nutzeinheiten_order_positions", x => new { x.order_position_guid, x.nutzeinheit_guid });
                    table.ForeignKey(
                        name: "fk_nutzeinheiten_nutzeinheiten_order_positions_nutzeinheit_guid",
                        column: x => x.nutzeinheit_guid,
                        principalTable: "nutzeinheiten",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_nutzeinheiten_order_positions_order_position_guid",
                        column: x => x.order_position_guid,
                        principalTable: "order_positions",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "salutations",
                columns: new[] { "id", "label" },
                values: new object[] { 1, "Herr/Frau/Firma" });

            migrationBuilder.InsertData(
                table: "salutations",
                columns: new[] { "id", "label" },
                values: new object[] { 2, "Herr/Frau" });

            migrationBuilder.InsertData(
                table: "salutations",
                columns: new[] { "id", "label" },
                values: new object[] { 3, "Herr" });

            migrationBuilder.InsertData(
                table: "salutations",
                columns: new[] { "id", "label" },
                values: new object[] { 4, "Frau" });

            migrationBuilder.InsertData(
                table: "salutations",
                columns: new[] { "id", "label" },
                values: new object[] { 5, "Firma" });

            migrationBuilder.InsertData(
                table: "salutations",
                columns: new[] { "id", "label" },
                values: new object[] { 6, "" });

            migrationBuilder.InsertData(
                table: "titles",
                columns: new[] { "id", "label", "note" },
                values: new object[] { 1, "Dr.", null });

            migrationBuilder.InsertData(
                table: "titles",
                columns: new[] { "id", "label", "note" },
                values: new object[] { 2, "Prof.", null });

            migrationBuilder.CreateIndex(
                name: "IX_abrechnungseinheiten_address_guid",
                table: "abrechnungseinheiten",
                column: "address_guid");

            migrationBuilder.CreateIndex(
                name: "IX_abrechnungseinheiten_customer_guid",
                table: "abrechnungseinheiten",
                column: "customer_guid");

            migrationBuilder.CreateIndex(
                name: "IX_appointment_nutzeinheiten_nutzeinheit_guid",
                table: "appointment_nutzeinheiten",
                column: "nutzeinheit_guid");

            migrationBuilder.CreateIndex(
                name: "IX_appointments_order_guid",
                table: "appointments",
                column: "order_guid");

            migrationBuilder.CreateIndex(
                name: "IX_appointments_technician_appointment_guid",
                table: "appointments_technician",
                column: "appointment_guid");

            migrationBuilder.CreateIndex(
                name: "IX_customers_salutation_id",
                table: "customers",
                column: "salutation_id");

            migrationBuilder.CreateIndex(
                name: "IX_device_catalogs_device_kind_guid",
                table: "device_catalogs",
                column: "device_kind_guid");

            migrationBuilder.CreateIndex(
                name: "IX_device_catalogs_manufacturer_guid",
                table: "device_catalogs",
                column: "manufacturer_guid");

            migrationBuilder.CreateIndex(
                name: "IX_device_consumptions_device_guid",
                table: "device_consumptions",
                column: "device_guid");

            migrationBuilder.CreateIndex(
                name: "IX_device_consumptions_readind_kind_guid",
                table: "device_consumptions",
                column: "readind_kind_guid");

            migrationBuilder.CreateIndex(
                name: "IX_devices_device_catalog_guid",
                table: "devices",
                column: "device_catalog_guid");

            migrationBuilder.CreateIndex(
                name: "IX_devices_nutzeinheit_guid",
                table: "devices",
                column: "nutzeinheit_guid");

            migrationBuilder.CreateIndex(
                name: "IX_devices_room_guid",
                table: "devices",
                column: "room_guid");

            migrationBuilder.CreateIndex(
                name: "IX_devices_additional_articles_additional_article_guid",
                table: "devices_additional_articles",
                column: "additional_article_guid");

            migrationBuilder.CreateIndex(
                name: "IX_devices_order_states_amw_info_key_guid",
                table: "devices_order_states",
                column: "amw_info_key_guid");

            migrationBuilder.CreateIndex(
                name: "IX_devices_order_states_device_guid",
                table: "devices_order_states",
                column: "device_guid");

            migrationBuilder.CreateIndex(
                name: "IX_nutzeinheiten_abrechnungseinheit_guid",
                table: "nutzeinheiten",
                column: "abrechnungseinheit_guid");

            migrationBuilder.CreateIndex(
                name: "IX_nutzeinheiten_address_guid",
                table: "nutzeinheiten",
                column: "address_guid");

            migrationBuilder.CreateIndex(
                name: "IX_nutzeinheiten_order_positions_nutzeinheit_guid",
                table: "nutzeinheiten_order_positions",
                column: "nutzeinheit_guid");

            migrationBuilder.CreateIndex(
                name: "IX_nutzeinheiten_order_states_amw_info_key_guid",
                table: "nutzeinheiten_order_states",
                column: "amw_info_key_guid");

            migrationBuilder.CreateIndex(
                name: "IX_nutzeinheiten_order_states_nutzeinheit_guid",
                table: "nutzeinheiten_order_states",
                column: "nutzeinheit_guid");

            migrationBuilder.CreateIndex(
                name: "IX_nutzer_nutzeinheit_guid",
                table: "nutzer",
                column: "nutzeinheit_guid");

            migrationBuilder.CreateIndex(
                name: "IX_nutzer_salutation_id",
                table: "nutzer",
                column: "salutation_id");

            migrationBuilder.CreateIndex(
                name: "IX_nutzer_title_id",
                table: "nutzer",
                column: "title_id");

            migrationBuilder.CreateIndex(
                name: "IX_nutzer_communications_communication_feature_guid",
                table: "nutzer_communications",
                column: "communication_feature_guid");

            migrationBuilder.CreateIndex(
                name: "IX_nutzer_communications_nutzer_guid",
                table: "nutzer_communications",
                column: "nutzer_guid");

            migrationBuilder.CreateIndex(
                name: "IX_order_positions_article_guid",
                table: "order_positions",
                column: "article_guid");

            migrationBuilder.CreateIndex(
                name: "IX_order_positions_order_guid",
                table: "order_positions",
                column: "order_guid");

            migrationBuilder.CreateIndex(
                name: "IX_orderers_person_guid",
                table: "orderers",
                column: "person_guid");

            migrationBuilder.CreateIndex(
                name: "IX_orders_abrechnungseinheit_guid",
                table: "orders",
                column: "abrechnungseinheit_guid");

            migrationBuilder.CreateIndex(
                name: "IX_orders_orderer_guid",
                table: "orders",
                column: "orderer_guid");

            migrationBuilder.CreateIndex(
                name: "IX_persons_salutation_id",
                table: "persons",
                column: "salutation_id");

            migrationBuilder.CreateIndex(
                name: "IX_persons_title_id",
                table: "persons",
                column: "title_id");

            migrationBuilder.CreateIndex(
                name: "IX_persons_abrechnungseinheiten_abrechnungseinheit_guid",
                table: "persons_abrechnungseinheiten",
                column: "abrechnungseinheit_guid");

            migrationBuilder.CreateIndex(
                name: "IX_persons_abrechnungseinheiten_person_guid",
                table: "persons_abrechnungseinheiten",
                column: "person_guid");

            migrationBuilder.CreateIndex(
                name: "IX_persons_communications_communication_feature_guid",
                table: "persons_communications",
                column: "communication_feature_guid");

            migrationBuilder.CreateIndex(
                name: "IX_persons_communications_person_guid",
                table: "persons_communications",
                column: "person_guid");

            migrationBuilder.CreateIndex(
                name: "IX_photos_device_guid",
                table: "photos",
                column: "device_guid");

            migrationBuilder.CreateIndex(
                name: "IX_photos_nutzeinheit_guid",
                table: "photos",
                column: "nutzeinheit_guid");

            migrationBuilder.CreateIndex(
                name: "IX_signatures_nutzeinheit_guid",
                table: "signatures",
                column: "nutzeinheit_guid");

            migrationBuilder.CreateIndex(
                name: "IX_signatures_order_guid",
                table: "signatures",
                column: "order_guid");

            var updateLastModifiedAbrechnungseinheiten = @"
                CREATE TRIGGER [after_update_last_modified_abrechnungseinheiten]
                AFTER UPDATE
                ON abrechnungseinheiten
                FOR EACH ROW
                    BEGIN
                UPDATE abrechnungseinheiten SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedAbrechnungseinheiten);

            var updateLastModifiedPersonsAbrechnungseinheiten= @"
                CREATE TRIGGER [after_update_last_modified_persons_abrechnungseinheiten]
                AFTER UPDATE
                ON persons_abrechnungseinheiten
                FOR EACH ROW
                    BEGIN
                UPDATE persons_abrechnungseinheiten SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedPersonsAbrechnungseinheiten);

            var updateLastModifiedPersons = @"
                CREATE TRIGGER [after_update_last_modified_persons]
                AFTER UPDATE
                ON persons
                FOR EACH ROW
                    BEGIN
                UPDATE persons SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedPersons);

            var updateLastModifiedPersonCommunications = @"
                CREATE TRIGGER [after_update_last_modified_persons_communications]
                AFTER UPDATE
                ON persons_communications
                FOR EACH ROW
                    BEGIN
                UPDATE persons_communications SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedPersonCommunications);

            var updateLastModifiedNutzeinheiten = @"
                CREATE TRIGGER [after_update_last_modified_nutzeinheiten]
                AFTER UPDATE
                ON nutzeinheiten
                FOR EACH ROW
                    BEGIN
                UPDATE nutzeinheiten SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedNutzeinheiten);

             var updateLastModifiedNutzer = @"
                CREATE TRIGGER [after_update_last_modified_nutzer]
                AFTER UPDATE
                ON nutzer
                FOR EACH ROW
                    BEGIN
                UPDATE nutzer SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedNutzer);

            var updateLastModifiedNutzerCommunications = @"
                CREATE TRIGGER [after_update_last_modified_nutzer_communications]
                AFTER UPDATE
                ON nutzer_communications
                FOR EACH ROW
                    BEGIN
                UPDATE nutzer_communications SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedNutzerCommunications);

            var updateLastModifiedDevices = @"
                CREATE TRIGGER [after_update_last_modified_devices]
                AFTER UPDATE
                ON devices
                FOR EACH ROW
                    BEGIN
                UPDATE devices SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedDevices);

            var updateLastModifiedDeviceConsumtions = @"
                CREATE TRIGGER [after_update_last_modified_device_consumptions]
                AFTER UPDATE
                ON device_consumptions
                FOR EACH ROW
                    BEGIN
                UPDATE device_consumptions SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedDeviceConsumtions);

            var updateLastModifiedOrders = @"
                CREATE TRIGGER [after_update_last_modified_orders]
                AFTER UPDATE
                ON orders
                FOR EACH ROW
                    BEGIN
                UPDATE orders SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedOrders);

            var updateLastModifiedOrderPositions = @"
                CREATE TRIGGER [after_update_last_modified_order_positions]
                AFTER UPDATE
                ON order_positions
                FOR EACH ROW
                    BEGIN
                UPDATE order_positions SET last_modified = CURRENT_TIMESTAMP WHERE Guid = old.Guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedOrderPositions);

            var updateLastModifiedOrdersStates = @"
                CREATE TRIGGER [after_update_last_modified_orders_states]
                AFTER UPDATE
                ON orders_states
                FOR EACH ROW
                    BEGIN
                UPDATE orders_states SET last_modified = CURRENT_TIMESTAMP WHERE order_guid = old.order_guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedOrdersStates);

            var updateLastModifiedNutzeinheitenOrderStates = @"
                CREATE TRIGGER [after_update_last_modified_nutzeinheiten_order_states]
                AFTER UPDATE
                ON nutzeinheiten_order_states
                FOR EACH ROW
                    BEGIN
                UPDATE nutzeinheiten_order_states SET last_modified = CURRENT_TIMESTAMP WHERE nutzeinheit_guid = old.nutzeinheit_guid AND order_guid = old.order_guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedNutzeinheitenOrderStates);

            var updateLastModifiedDevicesOrderStates = @"
                CREATE TRIGGER [after_update_last_modified_devices_order_states]
                AFTER UPDATE
                ON devices_order_states
                FOR EACH ROW
                    BEGIN
                UPDATE devices_order_states SET last_modified = CURRENT_TIMESTAMP WHERE device_guid = old.device_guid AND order_guid = old.order_guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedDevicesOrderStates);

            var updateLastModifiedDevicesAdditionalArticles = @"
                CREATE TRIGGER [after_update_last_modified_devices_additional_articles]
                AFTER UPDATE
                ON devices_additional_articles
                FOR EACH ROW
                    BEGIN
                UPDATE devices_additional_articles SET last_modified = CURRENT_TIMESTAMP WHERE device_guid = old.device_guid AND additional_article_guid = old.additional_article_guid;
                END
            ";

            migrationBuilder.Sql(updateLastModifiedDevicesAdditionalArticles);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_abrechnungseinheiten");
            migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_persons_abrechnungseinheiten"); 
            migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_persons");
            migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_persons_communications"); 


            migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_nutzeinheiten");
            migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_nutzer"); 
            migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_nutzer_communications");
            migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_devices");
            migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_device_consumptions"); 


            migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_orders");
            migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_order_positions");
            migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_orders_states");
            migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_nutzeinheiten_order_states");
            migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_devices_order_states");

            migrationBuilder.Sql("DROP TRIGGER after_update_last_modified_devices_additional_articles");

            migrationBuilder.DropTable(
                name: "appointment_nutzeinheiten");

            migrationBuilder.DropTable(
                name: "appointments_technician");

            migrationBuilder.DropTable(
                name: "device_consumptions");

            migrationBuilder.DropTable(
                name: "devices_additional_articles");

            migrationBuilder.DropTable(
                name: "devices_order_states");

            migrationBuilder.DropTable(
                name: "nutzeinheiten_order_positions");

            migrationBuilder.DropTable(
                name: "nutzeinheiten_order_states");

            migrationBuilder.DropTable(
                name: "nutzer_communications");

            migrationBuilder.DropTable(
                name: "orders_states");

            migrationBuilder.DropTable(
                name: "persons_abrechnungseinheiten");

            migrationBuilder.DropTable(
                name: "persons_communications");

            migrationBuilder.DropTable(
                name: "photos");

            migrationBuilder.DropTable(
                name: "signatures");

            migrationBuilder.DropTable(
                name: "appointments");

            migrationBuilder.DropTable(
                name: "reading_kinds");

            migrationBuilder.DropTable(
                name: "additional_articles");

            migrationBuilder.DropTable(
                name: "order_positions");

            migrationBuilder.DropTable(
                name: "amw_info_keys");

            migrationBuilder.DropTable(
                name: "nutzer");

            migrationBuilder.DropTable(
                name: "communication_features");

            migrationBuilder.DropTable(
                name: "devices");

            migrationBuilder.DropTable(
                name: "articles");

            migrationBuilder.DropTable(
                name: "orders");

            migrationBuilder.DropTable(
                name: "device_catalogs");

            migrationBuilder.DropTable(
                name: "nutzeinheiten");

            migrationBuilder.DropTable(
                name: "rooms");

            migrationBuilder.DropTable(
                name: "orderers");

            migrationBuilder.DropTable(
                name: "device_kinds");

            migrationBuilder.DropTable(
                name: "manufacturers");

            migrationBuilder.DropTable(
                name: "abrechnungseinheiten");

            migrationBuilder.DropTable(
                name: "persons");

            migrationBuilder.DropTable(
                name: "addresses");

            migrationBuilder.DropTable(
                name: "customers");

            migrationBuilder.DropTable(
                name: "titles");

            migrationBuilder.DropTable(
                name: "salutations");
        }
    }
}
