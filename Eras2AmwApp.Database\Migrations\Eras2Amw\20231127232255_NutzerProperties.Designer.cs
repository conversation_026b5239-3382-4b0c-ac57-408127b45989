﻿// <auto-generated />
using System;
using Eras2AmwApp.Database.Contexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace Eras2AmwApp.Database.Migrations.Eras2Amw
{
    [DbContext(typeof(Eras2AmwContext))]
    [Migration("20231127232255_NutzerProperties")]
    partial class NutzerProperties
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "2.2.6-servicing-10079");

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Abrechnungseinheit", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<Guid>("AddressGuid")
                        .HasColumnName("address_guid");

                    b.Property<DateTime?>("BilligPeriodEnd")
                        .HasColumnName("billing_period_end");

                    b.Property<Guid>("CustomerGuid")
                        .HasColumnName("customer_guid");

                    b.Property<string>("IPAddress")
                        .HasColumnName("ip_address");

                    b.Property<DateTime?>("LastModified")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.Property<string>("NoteInternal")
                        .HasColumnName("note_internal");

                    b.Property<string>("NoteLegionella")
                        .HasColumnName("note_legionella");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasColumnName("number");

                    b.HasKey("Guid");

                    b.HasIndex("AddressGuid");

                    b.HasIndex("CustomerGuid");

                    b.ToTable("abrechnungseinheiten");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.AdditionalArticle", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<string>("Label")
                        .HasColumnName("label");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasColumnName("number");

                    b.HasKey("Guid");

                    b.ToTable("additional_articles");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Address", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<string>("Additional")
                        .HasColumnName("additional");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasColumnName("city");

                    b.Property<string>("Latitude")
                        .IsRequired()
                        .HasColumnName("latitude");

                    b.Property<string>("Longitude")
                        .IsRequired()
                        .HasColumnName("longitude");

                    b.Property<string>("Mailbox")
                        .HasColumnName("mailbox");

                    b.Property<string>("Street")
                        .HasColumnName("street");

                    b.Property<string>("Street2")
                        .HasColumnName("street2");

                    b.Property<string>("StreetNumber")
                        .HasColumnName("street_number");

                    b.Property<string>("Zipcode")
                        .HasColumnName("zipcode");

                    b.HasKey("Guid");

                    b.ToTable("addresses");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.AmwInfoKey", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<string>("Info")
                        .IsRequired()
                        .HasColumnName("info");

                    b.Property<int>("Key")
                        .HasColumnName("key");

                    b.HasKey("Guid");

                    b.ToTable("amw_info_keys");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Appointment", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<DateTime>("From")
                        .HasColumnName("from");

                    b.Property<Guid>("OrderGuid")
                        .HasColumnName("order_guid");

                    b.Property<DateTime>("To")
                        .HasColumnName("to");

                    b.HasKey("Guid");

                    b.HasIndex("OrderGuid");

                    b.ToTable("appointments");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.AppointmentNutzeinheit", b =>
                {
                    b.Property<Guid>("AppointmentGuid")
                        .HasColumnName("appointment_guid");

                    b.Property<Guid>("NutzeinheitGuid")
                        .HasColumnName("nutzeinheit_guid");

                    b.HasKey("AppointmentGuid", "NutzeinheitGuid");

                    b.HasIndex("NutzeinheitGuid");

                    b.ToTable("appointment_nutzeinheiten");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.AppointmentTechnician", b =>
                {
                    b.Property<Guid>("TechnicianGuid");

                    b.Property<Guid>("AppointmentGuid")
                        .HasColumnName("appointment_guid");

                    b.HasKey("TechnicianGuid", "AppointmentGuid");

                    b.HasIndex("AppointmentGuid");

                    b.ToTable("appointments_technician");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Article", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<string>("Label")
                        .IsRequired()
                        .HasColumnName("label");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasColumnName("number");

                    b.HasKey("Guid");

                    b.ToTable("articles");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.CommunicationFeature", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<string>("Feature")
                        .IsRequired()
                        .HasColumnName("feature");

                    b.Property<bool>("IsPreset")
                        .HasColumnName("is_preset");

                    b.Property<int>("Kind")
                        .HasColumnName("kind");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.HasKey("Guid");

                    b.ToTable("communication_features");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Customer", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnName("name");

                    b.Property<string>("Name2")
                        .HasColumnName("name_2");

                    b.Property<string>("Name3")
                        .HasColumnName("name_3");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasColumnName("number");

                    b.Property<int>("SalutationId")
                        .HasColumnName("salutation_id");

                    b.HasKey("Guid");

                    b.HasIndex("SalutationId");

                    b.ToTable("customers");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Device", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<DateTime?>("CalibrationDate")
                        .HasColumnName("calibration_date");

                    b.Property<DateTime?>("DeinstallationDate")
                        .HasColumnName("deinstallation_date");

                    b.Property<string>("Description")
                        .HasColumnName("description");

                    b.Property<Guid>("DeviceCatalogGuid")
                        .HasColumnName("device_catalog_guid");

                    b.Property<int>("Estimation")
                        .HasColumnName("estimation");

                    b.Property<string>("FirmwareVersion")
                        .HasColumnName("firmware_version");

                    b.Property<DateTime?>("FirstActivation")
                        .HasColumnName("first_activation");

                    b.Property<string>("GroupIdentifier")
                        .HasColumnName("group_identifier")
                        .HasMaxLength(5);

                    b.Property<DateTime>("InstallationDate")
                        .HasColumnName("installation_date");

                    b.Property<bool>("IsCreatedByApp")
                        .HasColumnName("is_created_by_app");

                    b.Property<bool>("IsLeased")
                        .HasColumnName("is_leased");

                    b.Property<bool>("IsMaintained")
                        .HasColumnName("is_maintained");

                    b.Property<string>("Label")
                        .HasColumnName("label");

                    b.Property<DateTime?>("LastModified")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasColumnName("number");

                    b.Property<Guid>("NutzeinheitGuid")
                        .HasColumnName("nutzeinheit_guid");

                    b.Property<string>("OngoingNumber")
                        .IsRequired()
                        .HasColumnName("ongoing_number");

                    b.Property<Guid>("RoomGuid")
                        .HasColumnName("room_guid");

                    b.Property<bool>("Subtraction")
                        .HasColumnName("subtraction");

                    b.Property<int?>("Unit")
                        .HasColumnName("unit");

                    b.HasKey("Guid");

                    b.HasIndex("DeviceCatalogGuid");

                    b.HasIndex("NutzeinheitGuid");

                    b.HasIndex("RoomGuid");

                    b.ToTable("devices");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.DeviceAdditionalArticle", b =>
                {
                    b.Property<Guid>("DeviceGuid")
                        .HasColumnName("device_guid");

                    b.Property<Guid>("AdditionalArticleGuid")
                        .HasColumnName("additional_article_guid");

                    b.Property<bool>("IsCreatedByApp")
                        .HasColumnName("is_created_by_app");

                    b.Property<DateTime>("LastModified")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<decimal>("Quantity")
                        .HasColumnName("quantity")
                        .HasColumnType("decimal(8, 2)");

                    b.HasKey("DeviceGuid", "AdditionalArticleGuid");

                    b.HasIndex("AdditionalArticleGuid");

                    b.ToTable("devices_additional_articles");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.DeviceCatalog", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<string>("ArticleDescription")
                        .HasColumnName("article_description");

                    b.Property<string>("ArticleName")
                        .IsRequired()
                        .HasColumnName("article_name");

                    b.Property<string>("ArticleNumber")
                        .IsRequired()
                        .HasColumnName("article_number");

                    b.Property<string>("ArticleNumberManufacturer")
                        .HasColumnName("article_number_manufacturer");

                    b.Property<string>("Connector")
                        .HasColumnName("connector");

                    b.Property<Guid>("DeviceKindGuid")
                        .HasColumnName("device_kind_guid");

                    b.Property<string>("Diameter")
                        .HasColumnName("diameter");

                    b.Property<bool>("Hkvv")
                        .HasColumnName("hkvv");

                    b.Property<string>("InstallationLength")
                        .HasColumnName("installation_length");

                    b.Property<string>("Label")
                        .HasColumnName("label");

                    b.Property<Guid>("ManufacturerGuid")
                        .HasColumnName("manufacturer_guid");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.Property<string>("OrderCode")
                        .HasColumnName("order_code");

                    b.Property<bool>("Radio")
                        .HasColumnName("radio");

                    b.Property<string>("SontexHkvIdentifier")
                        .HasColumnName("sontex_hkv_identifier");

                    b.Property<string>("SontexRadioIdentifier")
                        .HasColumnName("sontex_radio_identifier");

                    b.Property<bool>("Subtraction")
                        .HasColumnName("subtraction");

                    b.HasKey("Guid");

                    b.HasIndex("DeviceKindGuid");

                    b.HasIndex("ManufacturerGuid");

                    b.ToTable("device_catalogs");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.DeviceConsumption", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<decimal?>("AirTemperature")
                        .HasColumnName("air_temperature")
                        .HasColumnType("decimal(4,1)");

                    b.Property<string>("AmpouleColour")
                        .HasColumnName("ampoule_colour");

                    b.Property<DateTime?>("ChangeDate")
                        .HasColumnName("change_date");

                    b.Property<double?>("Consumption")
                        .HasColumnName("consumption");

                    b.Property<Guid>("DeviceGuid")
                        .HasColumnName("device_guid");

                    b.Property<string>("Error")
                        .HasColumnName("error");

                    b.Property<int>("Estimation")
                        .HasColumnName("estimation_kind");

                    b.Property<double>("Factor")
                        .HasColumnName("factor");

                    b.Property<bool>("IsReconstructed")
                        .HasColumnName("is_reconstructed");

                    b.Property<DateTime?>("LastErrorDate")
                        .HasColumnName("last_error_date");

                    b.Property<DateTime?>("LastModified")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<double?>("Manual")
                        .HasColumnName("manual");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.Property<int>("Origin")
                        .HasColumnName("origin");

                    b.Property<decimal?>("RadiatorTemperature")
                        .HasColumnName("radiator_temperature")
                        .HasColumnType("decimal(5,1)");

                    b.Property<Guid>("ReadindKindGuid")
                        .HasColumnName("readind_kind_guid");

                    b.Property<double?>("Reading")
                        .HasColumnName("reading");

                    b.Property<DateTime?>("ReadingDate")
                        .HasColumnName("reading_date");

                    b.Property<int>("State")
                        .HasColumnName("state");

                    b.HasKey("Guid");

                    b.HasIndex("DeviceGuid");

                    b.HasIndex("ReadindKindGuid");

                    b.ToTable("device_consumptions");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.DeviceKind", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<int>("Class")
                        .HasColumnName("class");

                    b.Property<string>("LabelLong")
                        .HasColumnName("label_long");

                    b.Property<string>("LabelShort")
                        .IsRequired()
                        .HasColumnName("label_short");

                    b.Property<bool>("Readable")
                        .HasColumnName("readable");

                    b.HasKey("Guid");

                    b.ToTable("device_kinds");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.DeviceOrderState", b =>
                {
                    b.Property<Guid>("OrderGuid")
                        .HasColumnName("order_guid");

                    b.Property<Guid>("DeviceGuid")
                        .HasColumnName("device_guid");

                    b.Property<Guid?>("AmwInfoKeyGuid")
                        .HasColumnName("amw_info_key_guid");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnName("completed_date");

                    b.Property<DateTime>("LastModified")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("OrderKind")
                        .HasColumnName("order_kind");

                    b.Property<int>("ProcessState")
                        .HasColumnName("process_state");

                    b.HasKey("OrderGuid", "DeviceGuid");

                    b.HasIndex("AmwInfoKeyGuid");

                    b.HasIndex("DeviceGuid");

                    b.ToTable("devices_order_states");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Manufacturer", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnName("name");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.HasKey("Guid");

                    b.ToTable("manufacturers");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Nutzeinheit", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<Guid>("AbrechnungseinheitGuid")
                        .HasColumnName("abrechnungseinheit_guid");

                    b.Property<Guid>("AddressGuid")
                        .HasColumnName("address_guid");

                    b.Property<string>("IPAddress")
                        .HasColumnName("ip_address");

                    b.Property<bool>("IsCreatedByApp")
                        .HasColumnName("is_created_by_app");

                    b.Property<DateTime?>("LastModified")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Location")
                        .HasColumnName("location");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasColumnName("number");

                    b.Property<string>("WalkSequence")
                        .HasColumnName("walk_sequence");

                    b.HasKey("Guid");

                    b.HasIndex("AbrechnungseinheitGuid");

                    b.HasIndex("AddressGuid");

                    b.ToTable("nutzeinheiten");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.NutzeinheitOrderPosition", b =>
                {
                    b.Property<Guid>("OrderPositionGuid")
                        .HasColumnName("order_position_guid");

                    b.Property<Guid>("NutzeinheitGuid")
                        .HasColumnName("nutzeinheit_guid");

                    b.HasKey("OrderPositionGuid", "NutzeinheitGuid");

                    b.HasIndex("NutzeinheitGuid");

                    b.ToTable("nutzeinheiten_order_positions");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.NutzeinheitOrderState", b =>
                {
                    b.Property<Guid>("OrderGuid")
                        .HasColumnName("order_guid");

                    b.Property<Guid>("NutzeinheitGuid")
                        .HasColumnName("nutzeinheit_guid");

                    b.Property<Guid?>("AmwInfoKeyGuid")
                        .HasColumnName("amw_info_key_guid");

                    b.Property<string>("AppDeviceNumber")
                        .HasColumnName("app_device_number");

                    b.Property<string>("AppVersion")
                        .HasColumnName("app_version");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnName("completed_date");

                    b.Property<DateTime>("LastModified")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("OrderKinds")
                        .HasColumnName("order_kinds");

                    b.Property<string>("OsPlatform")
                        .HasColumnName("os_platform");

                    b.Property<string>("OsVersion")
                        .HasColumnName("os_version");

                    b.Property<int>("ProcessState")
                        .HasColumnName("process_state");

                    b.HasKey("OrderGuid", "NutzeinheitGuid");

                    b.HasIndex("AmwInfoKeyGuid");

                    b.HasIndex("NutzeinheitGuid");

                    b.ToTable("nutzeinheiten_order_states");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Nutzer", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<bool>("IsCreatedByApp")
                        .HasColumnName("is_created_by_app");

                    b.Property<int>("Kind")
                        .HasColumnName("kind");

                    b.Property<DateTime?>("LastModified")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime>("MoveInDate")
                        .HasColumnName("move_in_date");

                    b.Property<DateTime?>("MoveOutDate")
                        .HasColumnName("move_out_date");

                    b.Property<string>("Name1")
                        .HasColumnName("name1");

                    b.Property<string>("Name2")
                        .HasColumnName("name2");

                    b.Property<string>("Name3")
                        .HasColumnName("name3");

                    b.Property<string>("Name4")
                        .HasColumnName("name4");

                    b.Property<DateTime?>("NextAppointmentDate")
                        .HasColumnName("next_appointment_date");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.Property<Guid>("NutzeinheitGuid")
                        .HasColumnName("nutzeinheit_guid");

                    b.Property<int>("SalutationId")
                        .HasColumnName("salutation_id");

                    b.Property<int?>("TitleId")
                        .HasColumnName("title_id");

                    b.HasKey("Guid");

                    b.HasIndex("NutzeinheitGuid");

                    b.HasIndex("SalutationId");

                    b.HasIndex("TitleId");

                    b.ToTable("nutzer");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.NutzerCoOwnership", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<DateTime>("LastModified")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<Guid>("NutzerGuid")
                        .HasColumnName("nutzer_guid");

                    b.Property<DateTime>("RangeFrom")
                        .HasColumnName("range_from");

                    b.Property<DateTime?>("RangeTo")
                        .HasColumnName("range_to");

                    b.Property<decimal?>("ShareValue")
                        .HasColumnName("share_value");

                    b.HasKey("Guid");

                    b.HasIndex("NutzerGuid");

                    b.ToTable("nutzer_coownership");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.NutzerCommunication", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnName("address");

                    b.Property<Guid>("CommunicationFeatureGuid")
                        .HasColumnName("communication_feature_guid");

                    b.Property<DateTime?>("LastModified")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.Property<Guid>("NutzerGuid")
                        .HasColumnName("nutzer_guid");

                    b.HasKey("Guid");

                    b.HasIndex("CommunicationFeatureGuid");

                    b.HasIndex("NutzerGuid");

                    b.ToTable("nutzer_communications");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.NutzerPersonen", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<DateTime?>("LastModified")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int?>("NumberOfPeople")
                        .HasColumnName("number_of_people");

                    b.Property<Guid>("NutzerGuid")
                        .HasColumnName("nutzer_guid");

                    b.Property<DateTime>("RangeFrom")
                        .HasColumnName("range_from");

                    b.Property<DateTime?>("RangeTo")
                        .HasColumnName("range_to");

                    b.HasKey("Guid");

                    b.HasIndex("NutzerGuid");

                    b.ToTable("nutzer_personen");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.NutzerQuadratmeter", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<DateTime>("LastModified")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<Guid>("NutzerGuid")
                        .HasColumnName("nutzer_guid");

                    b.Property<DateTime>("RangeFrom")
                        .HasColumnName("range_from");

                    b.Property<DateTime?>("RangeTo")
                        .HasColumnName("range_to");

                    b.Property<decimal?>("SquareMeters_Hzg")
                        .HasColumnName("square_meters_hzg");

                    b.Property<decimal?>("SquareMeters_Nk")
                        .HasColumnName("square_meters_nk");

                    b.Property<decimal?>("SquareMeters_Ww")
                        .HasColumnName("square_meters_ww");

                    b.HasKey("Guid");

                    b.HasIndex("NutzerGuid");

                    b.ToTable("nutzer_quadratmeter");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Order", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<Guid>("AbrechnungseinheitGuid")
                        .HasColumnName("abrechnungseinheit_guid");

                    b.Property<string>("CreatedDate")
                        .IsRequired()
                        .HasColumnName("created_date");

                    b.Property<string>("IPAddress")
                        .HasColumnName("ip_address");

                    b.Property<bool>("IsSignatureRequired")
                        .HasColumnName("is_signature_required");

                    b.Property<string>("Label")
                        .IsRequired()
                        .HasColumnName("label");

                    b.Property<DateTime>("LastModified")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasColumnName("number");

                    b.Property<Guid>("OrdererGuid")
                        .HasColumnName("orderer_guid");

                    b.HasKey("Guid");

                    b.HasIndex("AbrechnungseinheitGuid");

                    b.HasIndex("OrdererGuid");

                    b.ToTable("orders");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.OrderPosition", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<int>("ActualQuantity")
                        .HasColumnName("actual_quantity");

                    b.Property<Guid>("ArticleGuid")
                        .HasColumnName("article_guid");

                    b.Property<DateTime>("LastModified")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.Property<Guid>("OrderGuid")
                        .HasColumnName("order_guid");

                    b.Property<int>("RequiredQuantity")
                        .HasColumnName("required_quantity");

                    b.HasKey("Guid");

                    b.HasIndex("ArticleGuid");

                    b.HasIndex("OrderGuid");

                    b.ToTable("order_positions");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.OrderState", b =>
                {
                    b.Property<Guid>("OrderGuid")
                        .HasColumnName("order_guid");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnName("completed_date");

                    b.Property<string>("Kinds")
                        .IsRequired()
                        .HasColumnName("kinds");

                    b.Property<DateTime>("LastModified")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<int>("ProcessState")
                        .HasColumnName("process_state");

                    b.HasKey("OrderGuid");

                    b.ToTable("orders_states");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Orderer", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<string>("BusinessPosition")
                        .IsRequired()
                        .HasColumnName("business_position");

                    b.Property<Guid>("PersonGuid")
                        .HasColumnName("person_guid");

                    b.HasKey("Guid");

                    b.HasIndex("PersonGuid");

                    b.ToTable("orderers");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Person", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<string>("Firstname1")
                        .HasColumnName("firstname_1");

                    b.Property<string>("Firstname2")
                        .HasColumnName("firstname_2");

                    b.Property<DateTime?>("LastModified")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Lastname1")
                        .IsRequired()
                        .HasColumnName("lastname_1");

                    b.Property<string>("Lastname2")
                        .HasColumnName("lastname_2");

                    b.Property<string>("Lastname3")
                        .HasColumnName("lastname_3");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.Property<int>("SalutationId")
                        .HasColumnName("salutation_id");

                    b.Property<int?>("TitleId")
                        .HasColumnName("title_id");

                    b.HasKey("Guid");

                    b.HasIndex("SalutationId");

                    b.HasIndex("TitleId");

                    b.ToTable("persons");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.PersonAbrechnungseinheit", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<Guid>("AbrechnungseinheitGuid")
                        .HasColumnName("abrechnungseinheit_guid");

                    b.Property<string>("BusinessPosition")
                        .IsRequired()
                        .HasColumnName("business_position");

                    b.Property<bool>("IsCreatedByApp")
                        .HasColumnName("is_created_by_app");

                    b.Property<DateTime?>("LastModified")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.Property<Guid>("PersonGuid")
                        .HasColumnName("person_guid");

                    b.HasKey("Guid");

                    b.HasIndex("AbrechnungseinheitGuid");

                    b.HasIndex("PersonGuid");

                    b.ToTable("persons_abrechnungseinheiten");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.PersonCommunication", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnName("address");

                    b.Property<Guid>("CommunicationFeatureGuid")
                        .HasColumnName("communication_feature_guid");

                    b.Property<DateTime?>("LastModified")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("last_modified")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.Property<Guid>("PersonGuid")
                        .HasColumnName("person_guid");

                    b.HasKey("Guid");

                    b.HasIndex("CommunicationFeatureGuid");

                    b.HasIndex("PersonGuid");

                    b.ToTable("persons_communications");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Photo", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<bool>("CreatedByApp")
                        .HasColumnName("created_by_app");

                    b.Property<Guid?>("DeviceGuid")
                        .HasColumnName("device_guid");

                    b.Property<string>("Name")
                        .HasColumnName("name");

                    b.Property<Guid?>("NutzeinheitGuid")
                        .HasColumnName("nutzeinheit_guid");

                    b.Property<string>("Path")
                        .HasColumnName("path");

                    b.Property<DateTime>("RecordedDate")
                        .HasColumnName("recorded_date");

                    b.HasKey("Guid");

                    b.HasIndex("DeviceGuid");

                    b.HasIndex("NutzeinheitGuid");

                    b.ToTable("photos");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.ReadingKind", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<string>("LabelLong")
                        .HasColumnName("label_long");

                    b.Property<string>("LabelShort")
                        .IsRequired()
                        .HasColumnName("label_short");

                    b.HasKey("Guid");

                    b.ToTable("reading_kinds");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Room", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<string>("Label")
                        .HasColumnName("label");

                    b.Property<string>("LabelShort")
                        .IsRequired()
                        .HasColumnName("label_short");

                    b.HasKey("Guid");

                    b.ToTable("rooms");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Salutation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("id");

                    b.Property<string>("Label")
                        .IsRequired()
                        .HasColumnName("label");

                    b.HasKey("Id");

                    b.ToTable("salutations");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Label = "Herr/Frau/Firma"
                        },
                        new
                        {
                            Id = 2,
                            Label = "Herr/Frau"
                        },
                        new
                        {
                            Id = 3,
                            Label = "Herr"
                        },
                        new
                        {
                            Id = 4,
                            Label = "Frau"
                        },
                        new
                        {
                            Id = 5,
                            Label = "Firma"
                        },
                        new
                        {
                            Id = 6,
                            Label = ""
                        });
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Signature", b =>
                {
                    b.Property<Guid>("Guid")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("guid");

                    b.Property<bool>("CreatedByApp")
                        .HasColumnName("created_by_app");

                    b.Property<int>("Kind")
                        .HasColumnName("signature_kind");

                    b.Property<Guid>("NutzeinheitGuid")
                        .HasColumnName("nutzeinheit_guid");

                    b.Property<Guid>("OrderGuid")
                        .HasColumnName("order_guid");

                    b.Property<string>("Path")
                        .HasColumnName("path");

                    b.Property<DateTime>("RecordedDate")
                        .HasColumnName("recorded_date");

                    b.HasKey("Guid");

                    b.HasIndex("NutzeinheitGuid");

                    b.HasIndex("OrderGuid");

                    b.ToTable("signatures");
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Title", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnName("id");

                    b.Property<string>("Label")
                        .IsRequired()
                        .HasColumnName("label");

                    b.Property<string>("Note")
                        .HasColumnName("note");

                    b.HasKey("Id");

                    b.ToTable("titles");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Label = "Dr."
                        },
                        new
                        {
                            Id = 2,
                            Label = "Prof."
                        });
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Abrechnungseinheit", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Address", "Address")
                        .WithMany("Abrechnungseinheiten")
                        .HasForeignKey("AddressGuid")
                        .HasConstraintName("fk_addresses_abrechnungseinheiten_address_guid")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Customer", "Customer")
                        .WithMany("Abrechnungseinheiten")
                        .HasForeignKey("CustomerGuid")
                        .HasConstraintName("fk_customers_abrechnungseinheiten_customer_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Appointment", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Order", "Order")
                        .WithMany("Appointments")
                        .HasForeignKey("OrderGuid")
                        .HasConstraintName("fk_appointments_orders_order_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.AppointmentNutzeinheit", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Appointment", "Appointment")
                        .WithMany("AppointmentNutzeinheiten")
                        .HasForeignKey("AppointmentGuid")
                        .HasConstraintName("fk_appointments_appointment_nutzeinheiten_appointment_guid")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Nutzeinheit", "Nutzeinheit")
                        .WithMany("AppointmentNutzeinheiten")
                        .HasForeignKey("NutzeinheitGuid")
                        .HasConstraintName("fk_appointments_appointment_nutzeinheiten_nutzeinheit_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.AppointmentTechnician", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Appointment", "Appointment")
                        .WithMany("AppointmentStoreUsers")
                        .HasForeignKey("AppointmentGuid")
                        .HasConstraintName("fk_appointments_appointment_technician_appointment_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Customer", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Salutation", "Salutation")
                        .WithMany("Customers")
                        .HasForeignKey("SalutationId")
                        .HasConstraintName("fk_salutations_customers_salutation_id")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Device", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.DeviceCatalog", "DeviceCatalog")
                        .WithMany("Devices")
                        .HasForeignKey("DeviceCatalogGuid")
                        .HasConstraintName("fk_device_catalogs_devices_device_catalog_guid")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Nutzeinheit", "Nutzeinheit")
                        .WithMany("Devices")
                        .HasForeignKey("NutzeinheitGuid")
                        .HasConstraintName("fk_nutzeinheiten_devices_nutzeinheit_guid")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Room", "Room")
                        .WithMany("Devices")
                        .HasForeignKey("RoomGuid")
                        .HasConstraintName("fk_rooms_devices_room_guid")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.DeviceAdditionalArticle", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.AdditionalArticle", "AdditionalArticle")
                        .WithMany("DeviceAdditionalArticles")
                        .HasForeignKey("AdditionalArticleGuid")
                        .HasConstraintName("fk_additional_articles_devices_additional_articles_additional_article_guid")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Device", "Device")
                        .WithMany("DeviceAdditionalArticles")
                        .HasForeignKey("DeviceGuid")
                        .HasConstraintName("fk_devices_devices_additional_articles_device_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.DeviceCatalog", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.DeviceKind", "DeviceKind")
                        .WithMany("DeviceCatalogs")
                        .HasForeignKey("DeviceKindGuid")
                        .HasConstraintName("fk_device_kinds_devices_catologs_device_kind_guid")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Manufacturer", "Manufacturer")
                        .WithMany("DeviceCatalogs")
                        .HasForeignKey("ManufacturerGuid")
                        .HasConstraintName("fk_manufacturers_devices_catologs_manufacturer_guid")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.DeviceConsumption", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Device", "Device")
                        .WithMany("DeviceConsumptions")
                        .HasForeignKey("DeviceGuid")
                        .HasConstraintName("fk_devices_devices_consumptions_device_guid")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.ReadingKind", "ReadingKind")
                        .WithMany("DeviceConsumptions")
                        .HasForeignKey("ReadindKindGuid")
                        .HasConstraintName("fk_reading_kinds_device_consumptions_reading_kind_guid")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.DeviceOrderState", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.AmwInfoKey", "AmwInfoKey")
                        .WithMany("DeviceOrderStates")
                        .HasForeignKey("AmwInfoKeyGuid")
                        .HasConstraintName("fk_amw_info_keys_device_order_states_amw_info_key_guid")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Device", "Device")
                        .WithMany("OrderStates")
                        .HasForeignKey("DeviceGuid")
                        .HasConstraintName("fk_devices_devices_order_states_device_guid")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Order", "Order")
                        .WithMany("DeviceOrderStates")
                        .HasForeignKey("OrderGuid")
                        .HasConstraintName("fk_orders_devices_order_states_order_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Nutzeinheit", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Abrechnungseinheit", "Abrechnungseinheit")
                        .WithMany("Nutzeinheiten")
                        .HasForeignKey("AbrechnungseinheitGuid")
                        .HasConstraintName("fk_abrechnungseinheiten_nutzeinheiten_abrechnungseinheit_guid")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Address", "Address")
                        .WithMany("Nutzeinheiten")
                        .HasForeignKey("AddressGuid")
                        .HasConstraintName("fk_addresses_nutzeinheiten_address_guid")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.NutzeinheitOrderPosition", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Nutzeinheit", "Nutzeinheit")
                        .WithMany("NutzeinheitOrderPositions")
                        .HasForeignKey("NutzeinheitGuid")
                        .HasConstraintName("fk_nutzeinheiten_nutzeinheiten_order_positions_nutzeinheit_guid")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.OrderPosition", "OrderPosition")
                        .WithMany("NutzeinheitOrderPositions")
                        .HasForeignKey("OrderPositionGuid")
                        .HasConstraintName("fk_nutzeinheiten_order_positions_order_position_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.NutzeinheitOrderState", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.AmwInfoKey", "AmwInfoKey")
                        .WithMany("NutzeinheitOrderStates")
                        .HasForeignKey("AmwInfoKeyGuid")
                        .HasConstraintName("fk_amw_info_keys_nutzeinheit_order_states_amw_info_key_guid")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Nutzeinheit", "Nutzeinheit")
                        .WithMany("OrderStates")
                        .HasForeignKey("NutzeinheitGuid")
                        .HasConstraintName("fk_nutzeinheiten_nutzeinheiten_order_states_nutzeinheit_guid")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Order", "Order")
                        .WithMany("NutzeinheitOrderStates")
                        .HasForeignKey("OrderGuid")
                        .HasConstraintName("fk_orders_nutzeinheiten_order_states_order_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Nutzer", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Nutzeinheit", "Nutzeinheit")
                        .WithMany("Nutzer")
                        .HasForeignKey("NutzeinheitGuid")
                        .HasConstraintName("fk_nutzeinheiten_nutzer_nutzeinheit_guid")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Salutation", "Salutation")
                        .WithMany("Nutzer")
                        .HasForeignKey("SalutationId")
                        .HasConstraintName("fk_salutations_nutzer_salutation_id")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Title", "Title")
                        .WithMany("Nutzer")
                        .HasForeignKey("TitleId")
                        .HasConstraintName("fk_titles_nutzer_title_id")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.NutzerCoOwnership", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Nutzer", "Nutzer")
                        .WithMany("NutzerCoOwnership")
                        .HasForeignKey("NutzerGuid")
                        .HasConstraintName("fk_nutzercoownership_nutzer_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.NutzerCommunication", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.CommunicationFeature", "CommunicationFeature")
                        .WithMany("NutzerCommunications")
                        .HasForeignKey("CommunicationFeatureGuid")
                        .HasConstraintName("fk_communication_features_nutzer_communications_communication_feature_guid")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Nutzer", "Nutzer")
                        .WithMany("NutzerCommunications")
                        .HasForeignKey("NutzerGuid")
                        .HasConstraintName("fk_nutzer_nutzer_communications_nutzer_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.NutzerPersonen", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Nutzer", "Nutzer")
                        .WithMany("NutzerPersonen")
                        .HasForeignKey("NutzerGuid")
                        .HasConstraintName("fk_nutzerpersonen_nutzer_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.NutzerQuadratmeter", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Nutzer", "Nutzer")
                        .WithMany("NutzerQuadratmeter")
                        .HasForeignKey("NutzerGuid")
                        .HasConstraintName("fk_nutzerquadratmeter_nutzer_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Order", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Abrechnungseinheit", "Abrechnungseinheit")
                        .WithMany("Orders")
                        .HasForeignKey("AbrechnungseinheitGuid")
                        .HasConstraintName("fk_abrechnungseinheiten_orders_order_guid")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Orderer", "Orderer")
                        .WithMany("Orders")
                        .HasForeignKey("OrdererGuid")
                        .HasConstraintName("fk_orders_orderers_orderer_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.OrderPosition", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Article", "Article")
                        .WithMany("OrderPositions")
                        .HasForeignKey("ArticleGuid")
                        .HasConstraintName("fk_articles_order_positions_article_guid")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Order", "Order")
                        .WithMany("OrderPositions")
                        .HasForeignKey("OrderGuid")
                        .HasConstraintName("fk_orders_order_positions_order_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.OrderState", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Order", "Order")
                        .WithOne("OrderState")
                        .HasForeignKey("Eras2AmwApp.Domain.Eras2Amw.Models.OrderState", "OrderGuid")
                        .HasConstraintName("fk_orders_orders_states_order_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Orderer", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Person", "Person")
                        .WithMany("Orderers")
                        .HasForeignKey("PersonGuid")
                        .HasConstraintName("fk_persons_orderers_person_guid")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Person", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Salutation", "Salutation")
                        .WithMany("Persons")
                        .HasForeignKey("SalutationId")
                        .HasConstraintName("fk_salutations_persons_id")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Title", "Title")
                        .WithMany("Persons")
                        .HasForeignKey("TitleId")
                        .HasConstraintName("fk_titles_persons_id")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.PersonAbrechnungseinheit", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Abrechnungseinheit", "Abrechnungseinheit")
                        .WithMany("PersonAbrechnungseinheiten")
                        .HasForeignKey("AbrechnungseinheitGuid")
                        .HasConstraintName("fk_abrechnungseinheiten_persons_abrechnungseinheiten_abrechnungseinheit_guid")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Person", "Person")
                        .WithMany("PersonAbrechnungseinheiten")
                        .HasForeignKey("PersonGuid")
                        .HasConstraintName("fk_persons_persons_abrechnungseinheiten_person_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.PersonCommunication", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.CommunicationFeature", "CommunicationFeature")
                        .WithMany("PersonCommunications")
                        .HasForeignKey("CommunicationFeatureGuid")
                        .HasConstraintName("fk_communication_features_persons_communications_communication_feature_guid")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Person", "Person")
                        .WithMany("PersonCommunications")
                        .HasForeignKey("PersonGuid")
                        .HasConstraintName("fk_persons_persons_communications_person_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Photo", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Device", "Device")
                        .WithMany("Photos")
                        .HasForeignKey("DeviceGuid")
                        .HasConstraintName("fk_devices_photos_device_guid")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Nutzeinheit", "Nutzeinheit")
                        .WithMany("Photos")
                        .HasForeignKey("NutzeinheitGuid")
                        .HasConstraintName("fk_nutzeinheiten_photos_nutzeinheit_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });

            modelBuilder.Entity("Eras2AmwApp.Domain.Eras2Amw.Models.Signature", b =>
                {
                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Nutzeinheit", "Nutzeinheit")
                        .WithMany("Signatures")
                        .HasForeignKey("NutzeinheitGuid")
                        .HasConstraintName("fk_nutzeinheiten_signatures_signature_guid")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Eras2AmwApp.Domain.Eras2Amw.Models.Order", "Order")
                        .WithMany("Signatures")
                        .HasForeignKey("OrderGuid")
                        .HasConstraintName("fk_orders_signatures_signature_guid")
                        .OnDelete(DeleteBehavior.Cascade);
                });
#pragma warning restore 612, 618
        }
    }
}
