﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Eras2AmwApp.Database.Migrations.Eras2Amw
{
    public partial class NutzerProperties : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "nutzer_coownership",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    share_value = table.Column<decimal>(nullable: true),
                    range_from = table.Column<DateTime>(nullable: false),
                    range_to = table.Column<DateTime>(nullable: true),
                    nutzer_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_nutzer_coownership", x => x.guid);
                    table.ForeignKey(
                        name: "fk_nutzercoownership_nutzer_guid",
                        column: x => x.nutzer_guid,
                        principalTable: "nutzer",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "nutzer_personen",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: true, defaultValueSql: "CURRENT_TIMESTAMP"),
                    range_from = table.Column<DateTime>(nullable: false),
                    range_to = table.Column<DateTime>(nullable: true),
                    number_of_people = table.Column<int>(nullable: true),
                    nutzer_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_nutzer_personen", x => x.guid);
                    table.ForeignKey(
                        name: "fk_nutzerpersonen_nutzer_guid",
                        column: x => x.nutzer_guid,
                        principalTable: "nutzer",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "nutzer_quadratmeter",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "CURRENT_TIMESTAMP"),
                    square_meters_hzg = table.Column<decimal>(type: "decimal(9,2)", nullable: true),
                    square_meters_ww = table.Column<decimal>(type: "decimal(9,2)", nullable: true),
                    square_meters_nk = table.Column<decimal>(type: "decimal(9,2)", nullable: true),
                    range_from = table.Column<DateTime>(nullable: false),
                    range_to = table.Column<DateTime>(nullable: true),
                    nutzer_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_nutzer_quadratmeter", x => x.guid);
                    table.ForeignKey(
                        name: "fk_nutzerquadratmeter_nutzer_guid",
                        column: x => x.nutzer_guid,
                        principalTable: "nutzer",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_nutzer_coownership_nutzer_guid",
                table: "nutzer_coownership",
                column: "nutzer_guid");

            migrationBuilder.CreateIndex(
                name: "IX_nutzer_personen_nutzer_guid",
                table: "nutzer_personen",
                column: "nutzer_guid");

            migrationBuilder.CreateIndex(
                name: "IX_nutzer_quadratmeter_nutzer_guid",
                table: "nutzer_quadratmeter",
                column: "nutzer_guid");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "nutzer_coownership");

            migrationBuilder.DropTable(
                name: "nutzer_personen");

            migrationBuilder.DropTable(
                name: "nutzer_quadratmeter");
        }
    }
}
