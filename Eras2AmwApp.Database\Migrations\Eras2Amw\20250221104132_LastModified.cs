﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Eras2AmwApp.Database.Migrations.Eras2Amw
{
    public partial class LastModified : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "new_persons_communications",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: true, defaultValueSql: "datetime('now', 'localtime')"), // Use local time
                    address = table.Column<string>(nullable: false),
                    note = table.Column<string>(nullable: true),
                    person_guid = table.Column<Guid>(nullable: false),
                    communication_feature_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_persons_communications", x => x.guid);
                    table.ForeignKey(
                        name: "fk_communication_features_new_persons_communications_communication_feature_guid",
                        column: x => x.communication_feature_guid,
                        principalTable: "communication_features",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_persons_new_persons_communications_person_guid",
                        column: x => x.person_guid,
                        principalTable: "persons",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_persons_communications (guid, last_modified, address, note, person_guid, communication_feature_guid) " +
                "SELECT guid, last_modified, address, note, person_guid, communication_feature_guid FROM persons_communications;");

            // Drop the old table
            migrationBuilder.DropTable(name: "persons_communications");

            // Rename the new table to the original table name
            migrationBuilder.RenameTable(name: "new_persons_communications", newName: "persons_communications");

            // Create a new table with the updated column definition
            migrationBuilder.CreateTable(
                name: "new_persons_abrechnungseinheiten",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: true, defaultValueSql: "datetime('now', 'localtime')"), // Use local time
                    note = table.Column<string>(nullable: true),
                    business_position = table.Column<string>(nullable: false),
                    is_created_by_app = table.Column<bool>(nullable: false),
                    abrechnungseinheit_guid = table.Column<Guid>(nullable: false),
                    person_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_persons_abrechnungseinheiten", x => x.guid);
                    table.ForeignKey(
                        name: "fk_abrechnungseinheiten_new_persons_abrechnungseinheiten_abrechnungseinheit_guid",
                        column: x => x.abrechnungseinheit_guid,
                        principalTable: "abrechnungseinheiten",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_persons_new_persons_abrechnungseinheiten_person_guid",
                        column: x => x.person_guid,
                        principalTable: "persons",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_persons_abrechnungseinheiten (guid, last_modified, note, business_position, is_created_by_app, abrechnungseinheit_guid, person_guid) " +
                "SELECT guid, last_modified, note, business_position, is_created_by_app, abrechnungseinheit_guid, person_guid FROM persons_abrechnungseinheiten;");

            // Drop the old table
            migrationBuilder.DropTable(name: "persons_abrechnungseinheiten");

            // Rename the new table to match the original table name
            migrationBuilder.RenameTable(name: "new_persons_abrechnungseinheiten", newName: "persons_abrechnungseinheiten");

            migrationBuilder.CreateTable(
                name: "new_persons",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: true, defaultValueSql: "datetime('now', 'localtime')"), // Use local time
                    lastname_1 = table.Column<string>(nullable: false),
                    lastname_2 = table.Column<string>(nullable: true),
                    lastname_3 = table.Column<string>(nullable: true),
                    firstname_1 = table.Column<string>(nullable: true),
                    firstname_2 = table.Column<string>(nullable: true),
                    note = table.Column<string>(nullable: true),
                    salutation_id = table.Column<int>(nullable: false),
                    title_id = table.Column<int>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_persons", x => x.guid);
                    table.ForeignKey(
                        name: "fk_salutations_new_persons_id",
                        column: x => x.salutation_id,
                        principalTable: "salutations",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_titles_new_persons_id",
                        column: x => x.title_id,
                        principalTable: "titles",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_persons (guid, last_modified, lastname_1, lastname_2, lastname_3, firstname_1, firstname_2, note, salutation_id, title_id) " +
                "SELECT guid, last_modified, lastname_1, lastname_2, lastname_3, firstname_1, firstname_2, note, salutation_id, title_id FROM persons;");

            // Drop the old table
            migrationBuilder.DropTable(name: "persons");

            // Rename the new table to match the original table name
            migrationBuilder.RenameTable(name: "new_persons", newName: "persons");

            migrationBuilder.CreateTable(
                name: "new_orders_states",
                columns: table => new
                {
                    order_guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "datetime('now', 'localtime')"), // Fix default to local time
                    kinds = table.Column<string>(nullable: false),
                    process_state = table.Column<int>(nullable: false),
                    completed_date = table.Column<DateTime>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_orders_states", x => x.order_guid);
                    table.ForeignKey(
                        name: "fk_orders_new_orders_states_order_guid",
                        column: x => x.order_guid,
                        principalTable: "orders",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_orders_states (order_guid, last_modified, kinds, process_state, completed_date) " +
                "SELECT order_guid, last_modified, kinds, process_state, completed_date FROM orders_states;");

            // Drop the old table
            migrationBuilder.DropTable(name: "orders_states");

            // Rename the new table to match the original table name
            migrationBuilder.RenameTable(name: "new_orders_states", newName: "orders_states");

            migrationBuilder.CreateTable(
                name: "new_orders",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    ip_address = table.Column<string>(nullable: true),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "datetime('now', 'localtime')"), // Fix default to local time
                    number = table.Column<string>(nullable: false),
                    label = table.Column<string>(nullable: false),
                    note = table.Column<string>(nullable: true),
                    is_signature_required = table.Column<bool>(nullable: false),
                    created_date = table.Column<string>(nullable: false),
                    abrechnungseinheit_guid = table.Column<Guid>(nullable: false),
                    orderer_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_orders", x => x.guid);
                    table.ForeignKey(
                        name: "fk_abrechnungseinheiten_new_orders_order_guid",
                        column: x => x.abrechnungseinheit_guid,
                        principalTable: "abrechnungseinheiten",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_orders_orderers_new_orders_orderer_guid",
                        column: x => x.orderer_guid,
                        principalTable: "orderers",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_orders (guid, ip_address, last_modified, number, label, note, is_signature_required, created_date, abrechnungseinheit_guid, orderer_guid) " +
                "SELECT guid, ip_address, last_modified, number, label, note, is_signature_required, created_date, abrechnungseinheit_guid, orderer_guid FROM orders;");

            // Drop the old table
            migrationBuilder.DropTable(name: "orders");

            // Rename the new table to match the original table name
            migrationBuilder.RenameTable(name: "new_orders", newName: "orders");

            migrationBuilder.CreateTable(
                name: "new_order_positions",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "datetime('now', 'localtime')"), // Fix default to local time
                    note = table.Column<string>(nullable: true),
                    required_quantity = table.Column<int>(nullable: false),
                    actual_quantity = table.Column<int>(nullable: false),
                    order_guid = table.Column<Guid>(nullable: false),
                    article_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_order_positions", x => x.guid);
                    table.ForeignKey(
                        name: "fk_articles_new_order_positions_article_guid",
                        column: x => x.article_guid,
                        principalTable: "articles",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_orders_new_order_positions_order_guid",
                        column: x => x.order_guid,
                        principalTable: "orders",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_order_positions (guid, last_modified, note, required_quantity, actual_quantity, order_guid, article_guid) " +
                "SELECT guid, last_modified, note, required_quantity, actual_quantity, order_guid, article_guid FROM order_positions;");

            // Drop the old table
            migrationBuilder.DropTable(name: "order_positions");

            // Rename the new table to match the original table name
            migrationBuilder.RenameTable(name: "new_order_positions", newName: "order_positions");

            migrationBuilder.CreateTable(
                name: "new_nutzer_quadratmeter",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "datetime('now', 'localtime')"), // Fix default to local time
                    square_meters_hzg = table.Column<decimal>(type: "decimal(9,2)", nullable: true),
                    square_meters_ww = table.Column<decimal>(type: "decimal(9,2)", nullable: true),
                    square_meters_nk = table.Column<decimal>(type: "decimal(9,2)", nullable: true),
                    range_from = table.Column<DateTime>(nullable: false),
                    range_to = table.Column<DateTime>(nullable: true),
                    nutzer_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_nutzer_quadratmeter", x => x.guid);
                    table.ForeignKey(
                        name: "fk_nutzerquadratmeter_nutzer_guid",
                        column: x => x.nutzer_guid,
                        principalTable: "nutzer",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_nutzer_quadratmeter (guid, last_modified, square_meters_hzg, square_meters_ww, square_meters_nk, range_from, range_to, nutzer_guid) " +
                "SELECT guid, last_modified, square_meters_hzg, square_meters_ww, square_meters_nk, range_from, range_to, nutzer_guid FROM nutzer_quadratmeter;");

            // Drop the old table
            migrationBuilder.DropTable(name: "nutzer_quadratmeter");

            // Rename the new table to match the original table name
            migrationBuilder.RenameTable(name: "new_nutzer_quadratmeter", newName: "nutzer_quadratmeter");

            migrationBuilder.CreateTable(
                name: "new_nutzer_personen",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "datetime('now', 'localtime')"), // Fix default to local time
                    range_from = table.Column<DateTime>(nullable: false),
                    range_to = table.Column<DateTime>(nullable: true),
                    number_of_people = table.Column<int>(nullable: true),
                    nutzer_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_nutzer_personen", x => x.guid);
                    table.ForeignKey(
                        name: "fk_nutzerpersonen_nutzer_guid",
                        column: x => x.nutzer_guid,
                        principalTable: "nutzer",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_nutzer_personen (guid, last_modified, range_from, range_to, number_of_people, nutzer_guid) " +
                "SELECT guid, last_modified, range_from, range_to, number_of_people, nutzer_guid FROM nutzer_personen;");

            // Drop the old table
            migrationBuilder.DropTable(name: "nutzer_personen");

            // Rename the new table to match the original table name
            migrationBuilder.RenameTable(name: "new_nutzer_personen", newName: "nutzer_personen");

            migrationBuilder.CreateTable(
                name: "new_nutzer_coownership",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "datetime('now', 'localtime')"), // Fix default to local time
                    share_value = table.Column<decimal>(nullable: true),
                    range_from = table.Column<DateTime>(nullable: false),
                    range_to = table.Column<DateTime>(nullable: true),
                    nutzer_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_nutzer_coownership", x => x.guid);
                    table.ForeignKey(
                        name: "fk_nutzercoownership_nutzer_guid",
                        column: x => x.nutzer_guid,
                        principalTable: "nutzer",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_nutzer_coownership (guid, last_modified, share_value, range_from, range_to, nutzer_guid) " +
                "SELECT guid, last_modified, share_value, range_from, range_to, nutzer_guid FROM nutzer_coownership;");

            // Drop the old table
            migrationBuilder.DropTable(name: "nutzer_coownership");

            // Rename the new table to match the original table name
            migrationBuilder.RenameTable(name: "new_nutzer_coownership", newName: "nutzer_coownership");

            migrationBuilder.CreateTable(
                name: "new_nutzer_communications",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "datetime('now', 'localtime')"), // Fix default to local time
                    address = table.Column<string>(nullable: false),
                    note = table.Column<string>(nullable: true),
                    nutzer_guid = table.Column<Guid>(nullable: false),
                    communication_feature_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_nutzer_communications", x => x.guid);
                    table.ForeignKey(
                        name: "fk_communication_features_nutzer_communications_communication_feature_guid",
                        column: x => x.communication_feature_guid,
                        principalTable: "communication_features",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_nutzer_nutzer_communications_nutzer_guid",
                        column: x => x.nutzer_guid,
                        principalTable: "nutzer",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_nutzer_communications (guid, last_modified, address, note, nutzer_guid, communication_feature_guid) " +
                "SELECT guid, last_modified, address, note, nutzer_guid, communication_feature_guid FROM nutzer_communications;");

            // Drop the old table
            migrationBuilder.DropTable(name: "nutzer_communications");

            // Rename the new table to match the original table name
            migrationBuilder.RenameTable(name: "new_nutzer_communications", newName: "nutzer_communications");

            migrationBuilder.CreateTable(
                name: "new_nutzer",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "datetime('now', 'localtime')"), // Fix default to local time
                    kind = table.Column<int>(nullable: false),
                    move_in_date = table.Column<DateTime>(nullable: false),
                    move_out_date = table.Column<DateTime>(nullable: true),
                    name1 = table.Column<string>(nullable: true),
                    name2 = table.Column<string>(nullable: true),
                    name3 = table.Column<string>(nullable: true),
                    name4 = table.Column<string>(nullable: true),
                    note = table.Column<string>(nullable: true),
                    next_appointment_date = table.Column<DateTime>(nullable: true),
                    is_created_by_app = table.Column<bool>(nullable: false),
                    nutzeinheit_guid = table.Column<Guid>(nullable: false),
                    title_id = table.Column<int>(nullable: true),
                    salutation_id = table.Column<int>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_nutzer", x => x.guid);
                    table.ForeignKey(
                        name: "fk_nutzeinheiten_nutzer_nutzeinheit_guid",
                        column: x => x.nutzeinheit_guid,
                        principalTable: "nutzeinheiten",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_salutations_nutzer_salutation_id",
                        column: x => x.salutation_id,
                        principalTable: "salutations",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_titles_nutzer_title_id",
                        column: x => x.title_id,
                        principalTable: "titles",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_nutzer (guid, last_modified, kind, move_in_date, move_out_date, name1, name2, name3, name4, note, next_appointment_date, is_created_by_app, nutzeinheit_guid, title_id, salutation_id) " +
                "SELECT guid, last_modified, kind, move_in_date, move_out_date, name1, name2, name3, name4, note, next_appointment_date, is_created_by_app, nutzeinheit_guid, title_id, salutation_id FROM nutzer;");

            // Drop the old table
            migrationBuilder.DropTable(name: "nutzer");

            // Rename the new table to match the original table name
            migrationBuilder.RenameTable(name: "new_nutzer", newName: "nutzer");

            migrationBuilder.CreateTable(
                name: "new_nutzeinheiten_order_states",
                columns: table => new
                {
                    nutzeinheit_guid = table.Column<Guid>(nullable: false),
                    order_guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "datetime('now', 'localtime')"), // Fix default to local time
                    order_kinds = table.Column<string>(nullable: true),
                    process_state = table.Column<int>(nullable: false),
                    completed_date = table.Column<DateTime>(nullable: true),
                    os_version = table.Column<string>(nullable: true),
                    app_version = table.Column<string>(nullable: true),
                    os_platform = table.Column<string>(nullable: true),
                    app_device_number = table.Column<string>(nullable: true),
                    amw_info_key_guid = table.Column<Guid>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_nutzeinheiten_order_states", x => new { x.order_guid, x.nutzeinheit_guid });
                    table.ForeignKey(
                        name: "fk_amw_info_keys_nutzeinheit_order_states_amw_info_key_guid",
                        column: x => x.amw_info_key_guid,
                        principalTable: "amw_info_keys",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_nutzeinheiten_nutzeinheiten_order_states_nutzeinheit_guid",
                        column: x => x.nutzeinheit_guid,
                        principalTable: "nutzeinheiten",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_orders_nutzeinheiten_order_states_order_guid",
                        column: x => x.order_guid,
                        principalTable: "orders",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_nutzeinheiten_order_states (nutzeinheit_guid, order_guid, last_modified, order_kinds, process_state, completed_date, os_version, app_version, os_platform, app_device_number, amw_info_key_guid) " +
                "SELECT nutzeinheit_guid, order_guid, last_modified, order_kinds, process_state, completed_date, os_version, app_version, os_platform, app_device_number, amw_info_key_guid FROM nutzeinheiten_order_states;");

            // Drop the old table
            migrationBuilder.DropTable(name: "nutzeinheiten_order_states");

            // Rename the new table to match the original table name
            migrationBuilder.RenameTable(name: "new_nutzeinheiten_order_states", newName: "nutzeinheiten_order_states");

            migrationBuilder.CreateTable(
                name: "new_nutzeinheiten",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    ip_address = table.Column<string>(nullable: true),
                    last_modified = table.Column<DateTime>(nullable: true, defaultValueSql: "datetime('now', 'localtime')"), // Fix default to local time
                    number = table.Column<string>(nullable: false),
                    location = table.Column<string>(nullable: true),
                    walk_sequence = table.Column<string>(nullable: true),
                    note = table.Column<string>(nullable: true),
                    is_created_by_app = table.Column<bool>(nullable: false),
                    abrechnungseinheit_guid = table.Column<Guid>(nullable: false),
                    address_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_nutzeinheiten", x => x.guid);
                    table.ForeignKey(
                        name: "fk_abrechnungseinheiten_nutzeinheiten_abrechnungseinheit_guid",
                        column: x => x.abrechnungseinheit_guid,
                        principalTable: "abrechnungseinheiten",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_addresses_nutzeinheiten_address_guid",
                        column: x => x.address_guid,
                        principalTable: "addresses",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                });

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_nutzeinheiten (guid, ip_address, last_modified, number, location, walk_sequence, note, is_created_by_app, abrechnungseinheit_guid, address_guid) " +
                "SELECT guid, ip_address, last_modified, number, location, walk_sequence, note, is_created_by_app, abrechnungseinheit_guid, address_guid FROM nutzeinheiten;");

            // Drop the old table
            migrationBuilder.DropTable(name: "nutzeinheiten");

            // Rename the new table to match the original table name
            migrationBuilder.RenameTable(name: "new_nutzeinheiten", newName: "nutzeinheiten");

            migrationBuilder.CreateTable(
                name: "new_devices_order_states",
                columns: table => new
                {
                    device_guid = table.Column<Guid>(nullable: false),
                    order_guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "datetime('now', 'localtime')"), // Fix default to local time
                    order_kind = table.Column<int>(nullable: false),
                    process_state = table.Column<int>(nullable: false),
                    completed_date = table.Column<DateTime>(nullable: true),
                    amw_info_key_guid = table.Column<Guid>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_devices_order_states", x => new { x.order_guid, x.device_guid });
                    table.ForeignKey(
                        name: "fk_amw_info_keys_device_order_states_amw_info_key_guid",
                        column: x => x.amw_info_key_guid,
                        principalTable: "amw_info_keys",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_devices_devices_order_states_device_guid",
                        column: x => x.device_guid,
                        principalTable: "devices",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_orders_devices_order_states_order_guid",
                        column: x => x.order_guid,
                        principalTable: "orders",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_devices_order_states (device_guid, order_guid, last_modified, order_kind, process_state, completed_date, amw_info_key_guid) " +
                "SELECT device_guid, order_guid, last_modified, order_kind, process_state, completed_date, amw_info_key_guid FROM devices_order_states;");

            // Drop the old table
            migrationBuilder.DropTable(name: "devices_order_states");

            // Rename the new table to match the original table name
            migrationBuilder.RenameTable(name: "new_devices_order_states", newName: "devices_order_states");

            migrationBuilder.CreateTable(
                name: "new_devices_additional_articles",
                columns: table => new
                {
                    device_guid = table.Column<Guid>(nullable: false),
                    additional_article_guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: false, defaultValueSql: "datetime('now', 'localtime')"), // Fix default to local time
                    quantity = table.Column<decimal>(type: "decimal(8, 2)", nullable: false),
                    is_created_by_app = table.Column<bool>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_devices_additional_articles", x => new { x.device_guid, x.additional_article_guid });
                    table.ForeignKey(
                        name: "fk_additional_articles_devices_additional_articles_additional_article_guid",
                        column: x => x.additional_article_guid,
                        principalTable: "additional_articles",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_devices_devices_additional_articles_device_guid",
                        column: x => x.device_guid,
                        principalTable: "devices",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_devices_additional_articles (device_guid, additional_article_guid, last_modified, quantity, is_created_by_app) " +
                "SELECT device_guid, additional_article_guid, last_modified, quantity, is_created_by_app FROM devices_additional_articles;");

            // Drop the old table
            migrationBuilder.DropTable(name: "devices_additional_articles");

            // Rename the new table to match the original table name
            migrationBuilder.RenameTable(name: "new_devices_additional_articles", newName: "devices_additional_articles");

            migrationBuilder.CreateTable(
                name: "new_devices",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: true, defaultValueSql: "datetime('now', 'localtime')"), // Fix default to local time
                    number = table.Column<string>(nullable: false),
                    label = table.Column<string>(nullable: true),
                    description = table.Column<string>(nullable: true),
                    installation_date = table.Column<DateTime>(nullable: false),
                    deinstallation_date = table.Column<DateTime>(nullable: true),
                    calibration_date = table.Column<DateTime>(nullable: true),
                    unit = table.Column<int>(nullable: true),
                    note = table.Column<string>(nullable: true),
                    subtraction = table.Column<bool>(nullable: false),
                    group_identifier = table.Column<string>(maxLength: 5, nullable: true),
                    estimation = table.Column<int>(nullable: false),
                    first_activation = table.Column<DateTime>(nullable: true),
                    firmware_version = table.Column<string>(nullable: true),
                    nutzeinheit_guid = table.Column<Guid>(nullable: false),
                    room_guid = table.Column<Guid>(nullable: false),
                    ongoing_number = table.Column<string>(nullable: false),
                    is_leased = table.Column<bool>(nullable: false),
                    is_maintained = table.Column<bool>(nullable: false),
                    is_created_by_app = table.Column<bool>(nullable: false),
                    device_catalog_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_devices", x => x.guid);
                    table.ForeignKey(
                        name: "fk_device_catalogs_devices_device_catalog_guid",
                        column: x => x.device_catalog_guid,
                        principalTable: "device_catalogs",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_nutzeinheiten_devices_nutzeinheit_guid",
                        column: x => x.nutzeinheit_guid,
                        principalTable: "nutzeinheiten",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_rooms_devices_room_guid",
                        column: x => x.room_guid,
                        principalTable: "rooms",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                });

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_devices (guid, last_modified, number, label, description, installation_date, deinstallation_date, calibration_date, unit, note, subtraction, group_identifier, estimation, first_activation, firmware_version, nutzeinheit_guid, room_guid, ongoing_number, is_leased, is_maintained, is_created_by_app, device_catalog_guid) " +
                "SELECT guid, last_modified, number, label, description, installation_date, deinstallation_date, calibration_date, unit, note, subtraction, group_identifier, estimation, first_activation, firmware_version, nutzeinheit_guid, room_guid, ongoing_number, is_leased, is_maintained, is_created_by_app, device_catalog_guid FROM devices;");

            // Drop the old table
            migrationBuilder.DropTable(name: "devices");

            // Rename the new table to match the original table name
            migrationBuilder.RenameTable(name: "new_devices", newName: "devices");

            migrationBuilder.CreateTable(
                name: "new_device_consumptions",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    last_modified = table.Column<DateTime>(nullable: true, defaultValueSql: "datetime('now', 'localtime')"), // Fix default to local time
                    reading_date = table.Column<DateTime>(nullable: true),
                    factor = table.Column<double>(nullable: false),
                    reading = table.Column<double>(nullable: true),
                    consumption = table.Column<double>(nullable: true),
                    change_date = table.Column<DateTime>(nullable: true),
                    estimation_kind = table.Column<int>(nullable: false),
                    state = table.Column<int>(nullable: false),
                    note = table.Column<string>(nullable: true),
                    is_reconstructed = table.Column<bool>(nullable: false),
                    manual = table.Column<double>(nullable: true),
                    origin = table.Column<int>(nullable: false),
                    air_temperature = table.Column<decimal>(type: "decimal(4,1)", nullable: true),
                    radiator_temperature = table.Column<decimal>(type: "decimal(5,1)", nullable: true),
                    last_error_date = table.Column<DateTime>(nullable: true),
                    error = table.Column<string>(nullable: true),
                    ampoule_colour = table.Column<string>(nullable: true),
                    device_guid = table.Column<Guid>(nullable: false),
                    readind_kind_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_device_consumptions", x => x.guid);
                    table.ForeignKey(
                        name: "fk_devices_devices_consumptions_device_guid",
                        column: x => x.device_guid,
                        principalTable: "devices",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "fk_reading_kinds_device_consumptions_reading_kind_guid",
                        column: x => x.readind_kind_guid,
                        principalTable: "reading_kinds",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                });

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_device_consumptions (guid, last_modified, reading_date, factor, reading, consumption, change_date, estimation_kind, state, note, is_reconstructed, manual, origin, air_temperature, radiator_temperature, last_error_date, error, ampoule_colour, device_guid, readind_kind_guid) " +
                "SELECT guid, last_modified, reading_date, factor, reading, consumption, change_date, estimation_kind, state, note, is_reconstructed, manual, origin, air_temperature, radiator_temperature, last_error_date, error, ampoule_colour, device_guid, readind_kind_guid FROM device_consumptions;");

            // Drop the old table
            migrationBuilder.DropTable(name: "device_consumptions");

            // Rename the new table to match the original table name
            migrationBuilder.RenameTable(name: "new_device_consumptions", newName: "device_consumptions");

            migrationBuilder.CreateTable(
                name: "new_abrechnungseinheiten",
                columns: table => new
                {
                    guid = table.Column<Guid>(nullable: false),
                    ip_address = table.Column<string>(nullable: true),
                    last_modified = table.Column<DateTime>(nullable: true, defaultValueSql: "datetime('now', 'localtime')"), // Fix default to local time
                    number = table.Column<string>(nullable: false),
                    note = table.Column<string>(nullable: true),
                    note_internal = table.Column<string>(nullable: true),
                    note_legionella = table.Column<string>(nullable: true),
                    customer_guid = table.Column<Guid>(nullable: false),
                    address_guid = table.Column<Guid>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_abrechnungseinheiten", x => x.guid);
                    table.ForeignKey(
                        name: "fk_addresses_abrechnungseinheiten_address_guid",
                        column: x => x.address_guid,
                        principalTable: "addresses",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "fk_customers_abrechnungseinheiten_customer_guid",
                        column: x => x.customer_guid,
                        principalTable: "customers",
                        principalColumn: "guid",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.AddColumn<DateTime>(
                name: "billing_period_end",
                table: "new_abrechnungseinheiten",
                nullable: true);

            // Copy data from old table to new table
            migrationBuilder.Sql("INSERT INTO new_abrechnungseinheiten (guid, ip_address, last_modified, number, note, note_internal, note_legionella, customer_guid, address_guid,billing_period_end) " +
                "SELECT guid, ip_address, last_modified, number, note, note_internal, note_legionella, customer_guid, address_guid, billing_period_end FROM abrechnungseinheiten;");

            // Drop the old table
            migrationBuilder.DropTable(name: "abrechnungseinheiten");

            // Rename the new table to match the original table name
            migrationBuilder.RenameTable(name: "new_abrechnungseinheiten", newName: "abrechnungseinheiten");

            var updateLastModifiedAbrechnungseinheiten = @"
                DROP TRIGGER IF EXISTS after_update_last_modified_abrechnungseinheiten;
                CREATE TRIGGER [after_update_last_modified_abrechnungseinheiten]
                AFTER UPDATE
                ON abrechnungseinheiten
                FOR EACH ROW
                    BEGIN
                UPDATE abrechnungseinheiten SET last_modified = datetime('now', 'localtime') WHERE Guid = old.Guid;
                END
            ";
            migrationBuilder.Sql(updateLastModifiedAbrechnungseinheiten);

            var updateLastModifiedPersonsAbrechnungseinheiten = @"
                DROP TRIGGER IF EXISTS after_update_last_modified_persons_abrechnungseinheiten;
                CREATE TRIGGER [after_update_last_modified_persons_abrechnungseinheiten]
                AFTER UPDATE
                ON persons_abrechnungseinheiten
                FOR EACH ROW
                    BEGIN
                UPDATE persons_abrechnungseinheiten SET last_modified = datetime('now', 'localtime') WHERE Guid = old.Guid;
                END
            ";
            migrationBuilder.Sql(updateLastModifiedPersonsAbrechnungseinheiten);

            var updateLastModifiedPersons = @"
                DROP TRIGGER IF EXISTS after_update_last_modified_persons;
                CREATE TRIGGER [after_update_last_modified_persons]
                AFTER UPDATE
                ON persons
                FOR EACH ROW
                    BEGIN
                UPDATE persons SET last_modified = datetime('now', 'localtime') WHERE Guid = old.Guid;
                END
            ";
            migrationBuilder.Sql(updateLastModifiedPersons);

            var updateLastModifiedPersonCommunications = @"
                DROP TRIGGER IF EXISTS after_update_last_modified_persons_communications;
                CREATE TRIGGER [after_update_last_modified_persons_communications]
                AFTER UPDATE
                ON persons_communications
                FOR EACH ROW
                    BEGIN
                UPDATE persons_communications SET last_modified = datetime('now', 'localtime') WHERE Guid = old.Guid;
                END
            ";
            migrationBuilder.Sql(updateLastModifiedPersonCommunications);

            var updateLastModifiedNutzeinheiten = @"
                DROP TRIGGER IF EXISTS after_update_last_modified_nutzeinheiten;
                CREATE TRIGGER [after_update_last_modified_nutzeinheiten]
                AFTER UPDATE
                ON nutzeinheiten
                FOR EACH ROW
                    BEGIN
                UPDATE nutzeinheiten SET last_modified = datetime('now', 'localtime') WHERE Guid = old.Guid;
                END
            ";
            migrationBuilder.Sql(updateLastModifiedNutzeinheiten);

            var updateLastModifiedNutzer = @"
                DROP TRIGGER IF EXISTS after_update_last_modified_nutzer;
                CREATE TRIGGER [after_update_last_modified_nutzer]
                AFTER UPDATE
                ON nutzer
                FOR EACH ROW
                    BEGIN
                UPDATE nutzer SET last_modified = datetime('now', 'localtime') WHERE Guid = old.Guid;
                END
                ";
            migrationBuilder.Sql(updateLastModifiedNutzer);

            var updateLastModifiedNutzerCommunications = @"
                DROP TRIGGER IF EXISTS after_update_last_modified_nutzer_communications;
                CREATE TRIGGER [after_update_last_modified_nutzer_communications]
                AFTER UPDATE
                ON nutzer_communications
                FOR EACH ROW
                    BEGIN
                UPDATE nutzer_communications SET last_modified = datetime('now', 'localtime') WHERE Guid = old.Guid;
                END
            ";
            migrationBuilder.Sql(updateLastModifiedNutzerCommunications);

            var updateLastModifiedDevices = @"
                DROP TRIGGER IF EXISTS after_update_last_modified_devices;
                CREATE TRIGGER [after_update_last_modified_devices]
                AFTER UPDATE
                ON devices
                FOR EACH ROW
                    BEGIN
                UPDATE devices SET last_modified = datetime('now', 'localtime') WHERE Guid = old.Guid;
                END
            ";
            migrationBuilder.Sql(updateLastModifiedDevices);

            var updateLastModifiedDeviceConsumtions = @"
                DROP TRIGGER IF EXISTS after_update_last_modified_device_consumptions;  
                CREATE TRIGGER [after_update_last_modified_device_consumptions]
                AFTER UPDATE
                ON device_consumptions
                FOR EACH ROW
                    BEGIN
                UPDATE device_consumptions SET last_modified = datetime('now', 'localtime') WHERE Guid = old.Guid;
                END
            ";
            migrationBuilder.Sql(updateLastModifiedDeviceConsumtions);

            var updateLastModifiedOrders = @"
                DROP TRIGGER IF EXISTS after_update_last_modified_orders;
                CREATE TRIGGER [after_update_last_modified_orders]
                AFTER UPDATE
                ON orders
                FOR EACH ROW
                    BEGIN
                UPDATE orders SET last_modified = datetime('now', 'localtime') WHERE Guid = old.Guid;
                END
            ";
            migrationBuilder.Sql(updateLastModifiedOrders);

            var updateLastModifiedOrderPositions = @"
                DROP TRIGGER IF EXISTS after_update_last_modified_order_positions;  
                CREATE TRIGGER [after_update_last_modified_order_positions]
                AFTER UPDATE
                ON order_positions
                FOR EACH ROW
                    BEGIN
                UPDATE order_positions SET last_modified = datetime('now', 'localtime') WHERE Guid = old.Guid;
                END
            ";
            migrationBuilder.Sql(updateLastModifiedOrderPositions);

            var updateLastModifiedOrdersStates = @"
                DROP TRIGGER IF EXISTS after_update_last_modified_orders_states;
                CREATE TRIGGER [after_update_last_modified_orders_states]
                AFTER UPDATE
                ON orders_states
                FOR EACH ROW
                    BEGIN
                UPDATE orders_states SET last_modified = datetime('now', 'localtime') WHERE order_guid = old.order_guid;
                END
            ";
            migrationBuilder.Sql(updateLastModifiedOrdersStates);

            var updateLastModifiedNutzeinheitenOrderStates = @"
                DROP TRIGGER IF EXISTS after_update_last_modified_nutzeinheiten_order_states;   
                CREATE TRIGGER [after_update_last_modified_nutzeinheiten_order_states]
                AFTER UPDATE
                ON nutzeinheiten_order_states
                FOR EACH ROW
                    BEGIN
                UPDATE nutzeinheiten_order_states SET last_modified = datetime('now', 'localtime') WHERE nutzeinheit_guid = old.nutzeinheit_guid AND order_guid = old.order_guid;
                END
            ";
            migrationBuilder.Sql(updateLastModifiedNutzeinheitenOrderStates);

            var updateLastModifiedDevicesOrderStates = @"
                DROP TRIGGER IF EXISTS after_update_last_modified_devices_order_states;
                CREATE TRIGGER [after_update_last_modified_devices_order_states]
                AFTER UPDATE
                ON devices_order_states
                FOR EACH ROW
                    BEGIN
                UPDATE devices_order_states SET last_modified = datetime('now', 'localtime') WHERE device_guid = old.device_guid AND order_guid = old.order_guid;
                END
            ";
            migrationBuilder.Sql(updateLastModifiedDevicesOrderStates);

            var updateLastModifiedDevicesAdditionalArticles = @"
                DROP TRIGGER IF EXISTS after_update_last_modified_devices_additional_articles;
                CREATE TRIGGER [after_update_last_modified_devices_additional_articles]
                AFTER UPDATE
                ON devices_additional_articles
                FOR EACH ROW
                    BEGIN
                UPDATE devices_additional_articles SET last_modified = datetime('now', 'localtime') WHERE device_guid = old.device_guid AND additional_article_guid = old.additional_article_guid;
                END
            ";
            migrationBuilder.Sql(updateLastModifiedDevicesAdditionalArticles);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
        }
    }
}
