﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Eras2AmwApp.Database.Migrations.Eras2Amw
{
    public partial class CreatedByAppNutzerDependencies : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "is_created_by_app",
                table: "nutzer_quadratmeter",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "is_created_by_app",
                table: "nutzer_personen",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "is_created_by_app",
                table: "nutzer_coownership",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "is_created_by_app",
                table: "nutzer_quadratmeter");

            migrationBuilder.DropColumn(
                name: "is_created_by_app",
                table: "nutzer_personen");

            migrationBuilder.DropColumn(
                name: "is_created_by_app",
                table: "nutzer_coownership");
        }
    }
}
