﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceClass.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Enums
{
    using Endiancode.Utilities.Attributes;

    public enum DeviceClass
    {
        [Description("HKV")]
        HKV,
        [Description("WWZ")]
        WWZ, 
        [Description("KWZ")]
        KWZ, 
        [Description("WZ")]
        WZ, 
        [Description("WMZ")]
        WMZ, 
        [Description("KMZ")]
        KMZ, 
        [Description("RM")]
        RM, 
        [Description("SZ")]
        SZ, 
        [Description("FM")]
        FM, 
        [Description("Sonstige")]
        Sonstige, 
        [Description("")]
        Null, 
        [Description("ENT")]
        ENT
    }
}