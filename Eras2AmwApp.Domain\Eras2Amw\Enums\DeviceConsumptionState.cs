﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceStateKind.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Enums
{
    using Endiancode.Utilities.Attributes;

    public enum DeviceConsumptionState
    { 
        [Description("")]
        Null,
        [Description("Rauchmelder geprüft und in Ordnung")]
        RMInOrdnung,
        [Description("Rauchmelder geprüft und fehlerhaft")]
        RMFehler,
        [Description("Rauchmelder konnte nichte geprüft werden")]
        RMNichtGepr,
        [Description("Rauchmelder geprüft, Rauchmelder fehlerhaft und getauscht")]
        RMFehlerTausch,
        [Description("Kein Zutritt zur Wohnung")]
        KeinWohnungszutritt,
        [Description("Defekt")]
        Defekt,
        [Description("Falsch laufend")]
        FalschLaufend,
        [Description("Überlauf")]
        Überlauf,
        [Description("Eichung beachten")]
        EichungBeachten,
        [Description("Probe entnommen")]
        ProbeEntnommen,
        [Description("Probe an Labor verschickt")]
        ProbeZumLabor,
        [Description("Probe positiv")]
        ProbePositiv,
        [Description("Probe negativ")]
        ProbeNegativ
    }
}