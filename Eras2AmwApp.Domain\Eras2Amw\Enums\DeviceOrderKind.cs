﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceOrderKind.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Enums
{
    using Endiancode.Utilities.Attributes;

    public enum DeviceOrderKind
    {
        [Description("Montage")]
        Assembly,
        [Description("Wartung")]
        Maintenance,
        [Description("Eichaustausch")]
        Exchange,
        [Description("Ablesung")]
        Reading,
        [Description("Prüfung")]
        Inspection,
        [Description("Hauptablesung")]
        MainReading
    }
}