﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="EinheitKind.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Enums
{
    using Endiancode.Utilities.Attributes;

    public enum UnitKind
    {
        [Description("l")]
        L,
        [Description("kg")]
        Kg,
        [Description("m³")]
        CBM,
        [Description("Stck")]
        Stck,
        [Description("kWh")]
        KWH,
        [Description("MWh")]
        MWH,
        [Description("GWh")]
        GWH,
        [Description("t")]
        T,
        [Description("Rm")]
        RM,
        [Description("Eht")]
        Einheit,
        [Description("m²")]
        Qm,
        [Description("GJ")]
        GJ,
        [Description("MJ")]
        MJ,
        [Description("")]
        Null
    }
}