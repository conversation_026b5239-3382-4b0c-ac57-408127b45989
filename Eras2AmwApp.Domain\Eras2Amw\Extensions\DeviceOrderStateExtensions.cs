﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceOrderStateExtensions.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Extensions
{
    using Models;

    public static class DeviceOrderStateExtensions
    {
        public static bool IsCompleted(this DeviceOrderState deviceOrderState) => deviceOrderState.ProcessState.IsCompleted();

        public static bool InProgress(this DeviceOrderState deviceOrderState) => deviceOrderState.ProcessState.InProgress();

        public static bool IsUpdating(this DeviceOrderState deviceOrderState) => deviceOrderState.ProcessState.IsUpdating();

        public static bool IsCreating(this DeviceOrderState deviceOrderState) => deviceOrderState.ProcessState.IsCreating();

        public static bool IsClosed(this DeviceOrderState deviceOrderState)
        {
            return deviceOrderState.IsUpdating() || deviceOrderState.IsCompleted() || deviceOrderState.IsCreating();
        }
    }
}