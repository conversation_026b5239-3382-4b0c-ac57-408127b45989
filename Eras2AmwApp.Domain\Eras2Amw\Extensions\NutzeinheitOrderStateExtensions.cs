﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitOrderState.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Extensions
{
    using System.Dynamic;
    using Models;

    public static class NutzeinheitOrderStateExtensions
    {
        public static bool IsUpdating(this NutzeinheitOrderState nutzeinheitOrderState) => nutzeinheitOrderState.ProcessState.IsUpdating();

        public static bool IsCompleted(this NutzeinheitOrderState nutzeinheitOrderState) => nutzeinheitOrderState.ProcessState.IsCompleted();

        public static bool InProgress(this NutzeinheitOrderState nutzeinheitOrderState) => nutzeinheitOrderState.ProcessState.InProgress();

        public static bool IsCreating(this NutzeinheitOrderState nutzeinheitOrderState) => nutzeinheitOrderState.ProcessState.IsCreating();

        public static bool CouldReopened(this NutzeinheitOrderState nutzeinheitOrderState)
        {
            return nutzeinheitOrderState.IsUpdating() || nutzeinheitOrderState.IsCompleted() || nutzeinheitOrderState.IsCreating();
        }
    }
}