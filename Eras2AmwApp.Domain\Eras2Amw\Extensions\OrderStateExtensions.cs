﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="OrderStateExtension.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Extensions
{
    using Eras2AmwApp.Domain.Eras2Amw.Enums;

    using Models;

    public static class OrderStateExtensions
    {
        public static bool IsCompleted(this OrderState orderState) => orderState.ProcessState.IsCompleted();

        public static bool InProgress(this OrderState orderState) => orderState.ProcessState.InProgress();

        public static bool IsUpdating(this OrderState orderState) => orderState.ProcessState.IsUpdating();
    }
}