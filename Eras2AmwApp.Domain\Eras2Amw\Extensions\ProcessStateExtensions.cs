﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="ProgressStateExtensions.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Extensions
{
    using Enums;

    public static class ProcessStateExtensions 
    {
        public static bool IsUpdating(this ProcessState processState) => processState == ProcessState.Updating;

        public static bool IsCompleted(this ProcessState processState) => processState == ProcessState.Completed;

        public static bool InProgress(this ProcessState processState) => processState == ProcessState.InProgress;

        public static bool IsCreating(this ProcessState processState) => processState == ProcessState.Creating;
    }
}