﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Abrechnungseinheit.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;

    public class Abrechnungseinheit : StampEntity
    {
        public Guid Guid { get; set; }

        public DateTime? LastModified { get; set; }

        public string Number { get; set; }

        public string Note { get; set; }

        public string NoteInternal { get; set; }

        public string NoteLegionella { get; set; }

        public Guid CustomerGuid { get; set; }

        public virtual Customer Customer { get; set; }

        public Guid AddressGuid { get; set; }

        public virtual Address Address { get; set; }

        public virtual IList<Nutzeinheit> Nutzeinheiten { get; set; }

        public virtual IList<Order> Orders { get; set; }

        public virtual IList<PersonAbrechnungseinheit> PersonAbrechnungseinheiten { get; set; }

        public DateTime? BilligPeriodEnd { get; set; }
    }
}