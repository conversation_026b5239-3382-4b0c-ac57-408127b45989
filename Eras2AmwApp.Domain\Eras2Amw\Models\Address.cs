﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Address.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;

    public class Address
    {
        public Guid Guid { get; set; }

        public string Street { get; set; }

        public string Street2 { get; set; }

        public string StreetNumber { get; set; }

        public string Zipcode { get; set; }

        public string City { get; set; }

        public string Mailbox { get; set; }

        public string Additional { get; set; }

        public string Latitude { get; set; }

        public string Longitude { get; set; }

        public virtual IList<Abrechnungseinheit> Abrechnungseinheiten { get; set; }

        public virtual IList<Nutzeinheit> Nutzeinheiten { get; set; }
    }
}