﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AmwInfoKey.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;

    public class AmwInfoKey
    {
        public Guid Guid { get; set; }

        public int Key { get; set; }

        public string Info { get; set; }

        public virtual IList<NutzeinheitOrderState> NutzeinheitOrderStates { get; set; }

        public virtual IList<DeviceOrderState> DeviceOrderStates { get; set; }
    }
}