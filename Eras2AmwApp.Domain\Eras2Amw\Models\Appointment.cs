﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Appointment.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;

    public class Appointment
    {
        public Guid Guid { get; set; }

        public DateTime From { get; set; }

        public DateTime To { get; set; }

        public Guid OrderGuid { get; set; }

        public virtual Order Order { get; set; }

        public virtual IList<AppointmentTechnician> AppointmentStoreUsers { get; set; }

        public virtual IList<AppointmentNutzeinheit> AppointmentNutzeinheiten { get; set; }
    }
}