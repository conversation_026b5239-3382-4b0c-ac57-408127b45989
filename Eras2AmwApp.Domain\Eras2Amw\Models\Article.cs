﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Article.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;

    public class Article
    {
        public Guid Guid { get; set; }

        public string Number { get; set; }

        public string Label { get; set; }

        public string Note { get; set; }

        public virtual IList<OrderPosition> OrderPositions { get; set; }
    }
}