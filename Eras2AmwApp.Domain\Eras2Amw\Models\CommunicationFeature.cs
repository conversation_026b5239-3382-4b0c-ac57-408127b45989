﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="CommunicationCapacity.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;
    using Enums;

    public class CommunicationFeature
    {
        public Guid Guid { get; set; }

        public string Feature { get; set; }

        public string Note { get; set; }

        public CommunicationKind Kind { get; set; }

        public bool IsPreset { get; set; }

        public virtual IList<PersonCommunication> PersonCommunications { get; set; }

        public virtual IList<NutzerCommunication> NutzerCommunications { get; set; }
    }
}