﻿namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;

    public class Customer
    {
        public Guid Guid { get; set; }

        public string Name { get; set; }

        public string Name2 { get; set; }

        public string Name3 { get; set; }

        public string Number { get; set; }

        public string Note { get; set; }

        public int SalutationId { get; set; }

        public virtual Salutation Salutation { get; set; }

        public virtual IList<Abrechnungseinheit> Abrechnungseinheiten { get; set; }
    }
}
