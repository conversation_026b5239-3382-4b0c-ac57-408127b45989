﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Devices.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;
    using Enums;

    public class Device
    {
        public Guid Guid { get; set; }

        public DateTime? LastModified { get; set; }

        public string Number { get; set; }

        public string Label { get; set; }

        public string Description { get; set; }

        public DateTime InstallationDate { get; set; }

        public DateTime? DeinstallationDate { get; set; }

        public DateTime? CalibrationDate { get; set; }

        public UnitKind? Unit { get; set; }

        public string Note { get; set; }

        public bool Subtraction { get; set; }

        public string GroupIdentifier { get; set; }

        public EstimationKind Estimation { get; set; }

        public DateTime? FirstActivation { get; set; }

        public string FirmwareVersion { get; set; }

        public Guid NutzeinheitGuid { get; set; }

        public virtual Nutzeinheit Nutzeinheit { get; set; }

        public Guid RoomGuid { get; set; }

        public virtual Room Room { get; set; }

        public string OngoingNumber { get; set; }

        public bool IsLeased { get; set; }

        public bool IsMaintained { get; set; }

        public bool IsCreatedByApp { get; set; }
        
        public Guid DeviceCatalogGuid { get; set; }

        public virtual DeviceCatalog DeviceCatalog { get; set; }

        public virtual IList<DeviceConsumption> DeviceConsumptions { get; set; }

        public virtual IList<Photo> Photos { get; set; }

        public virtual IList<DeviceOrderState> OrderStates { get; set; }

        public virtual IList<DeviceAdditionalArticle> DeviceAdditionalArticles { get; set; }
    }
}