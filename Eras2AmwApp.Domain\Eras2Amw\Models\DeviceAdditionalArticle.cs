﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceAdditionalArticle.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;

    public class DeviceAdditionalArticle
    {
        public Guid DeviceGuid { get; set; }

        public virtual Device Device { get; set; }

        public virtual AdditionalArticle AdditionalArticle { get; set; }

        public Guid AdditionalArticleGuid { get; set; }

        public DateTime LastModified { get; set; }

        public decimal Quantity { get; set; }

        public bool IsCreatedByApp { get; set; }
    }
}