﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceCatalog.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;

    public class DeviceCatalog
    {
        public Guid Guid { get; set; }

        public string ArticleName { get; set; }
        
        public string ArticleNumber { get; set; }
        
        public string ArticleDescription { get; set; }

        public string ArticleNumberManufacturer { get; set; }

        public string Label { get; set; }

        public bool Subtraction { get; set; }

        public bool Hkvv { get; set; }

        public bool Radio { get; set; }

        public string OrderCode { get; set; }

        public string InstallationLength { get; set; }

        public string Connector { get; set; }

        public string Diameter { get; set; }

        public string SontexRadioIdentifier { get; set; }

        public string SontexHkvIdentifier { get; set; }

        public string Note { get; set; }

        public Guid DeviceKindGuid { get; set; }

        public virtual DeviceKind DeviceKind { get; set; }

        public Guid ManufacturerGuid { get; set; }

        public virtual Manufacturer Manufacturer { get; set; }

        public virtual IList<Device> Devices { get; set; }
    }
}