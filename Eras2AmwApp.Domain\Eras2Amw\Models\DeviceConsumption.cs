﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceConsumption.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using Enums;

    public class DeviceConsumption
    {
        public Guid Guid { get; set; }

        public DateTime? LastModified { get; set; }

        public DateTime? ReadingDate { get; set; }

        public double Factor { get; set; }

        public double? Reading { get; set; }

        public double? Consumption { get; set; }
        
        public DateTime? ChangeDate { get; set; }

        public EstimationKind Estimation { get; set; }

        public DeviceConsumptionState State { get; set; }

        public string Note { get; set; }

        public bool IsReconstructed { get; set; }

        public double? Manual { get; set; }

        public DeviceConsumptionOrigin Origin { get; set; }

        public decimal? AirTemperature { get; set; }

        public decimal? RadiatorTemperature { get; set; }

        public DateTime? LastErrorDate { get; set; }

        public string Error { get; set; }

        public string AmpouleColour { get; set; }

        public Guid DeviceGuid { get; set; }

        public virtual Device Device { get; set; }

        public Guid ReadindKindGuid { get; set; }

        public virtual ReadingKind ReadingKind { get; set; }
    }
}