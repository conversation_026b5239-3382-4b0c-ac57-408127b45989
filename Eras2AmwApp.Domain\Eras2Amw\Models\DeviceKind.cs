﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceKind.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;

    using Eras2AmwApp.Domain.Eras2Amw.Enums;

    public class DeviceKind
    {
        public Guid Guid { get; set; }

        public string LabelLong { get; set; }
        
        public string LabelShort { get; set; }

        public bool Readable { get; set; }

        public DeviceClass Class { get; set; }
        
        public virtual IList<DeviceCatalog> DeviceCatalogs { get; set; }
    }
}