﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceOrderState.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using Enums;

    public class DeviceOrderState
    {
        public DateTime LastModified { get; set; }

        public DeviceOrderKind OrderKind { get; set; }

        public ProcessState ProcessState { get; set; }

        public DateTime? CompletedDate { get; set; }

        public Guid DeviceGuid { get; set; }

        public virtual Device Device { get; set; }     
        
        public Guid OrderGuid { get; set; }

        public virtual Order Order { get; set; } 

        public Guid? AmwInfoKeyGuid { get; set; }

        public virtual AmwInfoKey AmwInfoKey { get; set; }
    }
}