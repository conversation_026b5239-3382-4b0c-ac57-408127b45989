﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Nutzeinheit.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;

    public class Nutzeinheit : StampEntity
    {
        public Guid Guid { get; set; }

        public DateTime? LastModified { get; set; }

        public string Number { get; set; }

        public string Location { get; set; }

        public string WalkSequence { get; set; }

        public string Note { get; set; }

        public bool IsCreatedByApp { get; set; }
        
        public Guid AbrechnungseinheitGuid { get; set; }

        public virtual Abrechnungseinheit Abrechnungseinheit { get; set; }

        public Guid AddressGuid { get; set; }

        public virtual Address Address { get; set; }

        public virtual IList<Nutzer> Nutzer { get; set; }

        public virtual IList<NutzeinheitOrderPosition> NutzeinheitOrderPositions { get; set; }

        public virtual IList<Device> Devices { get; set; }

        public virtual IList<Photo> Photos { get; set; }

        public virtual IList<Signature> Signatures { get; set; }

        public virtual IList<NutzeinheitOrderState> OrderStates { get; set; }

        public virtual IList<AppointmentNutzeinheit> AppointmentNutzeinheiten { get; set; }
    }
}