﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitOrderState.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;
    using Enums;

    public class NutzeinheitOrderState
    {
        public DateTime LastModified { get; set; }

        public IList<NutzeinheitOrderKind> OrderKinds { get; set; }

        public ProcessState ProcessState { get; set; }

        public DateTime? CompletedDate { get; set; }

        public string OsVersion { get; set; }

        public string AppVersion { get; set; }

        public string OsPlatform { get; set; }

        public string AppDeviceNumber { get; set; }

        public Guid NutzeinheitGuid { get; set; }

        public virtual Nutzeinheit Nutzeinheit { get; set; }

        public Guid OrderGuid { get; set; }

        public virtual Order Order { get; set; }

        public Guid? AmwInfoKeyGuid { get; set; }

        public virtual AmwInfoKey AmwInfoKey { get; set; }
    }
}