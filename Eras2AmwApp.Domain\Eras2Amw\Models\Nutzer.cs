﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Nutzer.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;
    using Enums;

    public class Nutzer
    {
        public Guid Guid { get; set; }

        public DateTime? LastModified { get; set; }

        public NutzerKind Kind { get; set; }

        public DateTime MoveInDate { get; set; }

        public DateTime? MoveOutDate { get; set; }

        public string Name1 { get; set; }

        public string Name2 { get; set; }

        public string Name3 { get; set; }

        public string Name4 { get; set; }

        public string Note { get; set; }

        public DateTime? NextAppointmentDate { get; set; }

        public bool IsCreatedByApp { get; set; }

        public Guid NutzeinheitGuid { get; set; }

        public virtual Nutzeinheit Nutzeinheit { get; set; }

        public int? TitleId { get; set; }

        public virtual Title Title { get; set; }

        public int SalutationId { get; set; }

        public virtual Salutation Salutation { get; set; }

        public virtual IList<NutzerCommunication> NutzerCommunications { get; set; }

        public virtual IList<NutzerPersonen> NutzerPersonen { get; set; }

        public virtual IList<NutzerCoOwnership> NutzerCoOwnership { get; set; }

        public virtual IList<NutzerQuadratmeter> NutzerQuadratmeter { get; set; }
    }
}