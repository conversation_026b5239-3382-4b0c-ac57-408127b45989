﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzerCommunication.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;

    public class NutzerCommunication
    {
        public Guid Guid { get; set; }

        public DateTime? LastModified { get; set; }

        public string Address { get; set; }

        public string Note { get; set; }

        public Guid NutzerGuid { get; set; }

        public virtual Nutzer Nutzer { get; set; }

        public Guid CommunicationFeatureGuid { get; set; }

        public virtual CommunicationFeature CommunicationFeature { get; set; }
    }
}