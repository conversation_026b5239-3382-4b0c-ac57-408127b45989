﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzerQuadratmeter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------
namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;

    public class NutzerQuadratmeter
    {
        public Guid Guid { get; set; }

        public DateTime LastModified { get; set; }

        public decimal? SquareMeters_Hzg { get; set; }

        public decimal? SquareMeters_Ww { get; set; }

        public decimal? SquareMeters_Nk { get; set; }

        public DateTime RangeFrom { get; set; }

        public DateTime? RangeTo { get; set; }

        public Guid NutzerGuid { get; set; }

        public virtual Nutzer Nutzer { get; set; }

        public bool IsCreatedByApp { get; set; }
    }
}
