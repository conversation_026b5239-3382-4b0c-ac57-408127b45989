﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Order.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;

    public class Order : StampEntity
    {
        public Guid Guid { get; set; }

        public DateTime LastModified { get; set; }

        public string Number { get; set; }

        public string Label { get; set; }

        public string Note { get; set; }

        public bool IsSignatureRequired { get; set; }

        public string CreatedDate { get; set; }

        public Guid AbrechnungseinheitGuid { get; set; }

        public virtual Abrechnungseinheit Abrechnungseinheit { get; set; }

        public virtual IList<Appointment> Appointments { get; set; }

        public virtual IList<OrderPosition> OrderPositions { get; set; }

        public virtual OrderState OrderState { get; set; }

        public virtual IList<DeviceOrderState> DeviceOrderStates { get; set; }

        public virtual IList<NutzeinheitOrderState> NutzeinheitOrderStates { get; set; }

        public Guid OrdererGuid { get; set; }

        public virtual Orderer Orderer { get; set; }

        public virtual IList<Signature> Signatures { get; set; }
    }
}