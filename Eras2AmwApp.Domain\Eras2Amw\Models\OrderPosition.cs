﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="OrderPosition.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;

    public class OrderPosition
    {
        public Guid Guid { get; set; }

        public DateTime LastModified { get; set; }

        public string Note { get; set; }

        public int RequiredQuantity { get; set; }

        public int ActualQuantity { get; set; }

        public Guid OrderGuid { get; set; }

        public virtual Order Order { get; set; }

        public Guid ArticleGuid { get; set; }

        public virtual Article Article { get; set; }

        public virtual IList<NutzeinheitOrderPosition> NutzeinheitOrderPositions { get; set; }
    }
}