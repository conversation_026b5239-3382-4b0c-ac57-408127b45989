﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="OrderState.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;
    using Enums;

    public class OrderState
    {
        public DateTime LastModified { get; set; }

        public Guid OrderGuid { get; set; }

        public virtual Order Order { get; set; }        

        public IList<OrderKind> Kinds { get; set; }

        public ProcessState ProcessState { get; set; }

        public DateTime? CompletedDate { get; set; }
    }
}