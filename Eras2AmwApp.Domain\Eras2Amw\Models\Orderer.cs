﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Orderer.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;

    public class Orderer
    {
        public Guid Guid { get; set; }

        public Guid PersonGuid { get; set; }

        public string BusinessPosition { get; set; }

        public virtual Person Person { get; set; }

        public virtual IList<Order> Orders { get; set; }
    }
}