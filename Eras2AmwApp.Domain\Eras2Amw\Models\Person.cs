﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Person.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;

    public class Person
    {
        public Guid Guid { get; set; }

        public DateTime? LastModified { get; set; }

        public string Lastname1 { get; set; }

        public string Lastname2 { get; set; }

        public string Lastname3 { get; set; }

        public string Firstname1 { get; set; }

        public string Firstname2 { get; set; }

        public string Note { get; set; }

        public int SalutationId { get; set; }

        public virtual Salutation Salutation { get; set; }

        public int? TitleId { get; set; }

        public virtual Title Title { get; set; }

        public virtual IList<PersonCommunication> PersonCommunications { get; set; }

        public virtual IList<PersonAbrechnungseinheit> PersonAbrechnungseinheiten { get; set; }

        public virtual IList<Orderer> Orderers { get; set; }
    }
}