﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="PersonAbrechnungseinheit.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;

    public class PersonAbrechnungseinheit
    {
        public Guid Guid { get; set; }

        public DateTime? LastModified { get; set; }

        public string Note { get; set; }

        public string BusinessPosition { get; set; }

        public bool IsCreatedByApp { get; set; }

        public Guid AbrechnungseinheitGuid { get; set; }

        public virtual Abrechnungseinheit Abrechnungseinheit { get; set; }

        public Guid PersonGuid { get; set; }

        public virtual Person Person { get; set; }
   }
}