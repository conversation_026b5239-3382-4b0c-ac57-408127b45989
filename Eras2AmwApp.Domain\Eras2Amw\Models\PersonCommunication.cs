﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="PersonCommunication.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;

    public class PersonCommunication
    {
        public Guid Guid { get; set; }

        public DateTime? LastModified { get; set; }

        public string Address { get; set; }

        public string Note { get; set; }

        public Guid PersonGuid { get; set; }

        public virtual Person Person { get; set; }

        public Guid CommunicationFeatureGuid { get; set; }

        public virtual CommunicationFeature CommunicationFeature { get; set; }
    }
}