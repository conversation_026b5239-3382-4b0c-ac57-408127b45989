﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Foto.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;

    public class Photo
    {
        public Guid Guid { get; set; }

        public string Name { get; set; }

        public string Path { get; set; }

        public DateTime RecordedDate { get; set; }

        public bool CreatedByApp { get; set; }

        public Guid? NutzeinheitGuid { get; set; }

        public virtual Nutzeinheit Nutzeinheit { get; set; }

        public Guid? DeviceGuid { get; set; }

        public virtual Device Device { get; set; }
    }
}