﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="ReadindKind.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;

    public class ReadingKind
    {
        public Guid Guid { get; set; }

        public string LabelShort { get; set; }

        public string LabelLong { get; set; }

        public virtual IList<DeviceConsumption> DeviceConsumptions { get; set; }
    }
}