﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Room.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using System.Collections.Generic;

    public class Room
    {
        public Guid Guid { get; set; }

        public string LabelShort { get; set; }

        public string Label { get; set; }

        public virtual IList<Device> Devices { get; set; }
    }
}