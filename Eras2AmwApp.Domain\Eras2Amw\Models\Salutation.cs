﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Salutation.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System.Collections.Generic;

    public class Salutation
    {
        public int Id { get; set; }

        public string Label { get; set; }

        public virtual IList<Customer> Customers { get; set; }

        public virtual IList<Nutzer> Nutzer { get; set; }

        public virtual IList<Person> Persons { get; set; }
    }
}