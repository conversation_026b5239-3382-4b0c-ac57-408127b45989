﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Signature.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System;
    using Enums;

    public class Signature
    {
        public Guid Guid { get; set; }

        public string Path { get; set; }

        public SignatureKind Kind { get; set; }

        public bool CreatedByApp { get; set; }

        public DateTime RecordedDate { get; set; }

        public Guid NutzeinheitGuid { get; set; }

        public virtual Nutzeinheit Nutzeinheit { get; set; }

        public Guid OrderGuid { get; set; }

        public virtual Order Order { get; set; }
    }
}