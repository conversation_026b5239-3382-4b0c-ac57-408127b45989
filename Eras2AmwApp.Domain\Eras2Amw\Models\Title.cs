﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="Title.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2Amw.Models
{
    using System.Collections.Generic;

    public class Title
    {
        public int Id { get; set; }

        public string Label { get; set; }

        public string Note { get; set; }

        public virtual IList<Nutzer> <PERSON>utzer { get; set; }

        public virtual IList<Person> Persons { get; set; }
    }
}