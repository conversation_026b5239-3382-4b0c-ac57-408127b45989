﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="User.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.Domain.Eras2App.Database
{
    using System;

    public class User
    {
        public Guid Guid { get; set; }

        public string Name { get; set; }

        public string Password { get; set; }

        public string Email { get; set; }

        public DateTime LastWebserviceLoginDate { get; set; }

        public DateTime? LastWebserviceSyncDate { get; set; }

        public int CustomerId { get; set; }

        public virtual Customer Customer { get; set; }

        public bool IsLiveSyncEnabled { get; set; }
    }
}