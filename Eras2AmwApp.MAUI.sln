Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35825.156
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Eras2AmwApp.MAUI", "Eras2AmwApp.MAUI\Eras2AmwApp.MAUI.csproj", "{528B4A72-43E0-4183-96C7-7AB508321C25}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Endiancode.Utilities", "Endiancode.Utilities\Endiancode.Utilities.csproj", "{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Eras2AmwApp.BusinessLogic", "Eras2AmwApp.Bl\Eras2AmwApp.BusinessLogic.csproj", "{93BEF591-F821-8695-9451-CE2A8F770F32}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Eras2AmwApp.Common", "Eras2AmwApp.Common\Eras2AmwApp.Common.csproj", "{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Eras2AmwApp.Domain", "Eras2AmwApp.Domain\Eras2AmwApp.Domain.csproj", "{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Eras2AmwApp.WebService", "Eras2AmwApp.WebService\Eras2AmwApp.WebService.csproj", "{B3268ED8-FCD6-98F1-7984-C87B191D751B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Eras2AmwApp.Database", "Eras2AmwApp.Database\Eras2AmwApp.Database.csproj", "{7268034D-5615-25E9-EC41-6388B2BB4DEE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Eras2AmwApp.Database.Migration", "Eras2AmwApp.Database.Migration\Eras2AmwApp.Database.Migration.csproj", "{A5D8C3E7-F9B2-4E1A-B9D5-8F2C05F3A123}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{8EC462FD-D22E-90A8-E5CE-7E832BA40C5D}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Development|Any CPU = Development|Any CPU
		Release|Any CPU = Release|Any CPU
		StandaloneDevelopment|Any CPU = StandaloneDevelopment|Any CPU
		StandaloneRelease|Any CPU = StandaloneRelease|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{528B4A72-43E0-4183-96C7-7AB508321C25}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{528B4A72-43E0-4183-96C7-7AB508321C25}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{528B4A72-43E0-4183-96C7-7AB508321C25}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{528B4A72-43E0-4183-96C7-7AB508321C25}.Development|Any CPU.ActiveCfg = Debug|Any CPU
		{528B4A72-43E0-4183-96C7-7AB508321C25}.Development|Any CPU.Build.0 = Debug|Any CPU
		{528B4A72-43E0-4183-96C7-7AB508321C25}.Development|Any CPU.Deploy.0 = Debug|Any CPU
		{528B4A72-43E0-4183-96C7-7AB508321C25}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{528B4A72-43E0-4183-96C7-7AB508321C25}.Release|Any CPU.Build.0 = Release|Any CPU
		{528B4A72-43E0-4183-96C7-7AB508321C25}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{528B4A72-43E0-4183-96C7-7AB508321C25}.StandaloneDevelopment|Any CPU.ActiveCfg = Debug|Any CPU
		{528B4A72-43E0-4183-96C7-7AB508321C25}.StandaloneDevelopment|Any CPU.Build.0 = Debug|Any CPU
		{528B4A72-43E0-4183-96C7-7AB508321C25}.StandaloneDevelopment|Any CPU.Deploy.0 = Debug|Any CPU
		{528B4A72-43E0-4183-96C7-7AB508321C25}.StandaloneRelease|Any CPU.ActiveCfg = Release|Any CPU
		{528B4A72-43E0-4183-96C7-7AB508321C25}.StandaloneRelease|Any CPU.Build.0 = Release|Any CPU
		{528B4A72-43E0-4183-96C7-7AB508321C25}.StandaloneRelease|Any CPU.Deploy.0 = Release|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.Development|Any CPU.ActiveCfg = Debug|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.Development|Any CPU.Build.0 = Debug|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.Release|Any CPU.Build.0 = Release|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.StandaloneDevelopment|Any CPU.ActiveCfg = Debug|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.StandaloneDevelopment|Any CPU.Build.0 = Debug|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.StandaloneRelease|Any CPU.ActiveCfg = Release|Any CPU
		{104FE378-CE02-DA5A-FAB9-A7F8DA09215E}.StandaloneRelease|Any CPU.Build.0 = Release|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.Development|Any CPU.Build.0 = Development|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.Release|Any CPU.Build.0 = Release|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.StandaloneDevelopment|Any CPU.ActiveCfg = StandaloneDevelopment|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.StandaloneDevelopment|Any CPU.Build.0 = StandaloneDevelopment|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.StandaloneRelease|Any CPU.ActiveCfg = StandaloneRelease|Any CPU
		{93BEF591-F821-8695-9451-CE2A8F770F32}.StandaloneRelease|Any CPU.Build.0 = StandaloneRelease|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.Development|Any CPU.Build.0 = Development|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.Release|Any CPU.Build.0 = Release|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.StandaloneDevelopment|Any CPU.ActiveCfg = StandaloneDevelopment|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.StandaloneDevelopment|Any CPU.Build.0 = StandaloneDevelopment|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.StandaloneRelease|Any CPU.ActiveCfg = StandaloneRelease|Any CPU
		{BAD6E1AA-1F82-35EA-2976-56E6FDACAB92}.StandaloneRelease|Any CPU.Build.0 = StandaloneRelease|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.Development|Any CPU.Build.0 = Development|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.Release|Any CPU.Build.0 = Release|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.StandaloneDevelopment|Any CPU.ActiveCfg = StandaloneDevelopment|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.StandaloneDevelopment|Any CPU.Build.0 = StandaloneDevelopment|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.StandaloneRelease|Any CPU.ActiveCfg = StandaloneRelease|Any CPU
		{6F5D6CC3-BE3A-F9EC-9839-A9ACBD35A43A}.StandaloneRelease|Any CPU.Build.0 = StandaloneRelease|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.Development|Any CPU.Build.0 = Development|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.Release|Any CPU.Build.0 = Release|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.StandaloneDevelopment|Any CPU.ActiveCfg = StandaloneDevelopment|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.StandaloneDevelopment|Any CPU.Build.0 = StandaloneDevelopment|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.StandaloneRelease|Any CPU.ActiveCfg = StandaloneRelease|Any CPU
		{B3268ED8-FCD6-98F1-7984-C87B191D751B}.StandaloneRelease|Any CPU.Build.0 = StandaloneRelease|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.Development|Any CPU.Build.0 = Development|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.Release|Any CPU.Build.0 = Release|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.StandaloneDevelopment|Any CPU.ActiveCfg = StandaloneDevelopment|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.StandaloneDevelopment|Any CPU.Build.0 = StandaloneDevelopment|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.StandaloneRelease|Any CPU.ActiveCfg = StandaloneRelease|Any CPU
		{7268034D-5615-25E9-EC41-6388B2BB4DEE}.StandaloneRelease|Any CPU.Build.0 = StandaloneRelease|Any CPU
		{A5D8C3E7-F9B2-4E1A-B9D5-8F2C05F3A123}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A5D8C3E7-F9B2-4E1A-B9D5-8F2C05F3A123}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A5D8C3E7-F9B2-4E1A-B9D5-8F2C05F3A123}.Development|Any CPU.ActiveCfg = Development|Any CPU
		{A5D8C3E7-F9B2-4E1A-B9D5-8F2C05F3A123}.Development|Any CPU.Build.0 = Development|Any CPU
		{A5D8C3E7-F9B2-4E1A-B9D5-8F2C05F3A123}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A5D8C3E7-F9B2-4E1A-B9D5-8F2C05F3A123}.Release|Any CPU.Build.0 = Release|Any CPU
		{A5D8C3E7-F9B2-4E1A-B9D5-8F2C05F3A123}.StandaloneDevelopment|Any CPU.ActiveCfg = StandaloneDevelopment|Any CPU
		{A5D8C3E7-F9B2-4E1A-B9D5-8F2C05F3A123}.StandaloneDevelopment|Any CPU.Build.0 = StandaloneDevelopment|Any CPU
		{A5D8C3E7-F9B2-4E1A-B9D5-8F2C05F3A123}.StandaloneRelease|Any CPU.ActiveCfg = StandaloneRelease|Any CPU
		{A5D8C3E7-F9B2-4E1A-B9D5-8F2C05F3A123}.StandaloneRelease|Any CPU.Build.0 = StandaloneRelease|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		StartupProject = {528B4A72-43E0-4183-96C7-7AB508321C25}
		SolutionGuid = {5C9BF02A-6CD4-428C-BE56-A2B08A414FB1}
	EndGlobalSection
EndGlobal
