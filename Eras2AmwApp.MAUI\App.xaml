﻿<?xml version = "1.0" encoding = "UTF-8" ?>
<Application xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:Eras2AmwApp.MAUI"
             xmlns:converters="clr-namespace:Eras2AmwApp.MAUI.Converters"
             x:Class="Eras2AmwApp.MAUI.App">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/Styles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Converters -->
            <converters:StringToBoolConverter x:Key="StringToBoolConverter" />
            <converters:OrderStateConverter x:Key="OrderStateConverter" />
            <converters:ShortTimeConverter x:Key="ShortTimeConverter" />
            <converters:ShortDateConverter x:Key="ShortDateConverter" />
            <converters:DataGridTimeFormatConverter x:Key="DataGridTimeFormatConverter" />
            <converters:DeviceNutzeinheitOrderConverter x:Key="DeviceNutzeinheitOrderConverter" />
            <converters:NutzeinheitStatusConverter x:Key="NutzeinheitStatusConverter" />
            <converters:NutzeinheitOrderStateConverter x:Key="NutzeinheitOrderStateConverter" />
            <converters:NutzerKindConverter x:Key="NutzerKindConverter" />
            <converters:NegateBooleanConverter x:Key="NegateBooleanConverter" />
        </ResourceDictionary>
    </Application.Resources>
</Application>
