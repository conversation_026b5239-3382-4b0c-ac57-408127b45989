﻿using Eras2AmwApp.MAUI.Bootstrap;
using Eras2AmwApp.MAUI.Views;
using Microsoft.Maui.ApplicationModel;

namespace Eras2AmwApp.MAUI
{
    public partial class App : Application
    {
        private readonly Bootstrapper? bootstrapper;

        public App(Bootstrapper? bootstrapper = null)
        {
            this.bootstrapper = bootstrapper;

            try
            {
                InitializeComponent();

                // Set the main page to a simple page first to ensure we can display something
                MainPage = new ContentPage
                {
                    Content = new VerticalStackLayout
                    {
                        Children =
                        {
                            new Label
                            {
                                Text = "Loading...",
                                FontSize = 24,
                                HorizontalOptions = LayoutOptions.Center,
                                VerticalOptions = LayoutOptions.Center
                            }
                        },
                        VerticalOptions = LayoutOptions.Center
                    }
                };

                // Now try to set the real main page
                try
                {
                    // Set the main page to AppShell
                    MainPage = new AppShell();
                }
                catch
                {
                    // Keep the loading page
                }
            }
            catch (Exception ex)
            {
                // Create a simple error page
                MainPage = new ContentPage
                {
                    Content = new VerticalStackLayout
                    {
                        Children =
                        {
                            new Label
                            {
                                Text = "Error initializing application",
                                FontSize = 24,
                                HorizontalOptions = LayoutOptions.Center,
                                Margin = new Thickness(0, 50, 0, 0)
                            },
                            new Label
                            {
                                Text = ex.Message,
                                FontSize = 16,
                                HorizontalOptions = LayoutOptions.Center,
                                Margin = new Thickness(20, 20, 20, 0)
                            }
                        },
                        VerticalOptions = LayoutOptions.Center
                    }
                };
            }
        }

        protected override async void OnStart()
        {
            // Handle when your app starts
            base.OnStart();

            try
            {
                // Check if bootstrapper is available
                if (bootstrapper != null)
                {
                    // Initialize the application using the bootstrapper
                    await bootstrapper.InitializeAsync();
                }
            }
            catch (Exception ex)
            {
                // Show error dialog if MainPage is available
                if (MainPage != null)
                {
                    try
                    {
                        await MainPage.DisplayAlert("Initialization Error",
                            $"An error occurred during application initialization: {ex.Message}", "OK");
                    }
                    catch
                    {
                        // Failed to show error dialog
                    }
                }
            }

            // Request camera permission for QR code scanning
            await RequestCameraPermissionAsync();
        }

        private static async Task RequestCameraPermissionAsync()
        {
            var status = await Permissions.CheckStatusAsync<Permissions.Camera>();
            if (status != PermissionStatus.Granted)
            {
                await Permissions.RequestAsync<Permissions.Camera>();
            }
        }

        protected override void OnSleep()
        {
            // Handle when your app sleeps
            base.OnSleep();
        }

        protected override void OnResume()
        {
            // Handle when your app resumes
            base.OnResume();
        }
    }
}
