﻿using Eras2AmwApp.MAUI.Views;
using System.Diagnostics;

namespace Eras2AmwApp.MAUI
{
    public partial class AppShell : Shell
    {
        public AppShell()
        {
            try
            {
                InitializeComponent();

                // Register routes for navigation
                try
                {
                    // Register routes with explicit names for clarity
                   
                    Routing.RegisterRoute("//RegistrationPage", typeof(RegistrationPage));
                    Routing.RegisterRoute("//LoginPage", typeof(LoginPage));
                    Routing.RegisterRoute("//QrScannerView", typeof(QrScannerView));
                    Routing.RegisterRoute("//AppointmentPage", typeof(AppointmentPage));
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error registering routes: {ex.Message}");
                }

                // Add navigation event handler
                Navigated += OnNavigated;
            }
            catch
            {
                // Error in AppShell constructor
            }
        }

        private void OnNavigated(object? sender, ShellNavigatedEventArgs e)
        {
            // Navigation event handler
        }

        protected override void OnNavigating(ShellNavigatingEventArgs args)
        {
            base.OnNavigating(args);
            // Navigating event handler
        }
    }
}
