using System.Reflection;
using System.Windows.Input;

namespace Eras2AmwApp.MAUI.Behaviors
{
    public class EventToCommandBehavior : Behavior<VisualElement>
    {
        public static readonly BindableProperty EventNameProperty =
            BindableProperty.Create(nameof(EventName), typeof(string), typeof(EventToCommandBehavior), null);

        public static readonly BindableProperty CommandProperty =
            BindableProperty.Create(nameof(Command), typeof(ICommand), typeof(EventToCommandBehavior), null);

        public static readonly BindableProperty CommandParameterProperty =
            BindableProperty.Create(nameof(CommandParameter), typeof(object), typeof(EventToCommandBehavior), null);

        public string EventName
        {
            get => (string)GetValue(EventNameProperty);
            set => SetValue(EventNameProperty, value);
        }

        public ICommand Command
        {
            get => (ICommand)GetValue(CommandProperty);
            set => SetValue(CommandProperty, value);
        }

        public object CommandParameter
        {
            get => GetValue(CommandParameterProperty);
            set => SetValue(CommandParameterProperty, value);
        }

        protected override void OnAttachedTo(VisualElement bindable)
        {
            base.OnAttachedTo(bindable);

            if (string.IsNullOrEmpty(EventName))
            {
                return;
            }

            EventInfo eventInfo = bindable.GetType().GetRuntimeEvent(EventName)
                ?? throw new ArgumentException($"EventToCommandBehavior: Event '{EventName}' not found on '{bindable}'");

            MethodInfo methodInfo = GetType().GetTypeInfo().GetDeclaredMethod(nameof(OnEvent))
                ?? throw new ArgumentException("EventToCommandBehavior: OnEvent method not found");

            Delegate handler = Delegate.CreateDelegate(eventInfo.EventHandlerType, this, methodInfo);
            eventInfo.AddEventHandler(bindable, handler);
        }

        protected override void OnDetachingFrom(VisualElement bindable)
        {
            if (string.IsNullOrEmpty(EventName))
            {
                return;
            }

            EventInfo eventInfo = bindable.GetType().GetRuntimeEvent(EventName);
            if (eventInfo != null)
            {
                MethodInfo methodInfo = GetType().GetTypeInfo().GetDeclaredMethod(nameof(OnEvent));
                if (methodInfo != null)
                {
                    Delegate handler = Delegate.CreateDelegate(eventInfo.EventHandlerType, this, methodInfo);
                    eventInfo.RemoveEventHandler(bindable, handler);
                }
            }

            base.OnDetachingFrom(bindable);
        }

        private void OnEvent(object sender, object eventArgs)
        {
            if (Command?.CanExecute(CommandParameter) == true)
            {
                Command.Execute(CommandParameter);
            }
        }
    }
}
