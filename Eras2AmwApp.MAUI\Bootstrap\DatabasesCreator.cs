//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DatabaseCreator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.Bootstrap
{
    using System;
    using Database.Contexts;
    using Database.Interfaces;
    using Microsoft.EntityFrameworkCore;
    using Serilog;

    public class DatabasesCreator
    {
        private readonly ILogger logger;

        private readonly IDbContextFactory contextFactory;

        public DatabasesCreator(ILogger logger, IDbContextFactory contextFactory)
        {
            this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
        }

        public void Start()
        {
            try
            {
                Migrate();
            }
            catch (Exception e)
            {
                logger.Error(e, "Database Setup failed");
                throw;
            }
        }

        private void Migrate()
        {
            using var amwContext = contextFactory.CreateAmw();
            amwContext.Database.Migrate();

            using var appContext = contextFactory.CreateApp();
            appContext.Database.Migrate();
        }
    }
}
