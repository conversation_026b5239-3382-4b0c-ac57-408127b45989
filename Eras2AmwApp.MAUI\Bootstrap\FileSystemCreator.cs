//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="FileSystemCreator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.Bootstrap
{
    using System;
    using System.IO;
    using Eras2AmwApp.Common.Interfaces;
    using Serilog;
    
    public class FileSystemCreator
    {
        private readonly IAppSettings appSettings;
        private readonly ILogger logger;

        public FileSystemCreator(IAppSettings appSettings, ILogger logger)
        {
            this.appSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
            this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public void Start()
        {
            try
            {
                // Ensure all required directories exist
                EnsureDirectoryExists(appSettings.DatabaseDirectory);
                EnsureDirectoryExists(appSettings.LogFilesDirectory);
                EnsureDirectoryExists(appSettings.WebserviceDirectory);
                EnsureDirectoryExists(appSettings.WebserviceUploadDirectory);
                EnsureDirectoryExists(appSettings.WebserviceDownloadDirectory);
                EnsureDirectoryExists(appSettings.PicturesDirectory);
                EnsureDirectoryExists(appSettings.SignaturesDirectory);
                EnsureDirectoryExists(appSettings.BackupDirectory);
            }
            catch (Exception e)
            {
                logger.Error(e, "FileSystem Setup failed");
                throw;
            }
        }

        private void EnsureDirectoryExists(string path)
        {
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
        }
    }
}
