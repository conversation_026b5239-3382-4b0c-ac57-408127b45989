//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="UISetip.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.Bootstrap
{
    using System;
    using System.Linq;
    using System.Threading.Tasks;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.MAUI.Interfaces;
    using Eras2AmwApp.MAUI.ViewModels;
    using Serilog;

    public class UiSetup
    {
        private readonly IEcNavigationService navigationService;
        private readonly ILogger logger;
        private readonly IDbContextFactory factory;

        public UiSetup(IEcNavigationService navigationService, ILogger logger, IDbContextFactory factory)
        {
            this.navigationService = navigationService ?? throw new ArgumentNullException(nameof(navigationService));
            this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
            this.factory = factory ?? throw new ArgumentNullException(nameof(factory));
        }

        public async Task StartAsync()
        {
            try
            {
                using (Eras2AppContext context = factory.CreateApp())
                {
                    if (!context.Customers.Any())
                    {
                        await navigationService.InitializeAsync<RegistrationPageViewModel>();
                    }
                    else
                    {
                        await navigationService.InitializeAsync<LoginPageViewModel>();
                    }
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "UiSystem Setup failed");
                throw;
            }
        }
    }
}
