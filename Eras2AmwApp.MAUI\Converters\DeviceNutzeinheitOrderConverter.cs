using System.Globalization;
using Eras2AmwApp.Domain.Eras2Amw.Models;
using Eras2AmwApp.Domain.Eras2Amw.Extensions;

namespace Eras2AmwApp.MAUI.Converters
{
    public class DeviceNutzeinheitOrderConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is NutzeinheitOrderState nutzeinheitOrderState)
            {
                // Check the process state and return appropriate color
                if (nutzeinheitOrderState.ProcessState.IsCompleted())
                {
                    return Colors.Green;
                }
                else if (nutzeinheitOrderState.ProcessState.InProgress())
                {
                    return Colors.Orange;
                }
                else if (nutzeinheitOrderState.ProcessState.IsCreating() || nutzeinheitOrderState.ProcessState.IsUpdating())
                {
                    return Colors.Blue;
                }
                else
                {
                    return Colors.Gray;
                }
            }

            return Colors.Transparent;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
