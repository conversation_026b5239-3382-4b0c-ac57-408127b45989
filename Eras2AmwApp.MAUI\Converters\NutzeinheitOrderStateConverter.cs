using System.Globalization;
using Eras2AmwApp.MAUI.Models;
using Eras2AmwApp.Domain.Eras2Amw.Extensions;

namespace Eras2AmwApp.MAUI.Converters
{
    public class NutzeinheitOrderStateConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is NutzeinheitUiState nutzeinheitUiState)
            {
                // Check the process state and return appropriate color
                if (nutzeinheitUiState.NutzeinheitProcessState.IsCompleted())
                {
                    return Colors.Green;
                }
                else if (nutzeinheitUiState.NutzeinheitProcessState.InProgress())
                {
                    return Colors.Orange;
                }
                else if (nutzeinheitUiState.NutzeinheitProcessState.IsCreating() || nutzeinheitUiState.NutzeinheitProcessState.IsUpdating())
                {
                    return Colors.Blue;
                }
                else
                {
                    return Colors.Gray;
                }
            }

            return Colors.Transparent;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
