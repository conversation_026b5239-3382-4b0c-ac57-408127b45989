using System.Globalization;
using Eras2AmwApp.Domain.Eras2Amw.Enums;
using Eras2AmwApp.Domain.Eras2Amw.Extensions;

namespace Eras2AmwApp.MAUI.Converters
{
    public class NutzeinheitStatusConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is ProcessState processState)
            {
                // Check the process state and return appropriate image
                if (processState.IsCompleted())
                {
                    return "lockClosed.png";
                }
                else if (processState.InProgress() || processState.IsUpdating() || processState.IsCreating())
                {
                    return "lockOpen.png";
                }
                else
                {
                    return "lockOpen.png";
                }
            }

            return "lockOpen.png";
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
