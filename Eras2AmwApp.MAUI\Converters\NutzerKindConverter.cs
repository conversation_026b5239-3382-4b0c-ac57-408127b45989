using System.Globalization;
using Eras2AmwApp.Domain.Eras2Amw.Enums;

namespace Eras2AmwApp.MAUI.Converters
{
    public class NutzerKindConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is NutzerKind nutzerKind)
            {
                return nutzerKind switch
                {
                    NutzerKind.Leerstand => "Leerstand",
                    NutzerKind.Gewerblich => "Gewerblich",
                    NutzerKind.Privat => "Privat",
                    NutzerKind.AllgemeineNutzung => "AllgemeineNutzung",
                    _ => nutzerKind.ToString()
                };
            }

            return string.Empty;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                return stringValue switch
                {
                    "Leerstand" => NutzerKind.Leerstand,
                    "Gewerblich" => NutzerKind.<PERSON><PERSON>erblich,
                    "Privat" => NutzerKind.Privat,
                    "AllgemeineNutzung" => NutzerKind.AllgemeineNutzung,
                    _ => throw new ArgumentException($"Unknown NutzerKind: {stringValue}")
                };
            }

            throw new NotImplementedException();
        }
    }
}
