using System.Globalization;
using Eras2AmwApp.Domain.Eras2Amw.Models;
using Eras2AmwApp.Domain.Eras2Amw.Extensions;

namespace Eras2AmwApp.MAUI.Converters
{
    public class OrderStateConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is OrderState orderState)
            {
                // Check the process state and return appropriate color
                if (orderState.ProcessState.IsCompleted())
                {
                    return Colors.Green;
                }
                else if (orderState.ProcessState.InProgress())
                {
                    return Colors.Orange;
                }
                else if (orderState.ProcessState.IsCreating() || orderState.ProcessState.IsUpdating())
                {
                    return Colors.Blue;
                }
                else
                {
                    return Colors.Gray;
                }
            }

            return Colors.Transparent;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
