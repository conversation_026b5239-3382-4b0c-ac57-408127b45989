using System.Globalization;

namespace Eras2AmwApp.MAUI.Converters
{
    public class ShortDateConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is DateTime dateTime)
            {
                return dateTime.ToString("dd.MM.yyyy");
            }
            
            if (value is DateOnly dateOnly)
            {
                return dateOnly.ToString("dd.MM.yyyy");
            }

            return string.Empty;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is string stringValue && DateTime.TryParseExact(stringValue, "dd.MM.yyyy", culture, DateTimeStyles.None, out DateTime result))
            {
                return result;
            }

            throw new NotImplementedException();
        }
    }
}
