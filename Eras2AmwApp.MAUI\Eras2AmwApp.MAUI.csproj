<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net8.0-android34.0</TargetFrameworks>
		<!-- Only targeting Android for now to simplify development -->

		<OutputType>Exe</OutputType>
		<RootNamespace>Eras2AmwApp.MAUI</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<!-- Display name -->
		<ApplicationTitle>Eras2AmwApp.MAUI</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.companyname.eras2amwapp.maui</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">28.0</SupportedOSPlatformVersion>
	</PropertyGroup>

	<ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Images\eraslogo.png" Color="#FFFFFF" BaseSize="128,128" />

		<!-- Images -->
		<!-- Only include specific images that follow MAUI naming conventions -->
		<MauiImage Include="Resources\Images\eraslogo.png" />
		<MauiImage Include="Resources\Images\coldmeter.png" />
		<MauiImage Include="Resources\Images\firedetector.png" />
		<MauiImage Include="Resources\Images\hkve.png" />
		<MauiImage Include="Resources\Images\nelist.png" />
		<MauiImage Include="Resources\Images\szmeter.png" />
		<MauiImage Include="Resources\Images\warmmeter.png" />
		<MauiImage Include="Resources\Images\wmzmeter.png" />

		<!-- Include other images as content files (not processed by Resizetizer) -->
		<Content Include="Resources\Images\adaptiveIcon.png" />
		<Content Include="Resources\Images\adaptiveIconStandalone.png" />
		<Content Include="Resources\Images\appDevelopmentIcon.png" />
		<Content Include="Resources\Images\appReleaseIcon.png" />
		<Content Include="Resources\Images\appStandaloneDevelopmentIcon.png" />
		<Content Include="Resources\Images\appStandaloneReleaseIcon.png" />
		<Content Include="Resources\Images\barcodeIcon.png" />
		<Content Include="Resources\Images\changeIcon.png" />
		<Content Include="Resources\Images\confirmIcon.png" />
		<Content Include="Resources\Images\deleteIcon.png" />
		<Content Include="Resources\Images\downloadIcon.png" />
		<Content Include="Resources\Images\editIcon.png" />
		<Content Include="Resources\Images\forwardIcon.png" />
		<Content Include="Resources\Images\infoIcon.png" />
		<Content Include="Resources\Images\liveSyncOffIcon.png" />
		<Content Include="Resources\Images\liveSyncOnIcon.png" />
		<Content Include="Resources\Images\lockClosed.png" />
		<Content Include="Resources\Images\lockOpen.png" />
		<Content Include="Resources\Images\maintainIcon.png" />
		<Content Include="Resources\Images\minusIcon.png" />
		<Content Include="Resources\Images\moreIcon.png" />
		<Content Include="Resources\Images\newDateIcon.png" />
		<Content Include="Resources\Images\newIcon.png" />
		<Content Include="Resources\Images\overviewIcon.png" />
		<Content Include="Resources\Images\photoGalleryIcon.png" />
		<Content Include="Resources\Images\photoIcon.png" />
		<Content Include="Resources\Images\saveIcon.png" />
		<Content Include="Resources\Images\settingsIcon.png" />
		<Content Include="Resources\Images\signatureIcon.png" />
		<Content Include="Resources\Images\splashLogo.png" />
		<Content Include="Resources\Images\splashscreen.xml" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
		<PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
		<PackageReference Include="FluentValidation" Version="11.11.0" />
		<PackageReference Include="Microsoft.Maui.Controls" Version="$(MauiVersion)" />
		<PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="$(MauiVersion)" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.1" />
		<PackageReference Include="Ninject" Version="3.3.6" />
		<PackageReference Include="SQLitePCLRaw.bundle_e_sqlite3" Version="2.1.7" />
		<PackageReference Include="Syncfusion.Maui.Core" Version="29.2.7" />
		<PackageReference Include="Syncfusion.Maui.Popup" Version="29.2.7" />
		<PackageReference Include="Syncfusion.Maui.ProgressBar" Version="29.1.39" />
		<PackageReference Include="Syncfusion.Maui.Calendar" Version="29.1.39" />
		<PackageReference Include="Syncfusion.Maui.Scheduler" Version="29.1.39" />
		<PackageReference Include="Syncfusion.Maui.TabView" Version="29.1.39" />
		<PackageReference Include="Syncfusion.Maui.DataGrid" Version="29.1.39" />
		<PackageReference Include="Syncfusion.Maui.Buttons" Version="29.2.7" />
		<PackageReference Include="Syncfusion.Maui.TreeView" Version="29.1.39" />
		<PackageReference Include="ZXing.Net.MAUI" Version="0.4.0" />
		<PackageReference Include="ZXing.Net.Maui.Controls" Version="0.4.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Endiancode.Utilities\Endiancode.Utilities.csproj" />
		<ProjectReference Include="..\Eras2AmwApp.Bl\Eras2AmwApp.BusinessLogic.csproj" />
		<ProjectReference Include="..\Eras2AmwApp.Common\Eras2AmwApp.Common.csproj" />
		<ProjectReference Include="..\Eras2AmwApp.Domain\Eras2AmwApp.Domain.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Platforms\Android\Assets\" />
	</ItemGroup>

</Project>
