﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AppointmentNutzeinheitDetailsDialog.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.Implementations
{
    using Eras2AmwApp.MAUI.Interfaces;
    using Eras2AmwApp.MAUI.Models;
    using Eras2AmwApp.MAUI.Enums;
    using Syncfusion.Maui.Popup;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.Maui;
    using Microsoft.Maui.Controls;

    public class AppointmentNutzeinheitDetailsDialog : IAppointmentNutzeinheitDetailsDialog
    {
        #region fields

        private Command? acceptButtonCommand;
        private TaskCompletionSource<DialogResponse>? source;
        private SfPopup? sfPopup;
        private CollectionView? collectionView;

        #endregion

        #region Commands

        public Command AcceptButtonCommand => acceptButtonCommand ??= new Command(AcceptButtonExecuted);

        #endregion

        #region public methods

        public Task<DialogResponse> ShowAppointmentNutzeinheitInfo(Dictionary<string, string> dictOfAddressNutzeinheiten)
        {
            source = new TaskCompletionSource<DialogResponse>();

            sfPopup = CreateDialogWindow(dictOfAddressNutzeinheiten);
            sfPopup.Show();

            return source.Task;
        }

        #endregion

        #region private methods

        private SfPopup CreateDialogWindow(Dictionary<string, string> dictOfAddressNutzeinheiten)
        {
            Grid grid = CreateGrid();

            Label headerLabelOne = CreateHeaderFirst();
            Label headerLabelTwo = CreateHeaderTwo();

            grid.Add(headerLabelOne, 0, 0);
            grid.Add(headerLabelTwo, 1, 0);

            List<NutzeinheitInfo> listOfNutzeinheitInfo = [];

            for (int x = 1; x <= dictOfAddressNutzeinheiten.Count; x++)
            {
                NutzeinheitInfo nutzeinheitInfo = new()
                {
                    NutzeinheitAddress = dictOfAddressNutzeinheiten.Keys.ElementAt(x - 1),
                    NutzeinheitCount = dictOfAddressNutzeinheiten.Values.ElementAt(x - 1)
                };
                listOfNutzeinheitInfo.Add(nutzeinheitInfo);
            }

            collectionView = CreateCollectionView(listOfNutzeinheitInfo);

            grid.Add(collectionView, 0, 1);
            Grid.SetColumnSpan(collectionView, 2);

            DataTemplate headerTemplateView = new(CreateHeader);

            SfPopup popup = new()
            {
                HeaderTitle = "Nutzeinheiten Info",
                ContentTemplate = new DataTemplate(() => grid),
                HeightRequest = 400,
                WidthRequest = 350,
                ShowFooter = true,
                FooterTemplate = new(CreateFooter)
            };

            return popup;
        }

        private CollectionView CreateCollectionView(List<NutzeinheitInfo> listOfNutzeinheitInfo)
        {
            collectionView = new CollectionView
            {
                ItemsSource = listOfNutzeinheitInfo,
                ItemTemplate = new DataTemplate(CreateItemTemplate),
                SelectionMode = SelectionMode.None,
                HeightRequest = 300
            };

            return collectionView;
        }

        private static Grid CreateItemTemplate()
        {
            Grid grid = new()
            {
                RowSpacing = 1,
                ColumnSpacing = 5,
                Padding = new Thickness(5)
            };

            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(220) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(80) });

            Label addressLabel = new()
            {
                Margin = 5,
                BackgroundColor = Colors.White,
                HorizontalOptions = LayoutOptions.Start,
                VerticalOptions = LayoutOptions.Center,
                FontSize = 15,
                TextColor = Colors.Black
            };
            addressLabel.SetBinding(Label.TextProperty, "NutzeinheitAddress");

            Label countLabel = new()
            {
                Margin = new Thickness(5, 0, 10, 0),
                BackgroundColor = Colors.White,
                HorizontalOptions = LayoutOptions.End,
                VerticalOptions = LayoutOptions.Center,
                FontSize = 15,
                TextColor = Colors.Black
            };
            countLabel.SetBinding(Label.TextProperty, "NutzeinheitCount");

            grid.Add(addressLabel, 0, 0);
            grid.Add(countLabel, 1, 0);

            return grid;
        }

        private static Label CreateHeaderFirst()
        {
            var label = new Label
            {
                Margin = 5,
                BackgroundColor = Colors.White,
                HorizontalOptions = LayoutOptions.Start,
                VerticalOptions = LayoutOptions.Center,
                FontAttributes = FontAttributes.Bold,
                FontSize = 15,
                TextColor = Colors.Black,
                Text = "Adressen"
            };

            return label;
        }

        private static Label CreateHeaderTwo()
        {
            var label = new Label
            {
                Margin = new Thickness(5, 0, 10, 0),
                BackgroundColor = Colors.White,
                HorizontalOptions = LayoutOptions.End,
                VerticalOptions = LayoutOptions.Center,
                FontAttributes = FontAttributes.Bold,
                FontSize = 15,
                TextColor = Colors.Black,
                Text = "Anzahl NE"
            };

            return label;
        }

        private static Grid CreateGrid()
        {
            Grid grid = new()
            {
                RowSpacing = 1
            };

            // One row for Header
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // One row for the CollectionView
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Star });

            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(220) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(100) });

            return grid;
        }

        private static Label CreateHeader()
        {
            Label headerContent = new()
            {
                Padding = 0,
                Text = "Nutzeinheiten Info",
                FontAttributes = FontAttributes.Bold,
                TextColor = Colors.White,
                BackgroundColor = Color.FromArgb("#538EEC"),
                FontSize = 20,
                HorizontalTextAlignment = TextAlignment.Center,
                VerticalTextAlignment = TextAlignment.Center
            };
            return headerContent;
        }

        private Button CreateFooter()
        {
            Button okButton = new()
            {
                Text = "OK",
                HorizontalOptions = LayoutOptions.Center,
                BackgroundColor = Color.FromArgb("#538EEC"),
                TextColor = Colors.White,
                WidthRequest = 100,
                Command = AcceptButtonCommand
            };

            return okButton;
        }

        private void AcceptButtonExecuted()
        {
            if (sfPopup != null)
            {
                sfPopup.IsOpen = false;
            }
            source?.TrySetResult(DialogResponse.Accept);
        }

        #endregion

    }
}
