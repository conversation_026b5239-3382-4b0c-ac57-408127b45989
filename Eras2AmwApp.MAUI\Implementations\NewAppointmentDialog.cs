using Eras2AmwApp.Domain.Eras2Amw.Models;
using Eras2AmwApp.MAUI.Interfaces;
using Syncfusion.Maui.Popup;

namespace Eras2AmwApp.MAUI.Implementations
{
    public class NewAppointmentDialog : INewAppointmentDialog
    {
        #region Fields

        private TaskCompletionSource<DateTime?>? source;
        private SfPopup? sfPopup;
        private DatePicker? datePicker;
        private TimePicker? timePicker;
        private Command? acceptButtonCommand;
        private Command? cancelButtonCommand;

        #endregion

        #region Commands

        public Command AcceptButtonCommand => acceptButtonCommand ??= new Command(AcceptButtonExecuted);
        public Command CancelButtonCommand => cancelButtonCommand ??= new Command(CancelButtonExecuted);

        #endregion

        #region Public Methods

        public Task<DateTime?> ShowNewAppointmentDialog(Nutzer nutzer)
        {
            source = new TaskCompletionSource<DateTime?>();

            sfPopup = CreateDialogWindow(nutzer);
            sfPopup.Show();

            return source.Task;
        }

        #endregion

        #region Private Methods

        private SfPopup CreateDialogWindow(Nutzer nutzer)
        {
            var stackLayout = new VerticalStackLayout
            {
                Spacing = 15,
                Padding = 20
            };

            // Title
            var titleLabel = new Label
            {
                Text = $"Neuer Termin für {nutzer.FirstName} {nutzer.LastName}",
                FontSize = 18,
                FontAttributes = FontAttributes.Bold,
                HorizontalOptions = LayoutOptions.Center
            };
            stackLayout.Children.Add(titleLabel);

            // Date picker
            var dateLabel = new Label
            {
                Text = "Datum:",
                FontSize = 14,
                FontAttributes = FontAttributes.Bold
            };
            stackLayout.Children.Add(dateLabel);

            datePicker = new DatePicker
            {
                MinimumDate = DateTime.Today,
                MaximumDate = DateTime.Today.AddYears(1),
                Date = nutzer.NextAppointmentDate?.Date ?? DateTime.Today.AddDays(30),
                HeightRequest = 40
            };
            stackLayout.Children.Add(datePicker);

            // Time picker
            var timeLabel = new Label
            {
                Text = "Uhrzeit:",
                FontSize = 14,
                FontAttributes = FontAttributes.Bold
            };
            stackLayout.Children.Add(timeLabel);

            timePicker = new TimePicker
            {
                Time = nutzer.NextAppointmentDate?.TimeOfDay ?? new TimeSpan(9, 0, 0),
                HeightRequest = 40
            };
            stackLayout.Children.Add(timePicker);

            // Buttons
            var buttonLayout = new HorizontalStackLayout
            {
                Spacing = 10,
                HorizontalOptions = LayoutOptions.Center
            };

            var cancelButton = new Button
            {
                Text = "Abbrechen",
                BackgroundColor = Colors.Gray,
                TextColor = Colors.White,
                WidthRequest = 100,
                Command = CancelButtonCommand
            };

            var okButton = new Button
            {
                Text = "OK",
                BackgroundColor = Color.FromArgb("#538EEC"),
                TextColor = Colors.White,
                WidthRequest = 100,
                Command = AcceptButtonCommand
            };

            buttonLayout.Children.Add(cancelButton);
            buttonLayout.Children.Add(okButton);
            stackLayout.Children.Add(buttonLayout);

            var popup = new SfPopup
            {
                HeaderTitle = "Neuer Termin",
                ContentTemplate = new DataTemplate(() => stackLayout),
                HeightRequest = 350,
                WidthRequest = 400,
                ShowCloseButton = false,
                ShowFooter = false
            };

            return popup;
        }

        private void AcceptButtonExecuted()
        {
            try
            {
                if (datePicker != null && timePicker != null)
                {
                    var selectedDate = datePicker.Date;
                    var selectedTime = timePicker.Time;
                    var appointmentDateTime = selectedDate.Date.Add(selectedTime);

                    sfPopup?.Dismiss();
                    source?.TrySetResult(appointmentDateTime);
                }
                else
                {
                    sfPopup?.Dismiss();
                    source?.TrySetResult(null);
                }
            }
            catch (Exception)
            {
                sfPopup?.Dismiss();
                source?.TrySetResult(null);
            }
        }

        private void CancelButtonExecuted()
        {
            sfPopup?.Dismiss();
            source?.TrySetResult(null);
        }

        #endregion
    }
}
