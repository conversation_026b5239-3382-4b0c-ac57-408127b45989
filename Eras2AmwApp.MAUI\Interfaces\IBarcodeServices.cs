using System;
using System.Threading.Tasks;
using ZXing.Net.Maui;

namespace Eras2AmwApp.MAUI.Interfaces
{
    // Interface for barcode generation
    public interface IBarcodeGeneratorService
    {
        Task<byte[]> GenerateBarcodeAsync(string value, BarcodeFormat format, int width, int height);
    }

    // Interface for camera barcode reading
    public interface ICameraBarcodeReaderService
    {
        Task<string> ScanBarcodeAsync();
    }
}
