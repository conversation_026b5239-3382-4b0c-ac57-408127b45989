//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IDeviceMaintenanceDialog.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//  Interface for device maintenance dialog
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using Eras2AmwApp.BusinessLogic.Interfaces;
using Eras2AmwApp.Domain.Eras2Amw.Models;
using System.Threading.Tasks;

namespace Eras2AmwApp.MAUI.Interfaces
{
    public interface IDeviceMaintenanceDialog
    {
        Task<DeviceOrderState> ShowMaintenanceDialog(IDevice device);
    }
}
