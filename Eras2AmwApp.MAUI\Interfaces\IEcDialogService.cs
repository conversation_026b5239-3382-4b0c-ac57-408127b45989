using System.Threading.Tasks;
using Eras2AmwApp.MAUI.Enums;

namespace Eras2AmwApp.MAUI.Interfaces
{
    public interface IEcDialogService
    {
        void Info(string message, string? title = null);

        Task<DialogResponse> AcceptAsync(string message, string? acceptButtonText = null, string? title = null);

        Task<DialogResponse> AcceptDeclineAsync(string message, string acceptButtonText, string declineButtonText, string? title = null);

        void ShowBusyIndicator(AnimationTypes animationType = AnimationTypes.Gear, string title = "");

        void SetBusyIndicatorText(string text);

        void HideBusyIndicator();
    }
}
