using Eras2AmwApp.MAUI.ViewModels;

namespace Eras2AmwApp.MAUI.Interfaces
{
    public interface IEcNavigationService
    {
        bool ShowInitPageOnResume { get; set; }

        BaseViewModel PreviousPageViewModel { get; }

        Task InitializeAsync<TViewModel>() where TViewModel : BaseViewModel;

        Task NavigateToAsync<TViewModel>() where TViewModel : BaseViewModel;

        Task NavigateToAsync<TViewModel>(object parameter) where TViewModel : BaseViewModel;

        Task PopToRootAsync();

        Task RemoveLastFromBackStackAsync();

        Task RemoveBackStackAsync();

        Task GoBackAsync();
    }

}
