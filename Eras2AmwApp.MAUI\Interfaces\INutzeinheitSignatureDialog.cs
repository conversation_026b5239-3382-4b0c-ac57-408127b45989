//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="INutzeinheitSignatureDialog.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//  Interface for Nutzeinheit signature dialog
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using Eras2AmwApp.MAUI.Models;
using System.Threading.Tasks;

namespace Eras2AmwApp.MAUI.Interfaces
{
    public interface INutzeinheitSignatureDialog
    {
        Task ShowNutzeinheiSignatureDialog(NutzeinheitVM nutzeinheitVM);
    }
}
