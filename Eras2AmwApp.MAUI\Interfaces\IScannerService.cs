//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IScannerService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//  Interface for scanner service
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using System.Threading.Tasks;

namespace Eras2AmwApp.MAUI.Interfaces
{
    /// <summary>
    /// Interface for scanner service
    /// </summary>
    public interface IScannerService
    {
        /// <summary>
        /// Scans a barcode
        /// </summary>
        /// <returns>The scanned barcode</returns>
        Task<string> ScanBarcode();

        /// <summary>
        /// Reads a barcode
        /// </summary>
        /// <returns>The read barcode</returns>
        Task<string> ReadBarcode();
    }
}
