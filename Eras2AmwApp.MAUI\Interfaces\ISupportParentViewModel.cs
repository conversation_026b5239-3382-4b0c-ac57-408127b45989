//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="ISupportParentViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//    Interface for view models that need to reference their parent view models
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.Interfaces
{
    using Eras2AmwApp.MAUI.ViewModels;

    /// <summary>
    /// Interface for view models that need to reference their parent view models
    /// </summary>
    public interface ISupportParentViewModel
    {
        /// <summary>
        /// Gets the parent view model property
        /// </summary>
        /// <returns>The parent view model as AppointmentPageViewModel</returns>
        AppointmentPageViewModel GetParentProperty();

        /// <summary>
        /// Gets or sets the parent view model
        /// </summary>
        EcViewModelBase ParentViewModel { get; set; }
    }
}
