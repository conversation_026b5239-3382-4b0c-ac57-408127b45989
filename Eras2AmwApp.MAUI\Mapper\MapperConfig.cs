using AutoMapper;
using Eras2AmwApp.WebService.Mapper;

namespace Eras2AmwApp.MAUI.Mapper
{
    public class MapperConfig
    {
        public static void ConfigureMapper(IMapperConfigurationExpression config)
        {
            // Add profiles from WebService project
            config.AddProfile<OrdersProfile>();
            config.AddProfile<StammdatenProfile>();
            
            // Add any additional profiles here
        }
    }
}
