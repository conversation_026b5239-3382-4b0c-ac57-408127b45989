using Eras2AmwApp.Common.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace Eras2AmwApp.MAUI.MarkupExtensions
{
    [ContentProperty(nameof(Key))]
    public class LocalisationExtension : IMarkupExtension<string>
    {
        public string Key { get; set; } = string.Empty;

        public string ProvideValue(IServiceProvider serviceProvider)
        {
            if (string.IsNullOrWhiteSpace(Key))
                return string.Empty;

            var localisationService = Application.Current?.Handler?.MauiContext?.Services.GetService<ILocalisationService>();
            
            if (localisationService != null)
            {
                return localisationService.Get(Key);
            }

            return Key;
        }

        object IMarkupExtension.ProvideValue(IServiceProvider serviceProvider)
        {
            return ProvideValue(serviceProvider);
        }
    }
}
