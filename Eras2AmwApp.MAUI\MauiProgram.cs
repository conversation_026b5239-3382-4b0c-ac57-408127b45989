﻿using System;
using Eras2AmwApp.BusinessLogic.Interfaces;
using Eras2AmwApp.BusinessLogic.Services;
using BusinessLogicInterfaces = Eras2AmwApp.BusinessLogic.Interfaces;
using Eras2AmwApp.Common.Implementations;
using Eras2AmwApp.Common.Interfaces;
using Eras2AmwApp.Common.Services;
using Eras2AmwApp.Database.Implementations;
using Eras2AmwApp.Database.Interfaces;
using Eras2AmwApp.MAUI.Bootstrap;
using Eras2AmwApp.MAUI.Interfaces;
using Eras2AmwApp.MAUI.Mapper;
using Eras2AmwApp.MAUI.Services;
using Eras2AmwApp.MAUI.ViewModels;
using Eras2AmwApp.MAUI.Views;
using Eras2AmwApp.WebService.Implementations;
using Eras2AmwApp.WebService.Interfaces;
using FluentValidation;
using Microsoft.Extensions.Logging;
using ILogger = Serilog.ILogger;
using Syncfusion.Maui.Core.Hosting;
using SQLitePCL;
using ZXing.Net.Maui;
using ZXing.Net.Maui.Controls;


namespace Eras2AmwApp.MAUI
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            try
            {
                // Initialize SQLite
                // This initializes SQLite for the application
                Batteries_V2.Init();

                var builder = MauiApp.CreateBuilder();
                builder
                    .UseMauiApp<App>()
                    .ConfigureSyncfusionCore()
                    .UseBarcodeReader() // Register ZXing.Net.MAUI
                    .ConfigureFonts(fonts =>
                    {
                        fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                        fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                    });

                // Configure barcode reader
                builder.Services.AddSingleton<IBarcodeGeneratorService, BarcodeGeneratorService>();
                builder.Services.AddSingleton<ICameraBarcodeReaderService, CameraBarcodeReaderService>();

                // We'll use a simpler approach for barcode scanning
                // The actual scanning will be handled in the CameraBarcodeReaderService

                // Add exception handler for unhandled exceptions
                AppDomain.CurrentDomain.UnhandledException += (sender, args) =>
                {
                    // Handle unhandled exceptions
                };

#if DEBUG
                builder.Logging.AddDebug();
#endif

                // Register Syncfusion license
                Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("NTIyODA3QDMxMzkyZTMzMmUzMFp5UDBVMXhrckFmaVgzbkpEZ1hteCtWYk9TUEpMSTh1Ly8vbDF3US9vUnc9");

                // Register AutoMapper
                builder.Services.AddAutoMapper(MapperConfig.ConfigureMapper);

                // Register MAUI services
                builder.Services.AddSingleton<IEcDialogService, EcDialogService>();
                builder.Services.AddSingleton<IEcNavigationService>(provider =>
                    new EcNavigationService(
                        provider,
                        provider.GetRequiredService<ILogger>()));

                // Register MAUI Dialog services
                builder.Services.AddSingleton<IAppointmentNutzeinheitDetailsDialog, Implementations.AppointmentNutzeinheitDetailsDialog>();
                builder.Services.AddSingleton<INewAppointmentDialog, Implementations.NewAppointmentDialog>();
                builder.Services.AddSingleton<INutzeinheitAmwInfoDialog, Implementations.NutzeinheitAmwInfoDialog>();

                // Register Platform services
                builder.Services.AddSingleton<IPlatformService, MauiPlatformService>();

                // Register Common services
                builder.Services.AddSingleton<ISimpleFileLoggerFactory, SimpleFileLoggerFactory>();
                builder.Services.AddSingleton<ILocalisationService, LocalisationService>();
                builder.Services.AddSingleton<IResourceService, ResourceService>();
                builder.Services.AddSingleton<ILogger>(provider => provider.GetRequiredService<ISimpleFileLoggerFactory>().Create());
                builder.Services.AddSingleton<IBackupService, BackupService>();
                builder.Services.AddSingleton<INetworkServices, NetworkServices>();
                builder.Services.AddSingleton<IGlobalCacheTrash, GlobalCacheTrash>();

                // Register AppSettings based on build configuration
#if DEVELOPMENT
                builder.Services.AddSingleton<IAppSettings, AppSettingsDevelopment>();
#else
                builder.Services.AddSingleton<IAppSettings, AppSettings>();
#endif

                // Register ServiceLocator
                builder.Services.AddSingleton<IServiceLocator, ServiceLocator>();

                // Register Database services
                builder.Services.AddSingleton<IDbContextFactory, DbContextFactory>();
                builder.Services.AddSingleton<IDomainLoader, DomainLoader>();

                // Register WebService services
                builder.Services.AddSingleton<IAmwWebservice, AmwWebservice>();
                builder.Services.AddSingleton<ISyncBroker, SyncBroker>();

                // Register Business Logic services
                builder.Services.AddSingleton<ITestService, TestService>();
                builder.Services.AddSingleton<IAppointmentService, AppointmentService>();
                builder.Services.AddSingleton<ILoginService, LoginService>();
                builder.Services.AddSingleton<BusinessLogicInterfaces.IOrderService, OrderService>();
                builder.Services.AddSingleton<BusinessLogicInterfaces.IAbrechnungseinheitService, AbrechnungseinheitService>();
                builder.Services.AddSingleton<BusinessLogicInterfaces.INutzeinheitService, NutzeinheitService>();
                builder.Services.AddSingleton<IPersonService, PersonService>();
                builder.Services.AddSingleton<IAppDeviceInformationService, AppDeviceInformationService>();
                builder.Services.AddSingleton<IOptionService, OptionService>();
                builder.Services.AddSingleton<INutzerService, NutzerService>();
                builder.Services.AddSingleton<BusinessLogicInterfaces.IStammdatenService, StammdatenService>();
                builder.Services.AddSingleton<IAddressService, AddressService>();
                builder.Services.AddSingleton<IDeviceService, DeviceService>();
                builder.Services.AddSingleton<ISignatureService, SignatureService>();
                builder.Services.AddSingleton<IPhotoService, PhotoService>();
                builder.Services.AddSingleton<IRegistrationService, RegistrationService>();
                builder.Services.AddSingleton<ICloseApplicationService, Services.CloseApplicationService>();

                // Register ViewModels
                builder.Services.AddSingleton<RegistrationPageViewModel>();
                builder.Services.AddSingleton<LoginPageViewModel>(provider => new LoginPageViewModel(
                    provider.GetRequiredService<IServiceLocator>(),
                    provider.GetRequiredService<IEcDialogService>(),
                    provider.GetRequiredService<IEcNavigationService>(),
                    provider.GetRequiredService<ILoginService>(),
                    provider.GetRequiredService<IValidator<LoginPageViewModel>>(),
                    provider.GetRequiredService<IAppDeviceInformationService>(),
                    provider.GetRequiredService<IAmwWebservice>()
                ));
                builder.Services.AddSingleton<AppointmentPageViewModel>();
                builder.Services.AddSingleton<OrderPageViewModel>();

                // Register Views
                builder.Services.AddTransient<RegistrationPage>();
                builder.Services.AddTransient<QrScannerView>();
                builder.Services.AddTransient<LoginPage>();
                builder.Services.AddTransient<AppointmentPage>();
                builder.Services.AddTransient<OrderPage>();

                // Register Bootstrapper
                builder.Services.AddSingleton<Bootstrapper>();

                // Register App factory
                builder.Services.AddSingleton<App>(provider =>
                {
                    var bootstrapper = provider.GetRequiredService<Bootstrapper>();
                    return new App(bootstrapper);
                });

                // Register barcode services
                builder.Services.AddSingleton<IBarcodeGeneratorService, BarcodeGeneratorService>();
                builder.Services.AddSingleton<ICameraBarcodeReaderService, CameraBarcodeReaderService>();

                // Register validators
                builder.Services.AddSingleton<IValidator<LoginPageViewModel>, Validators.LoginPageViewModelValidator>();

                var app = builder.Build();

                // Note: Bootstrapper initialization is now handled in App.OnStart

                return app;
            }
            catch
            {
                // Create a minimal app that can at least start
                var builder = MauiApp.CreateBuilder();
                builder.UseMauiApp<App>();
                return builder.Build();
            }
        }
    }
}
