//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AbrechnungsPersonVM.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.Models
{
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using System.Collections.Generic;

    public class AbrechnungsPersonVM
    {
        public Guid PersonGuid { get; set; }
        public Guid PersonAEGuid { get; set; }
        public string PersonFirstname { get; set; } = string.Empty;
        public string PersonLastname { get; set; } = string.Empty;
        public Salutation PersonSalutation { get; set; } = new Salutation();
        public Title? PersonTitle { get; set; }
        public string PersonNote { get; set; } = string.Empty;
        public string PersonPosition { get; set; } = string.Empty;
        public Dictionary<CommunicationKind, string> PersonCommunicationDetails { get; set; } = new();
        public string PersonCommunicationValue { get; set; } = string.Empty;
        public string PersonCommunicationTyp { get; set; } = string.Empty;
    }
}
