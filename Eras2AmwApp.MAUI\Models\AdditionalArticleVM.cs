//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AdditionalArticleVM.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//  View model for AdditionalArticle
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using CommunityToolkit.Mvvm.ComponentModel;
using Eras2AmwApp.Domain.Eras2Amw.Models;

namespace Eras2AmwApp.MAUI.Models
{
    /// <summary>
    /// View model for AdditionalArticle
    /// </summary>
    public class AdditionalArticleVM : ObservableObject
    {
        private AdditionalArticle _additionalArticle;
        private decimal _amount;
        private bool _isCreatedByApp;

        /// <summary>
        /// Gets or sets the AdditionalArticle
        /// </summary>
        public AdditionalArticle AdditionalArticle
        {
            get => _additionalArticle;
            set => SetProperty(ref _additionalArticle, value);
        }

        /// <summary>
        /// Gets or sets the amount
        /// </summary>
        public decimal Amount
        {
            get => _amount;
            set => SetProperty(ref _amount, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the article is created by app
        /// </summary>
        public bool IsCreatedByApp
        {
            get => _isCreatedByApp;
            set => SetProperty(ref _isCreatedByApp, value);
        }
    }
}
