//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AppointmentVm.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//    
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.Models
{
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using System;
    using CommunityToolkit.Mvvm.ComponentModel;
    using Eras2AmwApp.MAUI.Interfaces;
    using Eras2AmwApp.MAUI.ViewModels;

    public class AppointmentVm : ObservableObject, ISupportParentViewModel
    {
        public AppointmentPageViewModel GetParentProperty()
        {
            return ParentViewModel as AppointmentPageViewModel;
        }

        private EcViewModelBase _parentViewModel;
        public EcViewModelBase ParentViewModel
        {
            get { return _parentViewModel; }
            set { SetProperty(ref _parentViewModel, value); }
        }

        private Guid _guid;
        public Guid Guid
        {
            get { return _guid; }
            set { SetProperty(ref _guid, value); }
        }

        private OrderState _orderState;
        public OrderState OrderState
        {
            get { return _orderState; }
            set { SetProperty(ref _orderState, value); }
        }

        private ProcessState _nutzeinheitenState;
        public ProcessState NutzeinheitenState
        {
            get { return _nutzeinheitenState; }
            set { SetProperty(ref _nutzeinheitenState, value); }
        }

        private DateTime _startTime;
        public DateTime StartTime
        {
            get { return _startTime; }
            set { SetProperty(ref _startTime, value); }
        }

        private DateTime _endTime;
        public DateTime EndTime
        {
            get { return _endTime; }
            set { SetProperty(ref _endTime, value); }
        }

        private string _dateTime;
        public string DateTime
        {
            get { return _dateTime; }
            set { SetProperty(ref _dateTime, value); }
        }

        private string _orderNumber;
        public string OrderNumber
        {
            get { return _orderNumber; }
            set { SetProperty(ref _orderNumber, value); }
        }

        private bool _hasNote;
        public bool HasNote
        {
            get { return _hasNote; }
            set { SetProperty(ref _hasNote, value); }
        }

        private string _orderLabel;
        public string OrderLabel
        {
            get { return _orderLabel; }
            set { SetProperty(ref _orderLabel, value); }
        }

        private string _address;
        public string Address
        {
            get { return _address; }
            set { SetProperty(ref _address, value); }
        }

        private string _orderNote;
        public string OrderNote
        {
            get { return _orderNote; }
            set { SetProperty(ref _orderNote, value); }
        }
    }
}
