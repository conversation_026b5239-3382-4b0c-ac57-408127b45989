//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="CalendarEventCollection.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//    Collection class for calendar events
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.Models
{
    using System.Collections.ObjectModel;
    using Syncfusion.Maui.Calendar;

    /// <summary>
    /// Collection class for calendar events
    /// </summary>
    public class CalendarEventCollection : ObservableCollection<CalendarInlineEvent>
    {
    }
}
