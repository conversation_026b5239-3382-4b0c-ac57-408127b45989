//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="CalendarInlineEvent.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//    Model class for calendar inline events
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.Models
{
    using System;
    using Microsoft.Maui.Graphics;

    /// <summary>
    /// Model class for calendar inline events
    /// </summary>
    public class CalendarInlineEvent
    {
        /// <summary>
        /// Gets or sets the start time of the event
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Gets or sets the end time of the event
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Gets or sets the subject of the event
        /// </summary>
        public string Subject { get; set; }

        /// <summary>
        /// Gets or sets the color of the event
        /// </summary>
        public Color Color { get; set; }
    }
}
