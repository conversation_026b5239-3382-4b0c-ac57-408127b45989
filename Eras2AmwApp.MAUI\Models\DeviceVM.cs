//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="DeviceVM.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//  Device View Model for MAUI
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using Eras2AmwApp.BusinessLogic.Interfaces;
using Eras2AmwApp.BusinessLogic.Models;
using Eras2AmwApp.Domain.Eras2Amw.Enums;
using Eras2AmwApp.Domain.Eras2Amw.Models;
using System;
using System.Collections.Generic;

namespace Eras2AmwApp.MAUI.Models
{
    public class DeviceVM
    {
        public DeviceClass DeviceClass { get; set; }
        public string DeviceNumber { get; set; } = string.Empty;
        public string DeviceLabel { get; set; } = string.Empty;
        public string DeviceRoom { get; set; } = string.Empty;
        public string DeviceOngoingNumber { get; set; } = string.Empty;
        public object? ParentViewModel { get; set; }
        public IDevice? Device { get; set; }
        public List<Photo> Photos { get; set; } = new();
        public DeviceUiState DeviceUiState { get; set; } = new();
        public bool IsDeviceListEditedOrCreated { get; set; }
        public bool IsDeviceMaintained { get; set; }
    }
}
