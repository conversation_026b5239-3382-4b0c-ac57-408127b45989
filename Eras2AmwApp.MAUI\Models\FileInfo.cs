//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="FileInfo.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//  File information class for MAUI
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.Models
{
    /// <summary>
    /// File information class for MAUI
    /// </summary>
    public class FileInfo
    {
        /// <summary>
        /// Gets or sets the path of the file
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// Gets or sets the name of the file
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the content type of the file
        /// </summary>
        public string ContentType { get; set; }
    }
}
