//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitVM.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//    View model for Nutzeinheit
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.Models
{
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.MAUI.Interfaces;
    using Eras2AmwApp.MAUI.ViewModels;
    using CommunityToolkit.Mvvm.ComponentModel;
    using System;

    /// <summary>
    /// View model for Nutzeinheit
    /// </summary>
    public class NutzeinheitVM : ObservableObject, ISupportParentViewModel
    {
        private EcViewModelBase _parentViewModel;

        /// <summary>
        /// Gets the parent view model property
        /// </summary>
        /// <returns>The parent view model as AppointmentPageViewModel</returns>
        public AppointmentPageViewModel GetParentProperty()
        {
            return ParentViewModel as AppointmentPageViewModel;
        }

        /// <summary>
        /// Gets or sets the parent view model
        /// </summary>
        public EcViewModelBase ParentViewModel
        {
            get { return _parentViewModel; }
            set { SetProperty(ref _parentViewModel, value); }
        }

        /// <summary>
        /// Gets or sets the Nutzeinheit GUID
        /// </summary>
        public Guid NutzeinheitGuid { get; set; }

        /// <summary>
        /// Gets or sets the Nutzer GUID
        /// </summary>
        public Guid NutzerGuid { get; set; }

        /// <summary>
        /// Gets or sets the appointment date
        /// </summary>
        public DateTime AppointmentDate { get; set; }

        /// <summary>
        /// Gets or sets the Nutzeinheit order
        /// </summary>
        public NutzeinheitOrder NutzeinheitOrder { get; set; }

        /// <summary>
        /// Gets or sets the Nutzer name and location
        /// </summary>
        public string NutzerNameLocation { get; set; }

        /// <summary>
        /// Gets or sets the Nutzeinheit note
        /// </summary>
        public string NutzeinheitNote { get; set; }

        /// <summary>
        /// Gets or sets the Nutzeinheit number
        /// </summary>
        public string NutzeinheitNumber { get; set; }

        /// <summary>
        /// Gets or sets the Nutzeinheit address
        /// </summary>
        public string NutzeinheitAddress { get; set; }

        /// <summary>
        /// Gets or sets the Nutzeinheit state
        /// </summary>
        public ProcessState NutzeinheitState { get; set; }

        /// <summary>
        /// Gets or sets the Nutzer kind
        /// </summary>
        public NutzerKind NutzerKind { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the Nutzeinheit is locked
        /// </summary>
        public bool IsNeLocked { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the Nutzeinheit has a new appointment
        /// </summary>
        public bool HasNewAppointment { get; set; }

        /// <summary>
        /// Gets or sets the Nutzeinheit UI state
        /// </summary>
        public NutzeinheitUiState NutzeinheitUiState { get; set; }
    }
}
