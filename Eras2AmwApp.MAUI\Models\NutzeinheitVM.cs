//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitVM.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//    View model for Nutzeinheit
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.Models
{
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.MAUI.Interfaces;
    using Eras2AmwApp.MAUI.ViewModels;
    using CommunityToolkit.Mvvm.ComponentModel;
    using System;

    /// <summary>
    /// View model for Nutzeinheit
    /// </summary>
    public class NutzeinheitVM : ObservableObject, ISupportParentViewModel
    {
        private EcViewModelBase _parentViewModel;

        /// <summary>
        /// Gets the parent view model property
        /// </summary>
        /// <returns>The parent view model as AppointmentPageViewModel</returns>
        public AppointmentPageViewModel GetParentProperty()
        {
            return ParentViewModel as AppointmentPageViewModel;
        }

        /// <summary>
        /// Gets or sets the parent view model
        /// </summary>
        public EcViewModelBase ParentViewModel
        {
            get { return _parentViewModel; }
            set { SetProperty(ref _parentViewModel, value); }
        }

        private Guid _nutzeinheitGuid;
        /// <summary>
        /// Gets or sets the Nutzeinheit GUID
        /// </summary>
        public Guid NutzeinheitGuid
        {
            get { return _nutzeinheitGuid; }
            set { SetProperty(ref _nutzeinheitGuid, value); }
        }

        private Guid _nutzerGuid;
        /// <summary>
        /// Gets or sets the Nutzer GUID
        /// </summary>
        public Guid NutzerGuid
        {
            get { return _nutzerGuid; }
            set { SetProperty(ref _nutzerGuid, value); }
        }

        private DateTime _appointmentDate;
        /// <summary>
        /// Gets or sets the appointment date
        /// </summary>
        public DateTime AppointmentDate
        {
            get { return _appointmentDate; }
            set { SetProperty(ref _appointmentDate, value); }
        }

        private NutzeinheitOrder _nutzeinheitOrder;
        /// <summary>
        /// Gets or sets the Nutzeinheit order
        /// </summary>
        public NutzeinheitOrder NutzeinheitOrder
        {
            get { return _nutzeinheitOrder; }
            set { SetProperty(ref _nutzeinheitOrder, value); }
        }

        private string _nutzerNameLocation;
        /// <summary>
        /// Gets or sets the Nutzer name and location
        /// </summary>
        public string NutzerNameLocation
        {
            get { return _nutzerNameLocation; }
            set { SetProperty(ref _nutzerNameLocation, value); }
        }

        private string _nutzerContact;
        /// <summary>
        /// Gets or sets the Nutzer contact
        /// </summary>
        public string NutzerContact
        {
            get { return _nutzerContact; }
            set { SetProperty(ref _nutzerContact, value); }
        }

        private string _nutzeinheitNote;
        /// <summary>
        /// Gets or sets the Nutzeinheit note
        /// </summary>
        public string NutzeinheitNote
        {
            get { return _nutzeinheitNote; }
            set { SetProperty(ref _nutzeinheitNote, value); }
        }

        private string _nutzeinheitNumber;
        /// <summary>
        /// Gets or sets the Nutzeinheit number
        /// </summary>
        public string NutzeinheitNumber
        {
            get { return _nutzeinheitNumber; }
            set { SetProperty(ref _nutzeinheitNumber, value); }
        }

        private string _nutzeinheitAddress;
        /// <summary>
        /// Gets or sets the Nutzeinheit address
        /// </summary>
        public string NutzeinheitAddress
        {
            get { return _nutzeinheitAddress; }
            set { SetProperty(ref _nutzeinheitAddress, value); }
        }

        private ProcessState _nutzeinheitState;
        /// <summary>
        /// Gets or sets the Nutzeinheit state
        /// </summary>
        public ProcessState NutzeinheitState
        {
            get { return _nutzeinheitState; }
            set { SetProperty(ref _nutzeinheitState, value); }
        }

        private NutzerKind _nutzerKind;
        /// <summary>
        /// Gets or sets the Nutzer kind
        /// </summary>
        public NutzerKind NutzerKind
        {
            get { return _nutzerKind; }
            set { SetProperty(ref _nutzerKind, value); }
        }

        private bool _isNeLocked;
        /// <summary>
        /// Gets or sets a value indicating whether the Nutzeinheit is locked
        /// </summary>
        public bool IsNeLocked
        {
            get { return _isNeLocked; }
            set { SetProperty(ref _isNeLocked, value); }
        }

        private bool _hasNewAppointment;
        /// <summary>
        /// Gets or sets a value indicating whether the Nutzeinheit has a new appointment
        /// </summary>
        public bool HasNewAppointment
        {
            get { return _hasNewAppointment; }
            set { SetProperty(ref _hasNewAppointment, value); }
        }

        private NutzeinheitUiState _nutzeinheitUiState;
        /// <summary>
        /// Gets or sets the Nutzeinheit UI state
        /// </summary>
        public NutzeinheitUiState NutzeinheitUiState
        {
            get { return _nutzeinheitUiState; }
            set { SetProperty(ref _nutzeinheitUiState, value); }
        }
    }
}
