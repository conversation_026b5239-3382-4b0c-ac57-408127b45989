//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="TechnicianAppointmentVM.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//    View model for technician appointments in tree view
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.Models.TreeViewModels
{
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using CommunityToolkit.Mvvm.ComponentModel;
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;

    /// <summary>
    /// View model for technician appointments in tree view
    /// </summary>
    public class TechnicianAppointmentVM : ObservableObject
    {
        private string _fileName;
        /// <summary>
        /// Gets or sets the file name
        /// </summary>
        public string FileName
        {
            get { return _fileName; }
            set { SetProperty(ref _fileName, value); }
        }

        private List<Nutzer> _nutzerList;
        /// <summary>
        /// Gets or sets the list of Nutzer
        /// </summary>
        public List<Nutzer> NutzerList
        {
            get { return _nutzerList; }
            set { SetProperty(ref _nutzerList, value); }
        }

        private ObservableCollection<TechnicianAppointmentVM> _appointmentTermine;
        /// <summary>
        /// Gets or sets the collection of appointment terms
        /// </summary>
        public ObservableCollection<TechnicianAppointmentVM> AppointmentTermine
        {
            get { return _appointmentTermine; }
            set { SetProperty(ref _appointmentTermine, value); }
        }

        /// <summary>
        /// Gets or sets the appointment
        /// </summary>
        public Appointment Appointment { get; set; }

        private Guid _nutzerGuid;
        /// <summary>
        /// Gets or sets the Nutzer GUID
        /// </summary>
        public Guid NutzerGuid
        {
            get { return _nutzerGuid; }
            set { SetProperty(ref _nutzerGuid, value); }
        }
    }
}
