using Eras2AmwApp.BusinessLogic.Interfaces;

namespace Eras2AmwApp.MAUI.Services
{
    public class CloseApplicationService : ICloseApplicationService
    {
        public void CloseApplication()
        {
            // For MAUI applications, we can use Application.Current.Quit()
            // This is the proper way to close a MAUI application
            Application.Current?.Quit();
        }
    }
}
