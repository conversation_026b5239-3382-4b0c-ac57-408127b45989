using System;
using System.Threading.Tasks;
using Eras2AmwApp.MAUI.Enums;
using Eras2AmwApp.MAUI.Interfaces;
using Syncfusion.Maui.Popup;
using Syncfusion.Maui.ProgressBar;

// Add this if DialogResponse is an enum or class you need to define in this file
namespace Eras2AmwApp.MAUI.Enums
{
    public enum DialogResponse
    {
        Accept,
        Decline
    }
}

namespace Eras2AmwApp.MAUI.Services
{
    public class EcDialogService : IEcDialogService
    {
        private SfPopup? popupLayout;
        private TaskCompletionSource<DialogResponse>? source;
        private SfPopup? busyIndicatorPopup;
        private SfCircularProgressBar? busyIndicator;
        private Command? declineButtonCommand;
        private Command? acceptButtonCommand;

        public Command DeclineButtonCommand => declineButtonCommand ??= new Command(DeclineButtonExecuted);
        public Command AcceptButtonCommand => acceptButtonCommand ??= new Command(AcceptButtonExecuted);

        public void Info(string message, string? title = null)
        {
            var stackLayout = new VerticalStackLayout();
            Label content = CreateContent(message);
            content.HorizontalTextAlignment = TextAlignment.Center;
            stackLayout.Children.Add(content);

            popupLayout = new SfPopup
            {
                HeaderTitle = title ?? string.Empty,
                ContentTemplate = new DataTemplate(() => stackLayout),
                HeightRequest = -1, // Auto height
                WidthRequest = 350,
                ShowFooter = false
            };

            popupLayout.Show();
        }

        public Task<DialogResponse> AcceptAsync(string message, string? acceptButtonText = null, string? title = null)
        {
            source = new TaskCompletionSource<DialogResponse>();

            var stackLayout = new VerticalStackLayout
            {
                VerticalOptions = LayoutOptions.Fill,
                HorizontalOptions = LayoutOptions.Fill
            };

            Label content = CreateContent(message);
            stackLayout.Children.Add(content);

            popupLayout = new SfPopup
            {
                HeaderTitle = title ?? string.Empty,
                ShowCloseButton = false,
                ContentTemplate = new DataTemplate(() => stackLayout),
                HeightRequest = -1, // Auto height
                WidthRequest = 350,
                ShowFooter = true,
                FooterTemplate = new DataTemplate(() => CreateFooter(acceptButtonText, null))
            };

            popupLayout.Show();

            return source.Task;
        }

        public Task<DialogResponse> AcceptDeclineAsync(string message, string acceptButtonText, string declineButtonText, string? title = null)
        {
            source = new TaskCompletionSource<DialogResponse>();

            var stackLayout = new VerticalStackLayout();
            Label content = CreateContent(message);
            stackLayout.Children.Add(content);

            popupLayout = new SfPopup
            {
                HeaderTitle = title ?? string.Empty,
                ShowCloseButton = false,
                ContentTemplate = new DataTemplate(() => stackLayout),
                HeightRequest = -1, // Auto height
                WidthRequest = 320,
                ShowFooter = true,
                FooterTemplate = new DataTemplate(() => CreateFooter(acceptButtonText, declineButtonText))
            };

            popupLayout.Show();
            return source.Task;
        }

        public void ShowBusyIndicator(AnimationTypes animationType = AnimationTypes.Gear, string title = "")
        {
            var stackLayout = new VerticalStackLayout
            {
                VerticalOptions = LayoutOptions.Fill,
                HorizontalOptions = LayoutOptions.Fill
            };

            busyIndicator = new SfCircularProgressBar
            {
                IsIndeterminate = true,
                HeightRequest = 70,
                WidthRequest = 70,
            };

            var titleLabel = new Label
            {
                Text = title,
                HorizontalOptions = LayoutOptions.Center,
                Margin = new Thickness(0, 10, 0, 0)
            };

            stackLayout.Children.Add(busyIndicator);
            stackLayout.Children.Add(titleLabel);

            busyIndicatorPopup = new SfPopup
            {
                ShowHeader = false,
                ShowFooter = false,
                ShowCloseButton = false,
                HeightRequest = 200,
                ContentTemplate = new DataTemplate(() => stackLayout)
            };

            busyIndicatorPopup.Show();
        }

        public void SetBusyIndicatorText(string text)
        {
            if (busyIndicatorPopup == null || !busyIndicatorPopup.IsOpen)
            {
                return;
            }

            // Since we can't directly access the content, we need to update the template
            if (busyIndicatorPopup.ContentTemplate != null &&
                busyIndicatorPopup.ContentTemplate.CreateContent() is VerticalStackLayout stackLayout &&
                stackLayout.Children.Count > 1 &&
                stackLayout.Children[1] is Label titleLabel)
            {
                titleLabel.Text = text ?? throw new ArgumentNullException(nameof(text));
            }
        }

        public void HideBusyIndicator()
        {
            if (busyIndicator == null)
            {
                return;
            }

            if (busyIndicatorPopup == null)
            {
                return;
            }

            busyIndicator.IsIndeterminate = false;
            busyIndicatorPopup.IsOpen = false;
        }

        private void AcceptButtonExecuted()
        {
            if (popupLayout != null)
            {
                popupLayout.IsOpen = false;
            }
            source?.TrySetResult(DialogResponse.Accept);
        }

        private void DeclineButtonExecuted()
        {
            if (popupLayout != null)
            {
                popupLayout.IsOpen = false;
            }
            source?.TrySetResult(DialogResponse.Decline);
        }

        private Grid CreateFooter(string? acceptButtonText, string? declineButtonText)
        {
            var footerGrid = new Grid();
            footerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Star });
            footerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Star });

            if (declineButtonText != null)
            {
                var declineButton = new Button
                {
                    Text = declineButtonText,
                    Command = DeclineButtonCommand,
                    HorizontalOptions = LayoutOptions.Fill,
                    Margin = new Thickness(5)
                };
                Grid.SetColumn(declineButton, 0);
                footerGrid.Children.Add(declineButton);
            }

            var acceptButton = new Button
            {
                Text = acceptButtonText ?? "OK",
                Command = AcceptButtonCommand,
                HorizontalOptions = LayoutOptions.Fill,
                Margin = new Thickness(5)
            };

            Grid.SetColumn(acceptButton, declineButtonText != null ? 1 : 0);
            if (declineButtonText == null)
            {
                Grid.SetColumnSpan(acceptButton, 2);
            }

            footerGrid.Children.Add(acceptButton);

            return footerGrid;
        }

        private static Label CreateContent(string message)
        {
            var label = new Label
            {
                Margin = new Thickness(5, 0, 5, 0),
                LineBreakMode = LineBreakMode.WordWrap,
                BackgroundColor = Colors.White,
                HorizontalOptions = LayoutOptions.Fill,
                VerticalOptions = LayoutOptions.Center,
                FontSize = 20,
                TextColor = Colors.Black,
                Text = message
            };

            return label;
        }

        // Implementation for IEcDialogService.ShowError
        public void ShowError(string message, string? title = null)
        {
            Info(message, title ?? "Error");
        }

        // Implementation for IEcDialogService.ShowErrorAsync
        public Task ShowErrorAsync(string message, string title = null)
        {
            return AcceptAsync(message, "OK", title ?? "Error");
        }
    }
}