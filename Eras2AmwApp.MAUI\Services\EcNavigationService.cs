using Eras2AmwApp.MAUI.Interfaces;
using Eras2AmwApp.MAUI.ViewModels;
using System;
using System.Threading.Tasks;
using ILogger = Serilog.ILogger;

namespace Eras2AmwApp.MAUI.Services
{
    public class EcNavigationService : IEcNavigationService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger _logger;

        public EcNavigationService(IServiceProvider serviceProvider, ILogger logger)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public BaseViewModel PreviousPageViewModel { get; private set; }

        public bool ShowInitPageOnResume { get; set; }

        public Task GoBackAsync()
        {
            return Shell.Current.GoToAsync("..");
        }

        public Task InitializeAsync<TViewModel>() where TViewModel : BaseViewModel
        {
            return NavigateToAsync<TViewModel>();
        }

        public Task NavigateToAsync<TViewModel>() where TViewModel : BaseViewModel
        {
            return NavigateToAsync<TViewModel>(null);
        }

        public async Task NavigateToAsync<TViewModel>(object parameter) where TViewModel : BaseViewModel
        {
            try
            {
                string typeName = typeof(TViewModel).Name;

                // Check if Shell.Current is null
                if (Shell.Current == null)
                {
                    throw new InvalidOperationException("Shell.Current is null. Cannot navigate.");
                }

                // Get the ViewModel from the service provider

                var viewModel = _serviceProvider.GetService(typeof(TViewModel)) as TViewModel;
                if (viewModel == null)
                {
                    try
                    {
                        // Try to create a default instance as fallback
                        viewModel = Activator.CreateInstance<TViewModel>();

                        if (viewModel == null)
                        {
                            throw new InvalidOperationException($"Failed to create instance of {typeName} using Activator");
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new InvalidOperationException($"Error creating instance of {typeName}", ex);
                    }
                }

                // Store the previous view model
                if (Shell.Current.CurrentPage?.BindingContext is BaseViewModel currentViewModel)
                {
                    PreviousPageViewModel = currentViewModel;
                }

                // Set up the view model with the parameter
                if (viewModel is EcViewModelBase ecViewModel)
                {
                    try
                    {
                        await ecViewModel.SetupAsync(parameter);
                    }
                    catch (Exception ex)
                    {
                        throw new InvalidOperationException($"Error setting up {ecViewModel.GetType().Name}", ex);
                    }
                }

                // Get the route for the view model
                var viewModelType = typeof(TViewModel);
                var viewName = viewModelType.Name.Replace("ViewModel", "");
                var pageName = viewName + "Page";

                // Try multiple navigation approaches
                Exception lastException = null;

                // Approach 1: Try with page name
                try
                {
                    var route = $"//{pageName}";
                    await Shell.Current.GoToAsync(route);
                    return; // If successful, return early
                }
                catch (Exception ex1)
                {
                    lastException = ex1;
                }

                // Approach 2: Try with view name
                try
                {
                    var route = $"//{viewName}";
                    await Shell.Current.GoToAsync(route);
                    return; // If successful, return early
                }
                catch (Exception ex2)
                {
                    lastException = ex2;
                }

                // Approach 3: Try with route name from AppShell.xaml
                try
                {
                    // Check if the view name is "Login" or "Registration" and use the route name
                    string routeName = viewName switch
                    {
                        "Login" => "LoginPage",
                        "Registration" => "RegistrationPage",
                        _ => viewName
                    };

                    var route = $"//{routeName}";
                    await Shell.Current.GoToAsync(route);
                    return; // If successful, return early
                }
                catch (Exception ex3)
                {
                    lastException = ex3;
                    throw new Exception($"All navigation approaches failed for {viewName}", lastException);
                }
            }
            catch (Exception ex)
            {
                try
                {
                    await Shell.Current.DisplayAlert("Navigation Error", $"Could not navigate to the requested page: {ex.Message}", "OK");
                }
                catch
                {
                    // Failed to show error dialog
                }

                throw; // Re-throw the exception to be caught by the caller
            }
        }

        public Task PopToRootAsync()
        {
            return Shell.Current.GoToAsync("//");
        }

        public Task RemoveBackStackAsync()
        {
            return Shell.Current.GoToAsync($"//{Shell.Current.CurrentState.Location}", true);
        }

        public Task RemoveLastFromBackStackAsync()
        {
            // This is not directly supported in Shell, but we can implement it if needed
            return Task.CompletedTask;
        }
    }
}


