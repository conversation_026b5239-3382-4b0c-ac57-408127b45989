using Eras2AmwApp.Common.Interfaces;
using Microsoft.Maui.Storage;
using Microsoft.Maui.ApplicationModel;
using System;
using System.IO;
using Eras2AmwApp.Common.Implementations;

namespace Eras2AmwApp.MAUI.Services
{
    public class MauiPlatformService : IPlatformService
    {
        public MauiPlatformService()
        {
            // Create an AppSettings instance to get directory paths
            var appSettings = new AppSettings(this);

            // Ensure all required directories exist
            EnsureDirectoryExists(GetDownloadFolderPath);
            EnsureDirectoryExists(appSettings.DatabaseDirectory);
            EnsureDirectoryExists(appSettings.LogFilesDirectory);
            EnsureDirectoryExists(appSettings.WebserviceDirectory);
            EnsureDirectoryExists(appSettings.WebserviceUploadDirectory);
            EnsureDirectoryExists(appSettings.WebserviceDownloadDirectory);
            EnsureDirectoryExists(appSettings.PicturesDirectory);
            EnsureDirectoryExists(appSettings.SignaturesDirectory);
            EnsureDirectoryExists(appSettings.BackupDirectory);
        }

        public string RootDirectory => FileSystem.AppDataDirectory;

        public string AppVersion => AppInfo.VersionString;

        // MAUI doesn't have direct SD card access, so we'll use app-specific storage
        public bool ExistsSdCard => false;

        public string ExternalPath => FileSystem.CacheDirectory;

        public string SdCardPath => FileSystem.CacheDirectory;

        public Stream GetAssetDatabaseStream()
        {
            
            var assetManager = Android.App.Application.Context.Assets;
            return assetManager.Open("test_eras2_amw.sqlite3");
            
        }

        public string GetDownloadFolderPath => Path.Combine(FileSystem.CacheDirectory, "Downloads");

        private void EnsureDirectoryExists(string path)
        {
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
        }
    }
}
