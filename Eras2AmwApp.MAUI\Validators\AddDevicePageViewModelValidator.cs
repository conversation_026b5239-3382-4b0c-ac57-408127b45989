//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AddDevicePageViewModelValidator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//  Validator for AddDevicePageViewModel
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using Eras2AmwApp.MAUI.ViewModels;
using FluentValidation;

namespace Eras2AmwApp.MAUI.Validators
{
    /// <summary>
    /// Validator for AddDevicePageViewModel
    /// </summary>
    public class AddDevicePageViewModelValidator : AbstractValidator<AddDevicePageViewModel>
    {
        public AddDevicePageViewModelValidator()
        {
            RuleFor(x => x.DeviceNumber)
                .NotEmpty().WithMessage("Die Gerätenummer darf nicht leer sein.");

            RuleFor(x => x.SelectedRoom)
                .NotNull().WithMessage("Bitte wählen Sie einen Raum aus.");

            RuleFor(x => x.OngoingNumber)
                .NotEmpty().WithMessage("Die laufende Nummer darf nicht leer sein.");

            RuleFor(x => x.SelectedDeviceKind)
                .NotNull().WithMessage("Bitte wählen Sie eine Geräteart aus.");

            RuleFor(x => x.SelectedDeviceCatalog)
                .NotNull().WithMessage("Bitte wählen Sie einen Gerätekatalog aus.");
        }
    }
}
