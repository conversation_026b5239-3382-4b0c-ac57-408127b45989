//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AppointmentPageViewModelValidator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//  Validator for the AppointmentPageViewModel
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.Validators
{
    using FluentValidation;
    using ViewModels;

    public class AppointmentPageViewModelValidator : AbstractValidator<AppointmentPageViewModel>
    {
        public AppointmentPageViewModelValidator()
        {
            RuleFor(x => x.UserEmail)
                .EmailAddress().When(x => !string.IsNullOrEmpty(x.UserEmail));
        }
    }
}
