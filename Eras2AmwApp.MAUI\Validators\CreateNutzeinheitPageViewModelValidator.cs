//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="CreateNutzeinheitPageViewModelValidator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//  Validator for CreateNutzeinheitPageViewModel
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using Eras2AmwApp.MAUI.ViewModels;
using FluentValidation;

namespace Eras2AmwApp.MAUI.Validators
{
    public class CreateNutzeinheitPageViewModelValidator : AbstractValidator<CreateNutzeinheitPageViewModel>
    {
        public CreateNutzeinheitPageViewModelValidator()
        {
            RuleFor(x => x.NutzeinheitNummer)
                .NotEmpty().WithMessage("Die Nutzeinheit Nummer darf nicht leer sein.");

            RuleFor(x => x.NutzeinheitLage)
                .NotEmpty().WithMessage("Die Nutzeinheit Lage darf nicht leer sein.");
        }
    }
}
