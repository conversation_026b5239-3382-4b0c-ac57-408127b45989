//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="LoginPageViewModelValidator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//  Validator for LoginPageViewModel
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using Eras2AmwApp.MAUI.ViewModels;
using FluentValidation;

namespace Eras2AmwApp.MAUI.Validators
{
    public class LoginPageViewModelValidator : AbstractValidator<LoginPageViewModel>
    {
        public LoginPageViewModelValidator()
        {
             RuleFor(x => x.Username)
                .NotEmpty().WithMessage("Der Nutzername darf nicht leer sein.")
                .Length(2, 25).WithMessage("Der Nutzername muss zwischen 2 und 25 Zeichen lang sein.");

            RuleFor(x => x.Password)
                .NotEmpty().WithMessage("Das Passwort darf nicht leer sein.")
                .Length(2, 25).WithMessage("Das Passwort muss zwischen 2 und 25 Zeichen lang sein.");
        }
    }
}
