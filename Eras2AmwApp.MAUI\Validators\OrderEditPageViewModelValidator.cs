//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="OrderEditPageViewModelValidator.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.Validators
{
    using Eras2AmwApp.MAUI.ViewModels;
    using FluentValidation;

    public class OrderEditPageViewModelValidator : AbstractValidator<OrderEditPageViewModel>
    {
        public OrderEditPageViewModelValidator()
        {
            RuleFor(x => x.PersonSalutation)
                .NotNull()
                .WithMessage("Bitte wählen Sie eine Anrede aus.");

            RuleFor(x => x.PersonFirstName)
                .NotEmpty()
                .WithMessage("Bitte geben Sie einen Vornamen ein.");

            RuleFor(x => x.PersonLastName)
                .NotEmpty()
                .WithMessage("Bitte geben Sie einen Nachnamen ein.");

            RuleFor(x => x.PersonPosition)
                .NotEmpty()
                .WithMessage("Bitte geben Sie eine Position ein.");

            RuleFor(x => x.PersonCommunicationType)
                .NotNull()
                .WithMessage("Bitte wählen Sie einen Kommunikationstyp aus.");

            RuleFor(x => x.PersonCommunicationValue)
                .NotEmpty()
                .WithMessage("Bitte geben Sie einen Kommunikationswert ein.");
        }
    }
}
