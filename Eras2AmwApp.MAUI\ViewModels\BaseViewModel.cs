//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="BaseViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//  Base ViewModel for MAUI
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using CommunityToolkit.Mvvm.ComponentModel;
using Eras2AmwApp.MAUI.Interfaces;
using System.Threading.Tasks;

namespace Eras2AmwApp.MAUI.ViewModels
{
    /// <summary>
    /// Base view model that implements INotifyPropertyChanged using CommunityToolkit.Mvvm
    /// </summary>
    public class BaseViewModel : ObservableObject
    {
        private bool _isBusy;
        private string _title = string.Empty;

        protected readonly IEcNavigationService navigationService;

        public BaseViewModel()
        {
        }

        public BaseViewModel(IEcNavigationService navigationService)
        {
            this.navigationService = navigationService;
        }

        /// <summary>
        /// Gets or sets a value indicating whether the view model is busy
        /// </summary>
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value);
        }

        /// <summary>
        /// Gets or sets the title of the view model
        /// </summary>
        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        public virtual Task SetupAsync(object navigationData)
        {
            return Task.CompletedTask;
        }
    }
}
