using CommunityToolkit.Mvvm.ComponentModel;

namespace Eras2AmwApp.MAUI.ViewModels
{
    /// <summary>
    /// Base view model that implements INotifyPropertyChanged using CommunityToolkit.Mvvm
    /// </summary>
    public class BaseViewModel : ObservableObject
    {
        private bool _isBusy;
        private string _title = string.Empty;

        /// <summary>
        /// Gets or sets a value indicating whether the view model is busy
        /// </summary>
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value);
        }

        /// <summary>
        /// Gets or sets the title of the view model
        /// </summary>
        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }
    }
}
