﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="CreateNutzeinheitPageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.ViewModels
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Linq;
    using System.Threading.Tasks;
    using CommunityToolkit.Mvvm.ComponentModel;
    using CommunityToolkit.Mvvm.Input;
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Eras2AmwApp.MAUI.Interfaces;
    using Eras2AmwApp.MAUI.Models;
    using Eras2AmwApp.MAUI.Validators;
    using Eras2AmwApp.WebService.EventArgs;
    using Eras2AmwApp.WebService.Interfaces;
    using FluentValidation.Results;
    using domain = Domain.Eras2Amw.Models;
    using INutzeinheitService = BusinessLogic.Interfaces.INutzeinheitService;

    public partial class CreateNutzeinheitPageViewModel : EcViewModelBase, ISupportParentViewModel
    {
        #region fields

        private readonly INutzeinheitService nutzeinheitService;
        private readonly IAddressService addressService;
        private readonly IPersonService personService;

        [ObservableProperty]
        private EcViewModelBase? _parentViewModel;

        [ObservableProperty]
        private string? _nutzeinheitNummer;

        [ObservableProperty]
        private string? _nutzeinheitLage;

        [ObservableProperty]
        private string? _nutzeinheitWalkSequence;

        [ObservableProperty]
        private string? _nutzeinheitNote;

        [ObservableProperty]
        private string? _addressStreet;

        [ObservableProperty]
        private string? _addressStreet2;

        [ObservableProperty]
        private string? _addressStreetNumber;

        [ObservableProperty]
        private string? _addressZipCode;

        [ObservableProperty]
        private string? _addressCity;

        [ObservableProperty]
        private string? _addressMailbox;

        [ObservableProperty]
        private string? _addressAdditional;

        [ObservableProperty]
        private string? _addressLatitude;

        [ObservableProperty]
        private string? _addressLongitude;

        [ObservableProperty]
        private string? _nutzeinheitNumberErrorText;

        [ObservableProperty]
        private string? _nutzeinheitLocationErrorText;

        [ObservableProperty]
        private bool _nutzeinheitNumberHasError;

        [ObservableProperty]
        private bool _nutzeinheitLocationHasError;

        private Abrechnungseinheit? abrechnungseinheit;
        private Appointment? appointment;

        #endregion

        #region ctor

        public CreateNutzeinheitPageViewModel(
            IServiceLocator serviceLocator,
            IEcNavigationService navigationService,
            INutzeinheitService nutzeinheitService,
            IAddressService addressService,
            IPersonService personService)
        : base(serviceLocator, navigationService)
        {
            this.nutzeinheitService = nutzeinheitService ?? throw new ArgumentNullException(nameof(nutzeinheitService));
            this.addressService = addressService ?? throw new ArgumentNullException(nameof(addressService));
            this.personService = personService ?? throw new ArgumentNullException(nameof(personService));
        }

        #endregion

        #region Commands

        [RelayCommand]
        private async Task SaveNutzeinheit()
        {
            try
            {
                if (!Validate())
                {
                    return;
                }

                Nutzeinheit nutzeinheit = CreateNutzeinheit();
                nutzeinheit.AppointmentNutzeinheiten = new List<AppointmentNutzeinheit>
                {
                    CreateAppointmentNutzeinheit(nutzeinheit)
                };

                nutzeinheitService.SaveNutzeinheit(nutzeinheit);
                NutzeinheitOrderState nutzeinheitOrderState = nutzeinheit.OrderStates.Single();

                //update previous model list
                AppointmentPageViewModel? previousViewModel = GetParentProperty();
                if (previousViewModel != null && ParentViewModel is OrderPageViewModel orderViewModel)
                {
                    ObservableCollection<NutzeinheitVM> appointmentNutzeinheiten = orderViewModel.AppointmentNutzeinheiten;
                    NutzeinheitVM newNutzeinheiten = orderViewModel.CreateNutzeinheitVmObject(nutzeinheitService.GetNutzeinheit(nutzeinheit.Guid), appointment!);
                    appointmentNutzeinheiten.Add(newNutzeinheiten);
                }

                await navigationService.GoBackAsync();
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while trying to save new Nutzeinheit!");
                throw;
            }
        }

        #endregion

        #region properties

        public Guid AbrechnungseinheitGuid { get; set; }

        #endregion

        #region public methods

        public AppointmentPageViewModel GetParentProperty()
        {
            return ParentViewModel as AppointmentPageViewModel;
        }

        public override Task SetupAsync(object navigationData)
        {
            if (navigationData is OrderPageViewModel parentVm)
            {
                ParentViewModel = parentVm;
                appointment = parentVm.SelectedAppointment;
                abrechnungseinheit = parentVm.SelectedAppointment.Order.Abrechnungseinheit;
                AbrechnungseinheitGuid = abrechnungseinheit.Guid;
                InitializeProperties();
                InitNutzeinheitNumberPlaceholder();
            }
            return base.SetupAsync(navigationData);
        }

        #endregion

        #region private methods

        private void InitializeProperties()
        {
            try
            {
                Address address = abrechnungseinheit.Address;
                AddressStreet = address.Street;
                AddressStreet2 = address.Street2;
                AddressStreetNumber = address.StreetNumber;
                AddressZipCode = address.Zipcode;
                AddressCity = address.City;
                AddressMailbox = address.Mailbox;
                AddressAdditional = address.Additional;
                AddressLatitude = address.Latitude;
                AddressLongitude = address.Longitude;
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while initializing properties in CreateNutzeinheitPageVM!");
                throw;
            }
        }

        private void InitNutzeinheitNumberPlaceholder()
        {
            try
            {
                NutzeinheitNummer = nutzeinheitService.GetPlaceholderNutzeinheitNumber(AbrechnungseinheitGuid);
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while attempting to initialize placeholder value for new Nutzeinheit Number!");
                throw;
            }
        }



        private Guid GetCreateAddressGuid()
        {
            Address address = new Address
            {
                Street = AddressStreet ?? string.Empty,
                Street2 = AddressStreet2 ?? string.Empty,
                StreetNumber = AddressStreetNumber ?? string.Empty,
                Zipcode = AddressZipCode ?? string.Empty,
                City = AddressCity ?? string.Empty,
                Mailbox = AddressMailbox ?? string.Empty,
                Additional = AddressAdditional ?? string.Empty,
                Latitude = AddressLatitude ?? string.Empty,
                Longitude = AddressLongitude ?? string.Empty,
            };
            return addressService.AddNutzeinheitAddress(address);
        }

        private Nutzeinheit CreateNutzeinheit()
        {
            return new Nutzeinheit
            {
                Number = NutzeinheitNummer ?? string.Empty,
                Location = NutzeinheitLage ?? string.Empty,
                WalkSequence = NutzeinheitWalkSequence ?? string.Empty,
                Note = NutzeinheitNote ?? string.Empty,
                AbrechnungseinheitGuid = abrechnungseinheit!.Guid,
                AddressGuid = GetCreateAddressGuid(),
                Nutzer = new List<Nutzer>
                {
                    CreateLeerstandNutzerToNutzeinheit()
                },
                Devices = new List<domain.Device>(),
                Photos = new List<Photo>(),
                Signatures = new List<Signature>(),
                NutzeinheitOrderPositions = new List<NutzeinheitOrderPosition>
                {
                    new NutzeinheitOrderPosition
                    {
                        OrderPositionGuid = appointment!.Order.OrderPositions.First().Guid
                    }
                },
                OrderStates = new List<NutzeinheitOrderState>
                {
                    CreateNutzeinheitOrderState()
                },
                IsCreatedByApp = true
            };
        }

        private AppointmentNutzeinheit CreateAppointmentNutzeinheit(Nutzeinheit nutzeinheit)
        {
            var appointmentNutzeinheit = new AppointmentNutzeinheit
            {
                AppointmentGuid = appointment!.Guid,
                NutzeinheitGuid = nutzeinheit.Guid
            };

            return appointmentNutzeinheit;
        }

        private Nutzer CreateLeerstandNutzerToNutzeinheit()
        {
            var defaultTime = default(DateTime);
            return new Nutzer()
            {
                Kind = NutzerKind.Leerstand,
                MoveInDate = defaultTime,
                TitleId = null,
                SalutationId = 6,
                Name1 = "Leerstand",
                NutzerCommunications = new List<NutzerCommunication>()
                {
                     CreateNutzerCommunication()
                }
            };
        }

        private NutzerCommunication CreateNutzerCommunication()
        {
            CommunicationFeature communicationFeature = personService.GetCommunicationFeature(CommunicationKind.Mobil);
            return new NutzerCommunication()
            {
                Address = string.Empty,
                Note = string.Empty,
                CommunicationFeatureGuid = communicationFeature.Guid
            };
        }

        private NutzeinheitOrderState CreateNutzeinheitOrderState()
        {
            return new NutzeinheitOrderState
            {
                OrderGuid = appointment!.Order.Guid,
                OrderKinds = new List<NutzeinheitOrderKind>
                {
                    NutzeinheitOrderKind.Assembly
                },
                ProcessState = ProcessState.Creating
            };
        }

        private bool Validate()
        {
            var validator = new CreateNutzeinheitPageViewModelValidator();
            ValidationResult result = validator.Validate(this);

            UpdateErrorMessages(result);

            return result.IsValid;
        }
    }
    #endregion

}
