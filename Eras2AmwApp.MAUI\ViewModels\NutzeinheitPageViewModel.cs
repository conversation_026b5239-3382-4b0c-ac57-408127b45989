﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitPageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

using Eras2AmwApp.BusinessLogic.Interfaces;
using Eras2AmwApp.BusinessLogic.Models;
using Eras2AmwApp.Common.Interfaces;
using Eras2AmwApp.Domain.Eras2Amw.Models;
using Eras2AmwApp.MAUI.Interfaces;
using Eras2AmwApp.MAUI.Enums;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Maui.Controls;
using Microsoft.Maui.ApplicationModel;
using Eras2AmwApp.BusinessLogic.Factory;
using Eras2AmwApp.Domain.Eras2Amw.Extensions;
using Eras2AmwApp.MAUI.Services;
using Device = Eras2AmwApp.Domain.Eras2Amw.Models.Device;
using Eras2AmwApp.Domain.Eras2Amw.Enums;
using Syncfusion.Maui.Popup;
using Eras2AmwApp.MAUI.Models;
using Syncfusion.Maui.Core;
using Eras2AmwApp.WebService.EventArgs;
using Eras2AmwApp.WebService.Interfaces;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using INutzeinheitService = Eras2AmwApp.BusinessLogic.Interfaces.INutzeinheitService;
using IOrderService = Eras2AmwApp.BusinessLogic.Interfaces.IOrderService;
using Microsoft.Extensions.Logging;

// Resolve ambiguous references
using BusinessLogicDeviceUiState = Eras2AmwApp.BusinessLogic.Models.DeviceUiState;

namespace Eras2AmwApp.MAUI.ViewModels
{
    public partial class NutzeinheitPageViewModel : EcViewModelBase
    {
        #region fields

        private readonly INutzeinheitService nutzeinheitService;
        private readonly INutzerService nutzerService;
        private readonly ISignatureService signatureService;
        private readonly IOrderService orderService;
        private readonly IEcDialogService dialogService;
        private readonly ILoginService loginService;
        private readonly IAmwWebservice webservice;
        private readonly IBackupService backupService;
        private readonly IDeviceService deviceService;
        private readonly IDeviceMaintenanceDialog deviceMaintenanceDialog;
        private readonly INewAppointmentDialog newAppointmentDialog;
        private readonly INutzeinheitSignatureDialog nutzeinheitSignatureDialog;

        [ObservableProperty]
        private bool areAllDevicesMaintained;

        [ObservableProperty]
        private NutzeinheitUiState? nutzeinheitUiState;

        [ObservableProperty]
        private string? nutzerNameLocation;

        [ObservableProperty]
        private string? nutzerAddressContact;

        [ObservableProperty]
        private string? nutzeinheitNumber;

        [ObservableProperty]
        private bool hasNutzeinheitNote;

        [ObservableProperty]
        private DeviceVM? selectedDevice;

        [ObservableProperty]
        private int photoCount;

        [ObservableProperty]
        private BadgeType badgeSetting;

        private bool _isSignatureWarningShown;

        [ObservableProperty]
        private string? currentDeviceCount;

        [ObservableProperty]
        private string? allDeviceCountInto;

        [ObservableProperty]
        private string? deviceCountInfo;

        [ObservableProperty]
        private string? currentDeviceCountInfo;

        [ObservableProperty]
        private bool toggleState;

        [ObservableProperty]
        private bool isMaintainAllVisible;

        [ObservableProperty]
        private bool isLiveSyncOn;

        [ObservableProperty]
        private bool isSetupDone;

        #endregion

        #region ctor

        public NutzeinheitPageViewModel(
            IServiceLocator serviceLocator,
            IEcNavigationService navigationService,
            INutzeinheitService nutzeinheitService,
            INutzerService nutzerService,
            ISignatureService signatureService,
            IDeviceService deviceService,
            IOrderService orderService,
            IEcDialogService dialogService,
            IAmwWebservice webservice,
            IBackupService backupService,
            IDeviceMaintenanceDialog deviceMaintenanceDialog,
            INewAppointmentDialog newAppointmentDialog,
            INutzeinheitSignatureDialog nutzeinheitSignatureDialog,
            ILoginService loginService)
           : base(serviceLocator, navigationService)
        {
            this.nutzeinheitService = nutzeinheitService ?? throw new ArgumentNullException(nameof(nutzeinheitService));
            this.nutzerService = nutzerService ?? throw new ArgumentNullException(nameof(nutzerService));
            this.signatureService = signatureService ?? throw new ArgumentNullException(nameof(signatureService));
            this.deviceService = deviceService ?? throw new ArgumentNullException(nameof(deviceService));
            this.orderService = orderService ?? throw new ArgumentNullException(nameof(orderService));
            this.dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            this.webservice = webservice ?? throw new ArgumentNullException(nameof(webservice));
            this.backupService = backupService ?? throw new ArgumentNullException(nameof(backupService));
            this.deviceMaintenanceDialog = deviceMaintenanceDialog ?? throw new ArgumentNullException(nameof(deviceMaintenanceDialog));
            this.newAppointmentDialog = newAppointmentDialog ?? throw new ArgumentNullException(nameof(newAppointmentDialog));
            this.nutzeinheitSignatureDialog = nutzeinheitSignatureDialog ?? throw new ArgumentNullException(nameof(nutzeinheitSignatureDialog));
            this.loginService = loginService ?? throw new ArgumentNullException(nameof(loginService));

            ListOfAllDevices = new ObservableCollection<DeviceVM>();
            ListOfCurrentDevices = new ObservableCollection<DeviceVM>();
            ListOfRemainingDevices = new ObservableCollection<DeviceVM>();

            IsSignatureWarningShown = false;
            PhotoCount = 0;
        }

        #endregion

        #region commands

        [RelayCommand]
        private async Task Appearing() => AppearingExecute();

        [RelayCommand]
        private void Disappearing() => DisappearingExecute();

        [RelayCommand]
        private async Task AddDevice() => await AddDeviceExecuteAsync();

        private async Task AddDeviceExecuteAsync()
        {
            try
            {
                if (SelectedNutzeinheitVM == null)
                {
                    throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
                }

                await navigationService.NavigateToAsync<AddDevicePageViewModel>(SelectedNutzeinheitVM);
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occoured while attempting to navigate to AddNewDevicePage!");
                throw;
            }
        }

        [RelayCommand]
        private async Task SaveSignature() => await SaveSignatureExecute();

        private async Task SaveSignatureExecute()
        {
            try
            {
                if (SelectedNutzeinheitVM == null)
                {
                    throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
                }

                if (!await CheckIfOverwriteSignature())
                {
                    return;
                }

                if (!await CheckIfNeAmwInfoKeyExist())
                {
                    return;
                }

                // Update Current State of NutzeinheitUiState in this View
                UpdateNutzeinheitState();
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to save the signature!");
                throw;
            }
        }

        [RelayCommand]
        private async Task NutzeinheitIconButton() => await Task.Run(() => NutzeinheitIconButtonExecute());

        [RelayCommand]
        private async Task PhotoPreview() => await Task.Run(() => PhotoPreviewExecute());

        [RelayCommand]
        private async Task NutzerChange() => await Task.Run(() => NutzerChangeExecute());

        [RelayCommand]
        private async Task NutzerEdit() => await Task.Run(() => NutzerEditExecute());

        [RelayCommand]
        private async Task Summary() => await Task.Run(() => SummaryExecute());

        [RelayCommand]
        private async Task InfoKey(DeviceVM deviceVM) => await Task.Run(() => InfoKeyExecute(deviceVM));

        [RelayCommand]
        private async Task ConfirmNutzeinheit() => await Task.Run(() => ConfirmNutzeinheitExecute());

        [RelayCommand]
        private async Task NewAppointment() => await Task.Run(() => NewAppointmentExecute());

        [RelayCommand]
        private void ToggleSwitch(bool isToggled)
        {
            try
            {
                if (isToggled)
                {
                    DeviceCountInfo = AllDeviceCountInto;
                }
                else
                {
                    DeviceCountInfo = CurrentDeviceCount;
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to refresh listview content information!");
                throw;
            }
        }

        [RelayCommand]
        private async Task SignatureDialog() => await Task.Run(() => SignatureDialogExecute());

        [RelayCommand]
        private void TabChanged(object selectedTab)
        {
            if (selectedTab == null)
            {
                return;
            }
        }

        #endregion

        #region properties

        public ObservableCollection<DeviceVM> ListOfAllDevices { get; set; } = new();

        public ObservableCollection<DeviceVM> ListOfCurrentDevices { get; set; } = new();

        public ObservableCollection<DeviceVM> ListOfRemainingDevices { get; set; } = new();

        // If you need SfPopupLayout, ensure the NuGet package Syncfusion.Maui.Popup is installed and add:
        // using Syncfusion.Maui.Popup;
        // Otherwise, comment out or remove the following line if not needed:
        // public Syncfusion.Maui.Popup.SfPopupLayout PopupMenu { get; set; } = new();

        public NutzeinheitVM? SelectedNutzeinheitVM { get; set; }

        public bool IsSignatureWarningShown
        {
            get => _isSignatureWarningShown;
            set => SetProperty(ref _isSignatureWarningShown, value);
        }

        #endregion

        #region public methods

        public override Task SetupAsync(object navigationData)
        {
            if (navigationData is NutzeinheitVM nutzeinheitVM)
            {
                SelectedNutzeinheitVM = nutzeinheitVM;
                SelectedNutzeinheitVM.ParentViewModel = this;
                IsLiveSyncOn = loginService.GetUserLiveSyncState();
                InitilizeProperties();
            }

            return base.SetupAsync(navigationData);
        }

        #endregion

        #region private methods

        private void InitilizeProperties()
        {
            try
            {
                if (SelectedNutzeinheitVM == null)
                {
                    throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
                }

                Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheit(SelectedNutzeinheitVM.NutzeinheitGuid);
                AssignNutzer(nutzeinheit);

                List<DeviceVM> listOfAllDeviceVM = new List<DeviceVM>();
                List<DeviceVM> listOfCurrentDeviceVM = new List<DeviceVM>();
                List<DeviceVM> listOfRemainingDeviceVM = new List<DeviceVM>();

                foreach (Device device in nutzeinheit.Devices)
                {
                    AssignDevice(device, listOfAllDeviceVM, listOfCurrentDeviceVM, listOfRemainingDeviceVM);
                }

                OrderAndFillTheAllDeviceCollection(listOfAllDeviceVM);
                OrderAndFillTheCurrentDeviceCollection(listOfCurrentDeviceVM);
                OrderAndFillTheRemainingDeviceCollection(listOfRemainingDeviceVM);

                UpdateMaintainAllButtonState();
                UpdatePhotoCount(nutzeinheit);
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while initializing properties for NutzeinheitEditPageVM!");
                throw;
            }
        }

        private void AssignNutzer(Nutzeinheit nutzeinheit)
        {
            Nutzer nutzer = nutzerService.GetCurrentNutzer(nutzeinheit);
            string nutzerSalutationAndName = nutzerService.GetNutzerTitleAndSalutation(nutzer) + nutzerService.GetNutzerName(nutzer);
            string nutzeinheitLocation = nutzeinheit.Location + " " + nutzeinheit.WalkSequence;
            string nutzerAddress = nutzerService.GetNutzerAddress(nutzeinheit);
            string nutzerContact = nutzerService.GetNutzerContactValue(nutzeinheit);

            bool hasNote = false;

            if (!string.IsNullOrEmpty(nutzeinheit.Note))
            {
                hasNote = true;
            }

            HasNutzeinheitNote = hasNote;
            NutzeinheitUiState = SelectedNutzeinheitVM.NutzeinheitUiState;
            NutzerNameLocation = string.Format("{0}, {1}", nutzerSalutationAndName, nutzeinheitLocation);
            NutzerAddressContact = nutzerAddress + "\n" + string.Format("Kontakt: {0}", nutzerContact);
            NutzeinheitNumber = nutzeinheit.Number;
        }

        private void AssignDevice(Device device, List<DeviceVM> listOfAllDeviceVM, List<DeviceVM> listOfCurrentDeviceVM, List<DeviceVM> listOfRemainingDeviceVM)
        {
            if (SelectedNutzeinheitVM == null)
            {
                throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
            }

            NutzeinheitOrder nutzeinheitOrder = SelectedNutzeinheitVM.NutzeinheitOrder;
            IEnumerable<DeviceOrderState> deviceOrderStates = device.OrderStates;

            DeviceOrderState orderState = deviceOrderStates.SingleOrDefault(x => x.OrderGuid == nutzeinheitOrder.OrderGuid);
            if (orderState == null)
            {
                Order mainOrder = orderService.GetOrder(nutzeinheitOrder.OrderGuid);
                Order backupOrder = orderService.GetBackupOrderFromMain(mainOrder);
                orderState = deviceOrderStates.Single(x => x.OrderGuid == backupOrder.Guid);
            }

            IDevice iDevice = DeviceFactory.Create(device, orderState);
            DeviceClass deviceClass = device.DeviceCatalog.DeviceKind.Class;

            DeviceVM deviceVM = new DeviceVM
            {
                DeviceClass = deviceClass,
                DeviceNumber = iDevice.DeviceNumber,
                DeviceLabel = iDevice.DeviceLabel,
                DeviceRoom = iDevice.DeviceRoom.Label,
                DeviceOngoingNumber = iDevice.DeviceOngoingNumber,
                ParentViewModel = this,
                Device = iDevice,
                Photos = iDevice.Photos.ToList(),
                DeviceUiState = new DeviceUiState
                {
                    DeviceOrderState = iDevice.DeviceOrderState,
                    IsMaintained = device.IsMaintained
                },
                IsDeviceListEditedOrCreated = false,
                IsDeviceMaintained = iDevice.IsDeviceMaintained
            };

            if (orderState.OrderGuid == nutzeinheitOrder.OrderGuid)
            {
                var deviceDeinstallationDate = deviceVM.Device?.DeviceDeinstallationDate;
                if (deviceVM.Device?.DeviceDeinstallationDate == null)
                {
                    listOfCurrentDeviceVM.Add(deviceVM);
                }
                listOfAllDeviceVM.Add(deviceVM);
            }
            else
            {
                listOfRemainingDeviceVM.Add(deviceVM);
            }
        }

        private void OrderAndFillTheAllDeviceCollection(List<DeviceVM> listOfDevices)
        {
            listOfDevices = listOfDevices.OrderBy(x => Convert.ToInt32(x.Device?.DeviceOngoingNumber ?? "0")).ToList();
            foreach (DeviceVM deviceVM in listOfDevices)
            {
                ListOfAllDevices.Add(deviceVM);
            }
            AllDeviceCountInto = $"Es sind: {ListOfAllDevices.Count()} Geräte in diesem Auftrag vorhanden";
        }

        private void OrderAndFillTheCurrentDeviceCollection(List<DeviceVM> listOfDevices)
        {
            listOfDevices = listOfDevices.OrderBy(x => Convert.ToInt32(x.Device?.DeviceOngoingNumber ?? "0")).ToList();
            foreach (DeviceVM deviceVM in listOfDevices)
            {
                ListOfCurrentDevices.Add(deviceVM);
            }

            string currentDeviceCountInfo = $"Es sind: {ListOfCurrentDevices.Count()} Geräte in diesem Auftrag vorhanden";
            DeviceCountInfo = currentDeviceCountInfo;
        }

        private void OrderAndFillTheRemainingDeviceCollection(List<DeviceVM> listOfDevices)
        {
            if (!listOfDevices.Any())
            {
                return;
            }

            listOfDevices = listOfDevices.OrderBy(x => Convert.ToInt32(x.Device?.DeviceOngoingNumber ?? "0")).ToList();
            foreach (DeviceVM deviceVM in listOfDevices)
            {
                ListOfRemainingDevices.Add(deviceVM);
            }
        }

        public async Task<bool> CheckIfOverwriteSignature()
        {
            if (SelectedNutzeinheitVM == null)
            {
                throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
            }

            bool result = true;
            DialogResponse signatureDeleteWarningResult = DialogResponse.Accept;
            bool nutzeinheitHasSignature = NutzeinheitUiState?.HasSignature ?? false;

            if (nutzeinheitHasSignature)
            {
                signatureDeleteWarningResult = await DisplayDeleteSignatureWarning();
            }

            if (signatureDeleteWarningResult == DialogResponse.Decline)
            {
                result = false;
            }
            else
            {
                NutzeinheitOrderState nutzeinheitOrderState = nutzeinheitService.GetNutzeinheitOrderState(SelectedNutzeinheitVM.NutzeinheitOrder);
                ProcessState nutzeinheitProcessState = nutzeinheitOrderState.ProcessState;

                Guid orderGuid = SelectedNutzeinheitVM.NutzeinheitOrder.OrderGuid;
                List<DeviceOrderState> deviceOrderStates = ListOfAllDevices.Select(x => x.DeviceUiState.DeviceOrderState).Where(x => x.OrderGuid == orderGuid).ToList();

                NutzeinheitUiState = new NutzeinheitUiState()
                {
                    AmwInfoKeyGuid = nutzeinheitOrderState.AmwInfoKeyGuid,
                    HasSignature = false,
                    OrderStates = deviceOrderStates,
                    NutzeinheitProcessState = nutzeinheitProcessState
                };
            }
            return result;
        }

        public DeviceUiState MaintainExecute(IDevice selectedDevice)
        {
            try
            {
                if (SelectedNutzeinheitVM == null)
                {
                    throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
                }

                Device device = deviceService.GetDevice(selectedDevice.DeviceGuid);
                DeviceUiState deviceUiState = UpdateDeviceAndDeviceOrderState(device, selectedDevice);
                UpdateMaintainAllButtonState();
                UpdateNutzeinheitState();

                //for backup order update
                if (SelectedNutzeinheitVM.NutzeinheitOrder.OrderGuid != selectedDevice.OrderGuid)
                {
                    Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheit(device.NutzeinheitGuid);
                    NutzeinheitOrderState nutzeinheitOrderState = nutzeinheit.OrderStates.Single(x => x.OrderGuid == deviceUiState.DeviceOrderState.OrderGuid);
                    nutzeinheitOrderState.ProcessState = ProcessState.Updating;

                    List<DeviceOrderState> backupOrderDeviceOrderStates = nutzeinheit.Devices.SelectMany(x => x.OrderStates).Where(y => y.OrderGuid == deviceUiState.DeviceOrderState.OrderGuid).ToList();

                    if (!backupOrderDeviceOrderStates.Any(x => x.ProcessState == ProcessState.InProgress))
                    {
                        nutzeinheitOrderState.CompletedDate = DateTime.Now;
                    }
                    nutzeinheitService.UpdateNutzeinheitOrderState(nutzeinheitOrderState);

                    Order order = orderService.GetOrder(deviceUiState.DeviceOrderState.OrderGuid);
                    OrderState orderState = order.OrderState;
                    orderState.ProcessState = ProcessState.Updating;
                    orderService.UpdateOrderState(orderState);
                }
                return deviceUiState;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while trying to maintain/inspect a device!");
                throw;
            }
        }

        private DeviceUiState UpdateDeviceAndDeviceOrderState(Device device, IDevice selectedDevice)
        {
            if (SelectedNutzeinheitVM == null)
            {
                throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
            }

            Guid orderGuid = SelectedNutzeinheitVM.NutzeinheitOrder.OrderGuid;
            DeviceOrderState deviceOrderState = device.OrderStates.Single();

            selectedDevice.IsDeviceMaintained = !selectedDevice.IsDeviceMaintained;

            if (deviceOrderState.ProcessState != ProcessState.Creating)
            {
                deviceOrderState.ProcessState = ProcessState.Updating;
                deviceOrderState.CompletedDate = DateTime.Now;
                deviceOrderState.AmwInfoKeyGuid = null;
                deviceOrderState.AmwInfoKey = null;
            }

            if (!selectedDevice.IsDeviceMaintained)
            {
                if (deviceOrderState.ProcessState != ProcessState.Creating)
                {
                    deviceOrderState.ProcessState = ProcessState.InProgress;
                    deviceOrderState.CompletedDate = null;
                }
            }
            else
            {
                deviceOrderState.AmwInfoKeyGuid = null;
                deviceOrderState.AmwInfoKey = null;
            }

            if (device.IsMaintained != selectedDevice.IsDeviceMaintained)
            {
                device.IsMaintained = selectedDevice.IsDeviceMaintained;
                deviceService.UpdateDevice(device);
            }

            deviceService.UpdateDeviceOrderState(deviceOrderState);

            DeviceVM deviceVM = ListOfAllDevices.Where(x => x.Device?.DeviceGuid == device.Guid).SingleOrDefault();

            if (deviceVM == null)
            {
                deviceVM = ListOfRemainingDevices.Where(x => x.Device?.DeviceGuid == device.Guid).SingleOrDefault();
            }

            DeviceUiState deviceUiState = new DeviceUiState
            {
                DeviceOrderState = deviceOrderState,
                IsMaintained = device.IsMaintained
            };

            deviceVM.DeviceUiState = deviceUiState;

            return deviceUiState;
        }

        private void SetNutzeinheitStateUpdating(NutzeinheitOrderState nutzeinheitOrderState)
        {
            nutzeinheitOrderState.CompletedDate = DateTime.Now;
            nutzeinheitOrderState.ProcessState = ProcessState.Updating;
            nutzeinheitService.UpdateNutzeinheitOrderState(nutzeinheitOrderState);
        }

        public void SetNutzeinheitStateInProgress()
        {
            if (SelectedNutzeinheitVM == null)
            {
                throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
            }

            NutzeinheitOrder nutzeinheitOrder = SelectedNutzeinheitVM.NutzeinheitOrder;
            NutzeinheitOrderState nutzeinheitOrderState = nutzeinheitService.GetNutzeinheitOrderState(nutzeinheitOrder);

            if (nutzeinheitOrderState.ProcessState == ProcessState.InProgress || nutzeinheitOrderState.ProcessState == ProcessState.Creating)
            {
                return;
            }

            nutzeinheitOrderState.ProcessState = ProcessState.InProgress;

            IEnumerable<DeviceVM> unfinishedDevices = ListOfCurrentDevices.Where(x => x.IsDeviceMaintained == false);
            foreach (DeviceVM deviceVM in unfinishedDevices)
            {
                NutzeinheitPageViewModel parentVM = (NutzeinheitPageViewModel)deviceVM.ParentViewModel;
                parentVM.SelectedNutzeinheitVM.NutzeinheitUiState = new NutzeinheitUiState()
                {
                    NutzeinheitProcessState = ProcessState.InProgress,
                    HasSignature = false,
                    OrderStates = SelectedNutzeinheitVM.NutzeinheitUiState.OrderStates,
                    AmwInfoKeyGuid = SelectedNutzeinheitVM.NutzeinheitUiState.AmwInfoKeyGuid
                };
            }
            nutzeinheitService.UpdateNutzeinheitOrderState(nutzeinheitOrderState);
        }

        private void UpdateOrderState()
        {
            if (SelectedNutzeinheitVM == null)
            {
                throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
            }

            NutzeinheitOrder nutzeinheitOrder = SelectedNutzeinheitVM.NutzeinheitOrder;
            Order order = orderService.GetOrder(nutzeinheitOrder.OrderGuid);
            OrderState orderState = order.OrderState;

            orderState.ProcessState = ProcessState.Updating;
            orderService.UpdateOrderState(orderState);
        }

        private async void AddDeviceExecute()
        {
            try
            {
                if (SelectedNutzeinheitVM == null)
                {
                    throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
                }

                await navigationService.NavigateToAsync<AddDevicePageViewModel>(SelectedNutzeinheitVM);
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occoured while attempting to navigate to AddNewDevicePage!");
                throw;
            }
        }

        private bool AreAllDeviceMaintenanceStatus()
        {
            if (SelectedNutzeinheitVM == null)
            {
                throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
            }

            Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheit(SelectedNutzeinheitVM.NutzeinheitGuid);
            NutzeinheitOrder nutzeinheitOrder = SelectedNutzeinheitVM.NutzeinheitOrder;

            List<DeviceOrderState> NutzeinheitDevices = nutzeinheit.Devices.SelectMany(x => x.OrderStates).Where(x => x.OrderGuid == nutzeinheitOrder.OrderGuid).ToList();

            int closedDeviceCounter = 0;
            foreach (DeviceOrderState deviceOrderState in NutzeinheitDevices)
            {
                if (deviceOrderState.IsClosed())
                {
                    closedDeviceCounter++;
                }
            }

            return closedDeviceCounter == NutzeinheitDevices.Count;
        }

        private bool HasAnyDeviceBeenChanged()
        {
            if (SelectedNutzeinheitVM == null)
            {
                throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
            }

            Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheit(SelectedNutzeinheitVM.NutzeinheitGuid);
            NutzeinheitOrder nutzeinheitOrder = SelectedNutzeinheitVM.NutzeinheitOrder;

            List<DeviceOrderState> NutzeinheitDevices = nutzeinheit.Devices.SelectMany(x => x.OrderStates).Where(x => x.OrderGuid == nutzeinheitOrder.OrderGuid).ToList();

            foreach (DeviceOrderState deviceOrderState in NutzeinheitDevices)
            {
                if (deviceOrderState.IsClosed())
                {
                    return true;
                }
            }

            return false;
        }

        private bool HasBeenSigned()
        {
            if (SelectedNutzeinheitVM == null)
            {
                throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
            }

            NutzeinheitOrder nutzeinheitOrder = SelectedNutzeinheitVM.NutzeinheitOrder;
            Signature signature = signatureService.GetSignatureForOrder(nutzeinheitOrder.NutzeinheitGuid, nutzeinheitOrder.OrderGuid);

            return signature != null;
        }

        public async Task DisplayUnsavedDataWarning()
        {
            if (SelectedNutzeinheitVM == null)
            {
                throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
            }

            ProcessState nutzeinheitState = SelectedNutzeinheitVM.NutzeinheitUiState.NutzeinheitProcessState;

            if ((HasAnyDeviceBeenChanged() || HasBeenSigned()) && !(nutzeinheitState == ProcessState.Updating || nutzeinheitState == ProcessState.Completed || nutzeinheitState == ProcessState.Creating))
            {
                DialogResponse response = await dialogService.AcceptDeclineAsync(localisationService.Get("NutzeinheitLostWarning"), "JA", "NEIN");

                if (response == DialogResponse.Accept)
                {
                    await navigationService.GoBackAsync();
                }
            }
            else
            {
                await navigationService.GoBackAsync();
            }
        }

        private async void NutzeinheitIconButtonExecute()
        {
            try
            {
                if (SelectedNutzeinheitVM == null)
                {
                    throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
                }

                Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheit(SelectedNutzeinheitVM.NutzeinheitGuid);
                string nutzeinheitNote = nutzeinheit.Note;
                await dialogService.AcceptAsync(nutzeinheitNote, "OK", "Nutzeinheit Information");
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to execute the command assign to the orderInfo Icon!");
                throw;
            }
        }

        private void MaintainAllDevices()
        {
            try
            {
                IEnumerable<DeviceVM> uncheckedDevices = ListOfCurrentDevices.Where(x => x.IsDeviceMaintained == false &&
                (x.Device?.DeviceOrderState?.OrderKind == DeviceOrderKind.Inspection || x.Device?.DeviceOrderState?.OrderKind == DeviceOrderKind.Maintenance));
                foreach (DeviceVM deviceVM in uncheckedDevices)
                {
                    deviceVM.IsDeviceMaintained = true;
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to maintain all devices!");
                throw;
            }
        }

        private async Task<DialogResponse> DisplayDeleteSignatureWarning()
        {
            if (SelectedNutzeinheitVM == null)
            {
                throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
            }

            string warning = localisationService.Get("DeleteSignatureWarning");
            DialogResponse result = await dialogService.AcceptDeclineAsync(warning, "JA", "NEIN");

            if (result == DialogResponse.Decline)
            {
                return result;
            }

            DeleteSignature();
            SetNutzeinheitStateInProgress();

            return result;
        }

        private async void PhotoPreviewExecute()
        {
            try
            {
                await navigationService.NavigateToAsync<PicturePreviewPageViewModel>(this);
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to load PhotoPreviewPage!");
                throw;
            }
        }

        private async void NutzerChangeExecute()
        {
            try
            {
                if (SelectedNutzeinheitVM == null)
                {
                    throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
                }

                Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheit(SelectedNutzeinheitVM.NutzeinheitGuid);
                Nutzer appointmentNutzer = nutzerService.GetNutzerForDate(nutzeinheit, SelectedNutzeinheitVM.AppointmentDate);
                Nutzer currentNutzer = nutzerService.GetCurrentNutzer(nutzeinheit);

                if (currentNutzer.Guid != appointmentNutzer.Guid)
                {
                    string dialogWarning = localisationService.Get("FutureNutzerExists");
                    string dialogHeader = localisationService.Get("NutzerChangeImpossible");
                    await dialogService.AcceptAsync(dialogWarning, "OK", dialogHeader);
                }
                else
                {
                    //await navigationService.NavigateToAsync<NutzerwechselPageViewModel>(SelectedNutzeinheitVM);
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exxception occured while navigating to NutzerChangePage!");
            }
        }

        private async void NutzerEditExecute()
        {
            try
            {
                if (SelectedNutzeinheitVM == null)
                {
                    throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
                }

                //await navigationService.NavigateToAsync<NutzerEditPageViewModel>(SelectedNutzeinheitVM);
            }
            catch (Exception e)
            {
                logger.Error(e, "Problem occured while trying to navigate to Edit Appointment!");
                throw;
            }
        }

        private async void SummaryExecute()
        {
            try
            {
                if (SelectedNutzeinheitVM == null)
                {
                    throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
                }

                //await navigationService.NavigateToAsync<SummeryPageViewModel>(SelectedNutzeinheitVM);
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to navigate to Summary Page!");
                throw;
            }
        }

        private void DeleteSignature()
        {
            if (SelectedNutzeinheitVM == null)
            {
                throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
            }

            Signature oldSignature = signatureService.GetSignatureForOrder(SelectedNutzeinheitVM.NutzeinheitGuid, SelectedNutzeinheitVM.NutzeinheitOrder.OrderGuid);

            if (oldSignature != null)
            {
                if (oldSignature.Path != null)
                {
                    signatureService.RemoveSignatureFromDevice(oldSignature);
                }
                signatureService.DeleteSignature(oldSignature);
            }
        }

        private async void ConfirmNutzeinheitExecute()
        {
            try
            {
                if (SelectedNutzeinheitVM == null)
                {
                    throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
                }

                if (!AreAllDeviceMaintenanceStatus())
                {
                    await dialogService.AcceptAsync("Nicht alle Geräte wurden aktualisiert", "OK", "Warnung");
                    return;
                }

                Signature oldSignature = signatureService.GetSignatureForOrder(SelectedNutzeinheitVM.NutzeinheitGuid, SelectedNutzeinheitVM.NutzeinheitOrder.OrderGuid);

                if (oldSignature == null)
                {
                    IsSignatureWarningShown = true;
                    return;
                }
                else
                {
                    IsSignatureWarningShown = false;
                }

                NutzeinheitOrder nutzeinheitOrder = SelectedNutzeinheitVM.NutzeinheitOrder;
                NutzeinheitOrderState nutzeinheitOrderState = nutzeinheitService.GetNutzeinheitOrderState(nutzeinheitOrder);

                if (nutzeinheitOrderState.ProcessState != ProcessState.Creating)
                {
                    SetNutzeinheitStateUpdating(nutzeinheitOrderState);
                }
                else if (nutzeinheitOrderState.ProcessState == ProcessState.Creating)
                {
                    nutzeinheitOrderState.CompletedDate = DateTime.Now;
                    nutzeinheitService.UpdateNutzeinheitOrderState(nutzeinheitOrderState);
                }

                UpdateOrderState();

                if (loginService.GetUserLiveSyncState())
                {
                    await DoWebserviceSync(nutzeinheitOrderState);
                }

                await navigationService.GoBackAsync();
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occure while attempting to confirm Nutzeinheit final state!");
                throw;
            }
        }

        private async Task DoWebserviceSync(NutzeinheitOrderState nutzeinheitOrderState)
        {
            if (nutzeinheitOrderState == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheitOrderState));
            }

            try
            {
                if (Connectivity.NetworkAccess != NetworkAccess.Internet)
                {
                    return;
                }

                //Xamarin.Forms.Device.BeginInvokeOnMainThread(() => { dialogService.ShowBusyIndicator(AnimationTypes.Globe, "Der Nutzeinheit Status wird übertragen..."); });

                await backupService.DatabaseDackup();

                await webservice.SyncLiveNutzeinheitAsync();
            }
            catch (Exception exp)
            {
                logger.Error(exp, "SyncLiveAsync(): Exception.");
            }
            finally
            {
                dialogService.HideBusyIndicator();
            }
        }

        private async void NavigateToSelectedSelectedDevice(DeviceVM deviceVM)
        {
            try
            {
                if (SelectedNutzeinheitVM == null)
                {
                    throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
                }

                DeviceOrderKind deviceOrderKind = deviceVM.DeviceUiState.DeviceOrderState.OrderKind;
                ProcessState deviceOrderState = deviceVM.DeviceUiState.DeviceOrderState.ProcessState;

                if ((deviceVM.DeviceClass == DeviceClass.RM && deviceOrderKind != DeviceOrderKind.Exchange) ||
                   (deviceVM.DeviceClass == DeviceClass.RM && deviceOrderKind == DeviceOrderKind.Exchange && deviceVM.Device?.DeviceDeinstallationDate != null))
                {
                    //await navigationService.NavigateToAsync<RauchmelderEditPageViewModel>(this);
                }
                else if (deviceVM.DeviceClass == DeviceClass.RM && deviceOrderKind == DeviceOrderKind.Exchange)
                {
                    //await navigationService.NavigateToAsync<RauchmelderExchangePageViewModel>(this);
                }
                else if ((deviceVM.DeviceClass != DeviceClass.RM && deviceOrderKind != DeviceOrderKind.Exchange) ||
                      (deviceVM.DeviceClass != DeviceClass.RM && deviceOrderKind == DeviceOrderKind.Exchange && deviceVM.Device?.DeviceDeinstallationDate != null))
                {
                    //await navigationService.NavigateToAsync<WatermeterEditPageViewModel>(this);
                }
                else
                {
                    //await navigationService.NavigateToAsync<WatermeterExchangePageViewModel>(this);
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while trying to navigate to SelectedDevice!");
                throw;
            }
        }

        private async void InfoKeyExecute(DeviceVM deviceVM)
        {
            try
            {
                if (SelectedNutzeinheitVM == null)
                {
                    throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
                }

                if (!await CheckIfNeAmwInfoKeyExist())
                {
                    return;
                }

                if (!await CheckIfOverwriteSignature())
                {
                    return;
                }

                // Skip if Device is null
                if (deviceVM.Device is null)
                {
                    return;
                }

                // Since we've checked Device is not null, we can safely use it
                IDevice device = deviceVM.Device;
                DeviceOrderState deviceOrderState = await deviceMaintenanceDialog.ShowMaintenanceDialog(device);

                deviceService.UpdateDeviceOrderState(deviceOrderState);

                // Get the domain Device entity
                Device domainDevice = deviceService.GetDevice(device.DeviceGuid);

                if (deviceOrderState.AmwInfoKeyGuid != null)
                {
                    deviceVM.DeviceUiState = new DeviceUiState()
                    {
                        DeviceOrderState = deviceOrderState,
                        IsMaintained = false
                    };
                    deviceVM.IsDeviceMaintained = false;

                    // Update the domain device
                    domainDevice.IsMaintained = false;
                    deviceService.UpdateDevice(domainDevice);
                }
                else
                {
                    deviceVM.DeviceUiState = new DeviceUiState()
                    {
                        DeviceOrderState = deviceOrderState,
                        IsMaintained = deviceVM.DeviceUiState.IsMaintained
                    };
                }

                UpdateNutzeinheitState();
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while trying to select infokey for device!");
                throw;
            }
        }

        private async void AppearingExecute()
        {
            try
            {
                await RefreshView();

                RegisterWebserviceEventHandlers();
            }
            catch (Exception exception)
            {
                logger.Error(exception, "Exception occured while executing Appearing assignment method!");
                throw;
            }
        }

        private async Task RefreshView()
        {
            try
            {
                if (SelectedNutzeinheitVM == null)
                {
                    throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
                }

                Nutzeinheit nutzeinheit = nutzeinheitService.GetNutzeinheit(SelectedNutzeinheitVM.NutzeinheitGuid);
                //if (SelectedNutzeinheitVM.ParentViewModel is NutzerEditPageViewModel)
                //{
                //    SelectedNutzeinheitVM.ParentViewModel = this;
                //}
                if (nutzeinheit == null)
                {
                    await navigationService.GoBackAsync();
                    return;
                }

                CurrentDeviceCountInfo = $"Es sind: {ListOfCurrentDevices.Count()} Geräte in diesem Auftrag vorhanden";
                AllDeviceCountInto = $"Es sind: {ListOfAllDevices.Count()} Geräte in diesem Auftrag vorhanden";

                DeviceCountInfo = ToggleState ? AllDeviceCountInto : CurrentDeviceCountInfo;

                AssignNutzer(nutzeinheit);
                UpdateMaintainAllButtonState();
                UpdateNutzeinheitState();

                //update the photo count
                UpdatePhotoCount(nutzeinheit);
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to refresh NutzeinheitPageViewModel values!");
                throw;
            }
        }

        private void DisappearingExecute()
        {
            try
            {
                UnregisterWebserviceEventHandlers();
            }
            catch (Exception exception)
            {
                logger.Error(exception, "DisappearingExecute failed");
                throw;
            }
        }

        private void RegisterWebserviceEventHandlers()
        {
            webservice.Info += FileInfoLogging;
            webservice.Error += FileErrorLogging;
            webservice.Warning += FileWarningLogging;
        }

        private void FileErrorLogging(object sebder, WebserviceEventArgs arg)
        {
            logger.ForContext<IAmwWebservice>().Error(arg.Label);
        }

        private void FileWarningLogging(object sebder, WebserviceEventArgs arg)
        {
            logger.ForContext<IAmwWebservice>().Warning(arg.Label);
        }

        private void FileInfoLogging(object sebder, WebserviceEventArgs arg)
        {
            logger.ForContext<IAmwWebservice>().Information(arg.Label);
        }

        private void UnregisterWebserviceEventHandlers()
        {
            webservice.Info -= FileInfoLogging;
            webservice.Error -= FileErrorLogging;
            webservice.Warning -= FileWarningLogging;
        }

        private void UpdateMaintainAllButtonState()
        {
            //update MaintainAllButton
            int unmaintaintedCount = ListOfCurrentDevices.Where(x => x.IsDeviceMaintained &&
            (x.Device?.DeviceOrderState?.OrderKind == DeviceOrderKind.Inspection || x.Device?.DeviceOrderState?.OrderKind == DeviceOrderKind.Maintenance)).Count();

            int allmaintainableCount = ListOfCurrentDevices.Where(x => x.Device?.DeviceOrderState?.OrderKind == DeviceOrderKind.Inspection || x.Device?.DeviceOrderState?.OrderKind == DeviceOrderKind.Maintenance).Count();

            IsMaintainAllVisible = ListOfCurrentDevices.Where(x => x.Device?.DeviceOrderState?.OrderKind == DeviceOrderKind.Inspection || x.Device?.DeviceOrderState?.OrderKind == DeviceOrderKind.Maintenance).Any();

            if (unmaintaintedCount == allmaintainableCount)
            {
                AreAllDevicesMaintained = true;
            }
            else
            {
                AreAllDevicesMaintained = false;
            }
        }

        private void UpdateNutzeinheitState()
        {
            if (SelectedNutzeinheitVM == null)
            {
                throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
            }

            NutzeinheitOrderState nutzeinheitOrderState = nutzeinheitService.GetNutzeinheitOrderState(SelectedNutzeinheitVM.NutzeinheitOrder);
            ProcessState nutzeinheitProcessState = nutzeinheitOrderState.ProcessState;

            Guid orderGuid = SelectedNutzeinheitVM.NutzeinheitOrder.OrderGuid;
            List<DeviceOrderState> deviceOrderStates = ListOfAllDevices.Select(x => x.DeviceUiState.DeviceOrderState).Where(x => x.OrderGuid == orderGuid).ToList();
            NutzeinheitUiState = new NutzeinheitUiState()
            {
                HasSignature = HasBeenSigned(),
                NutzeinheitProcessState = nutzeinheitProcessState,
                OrderStates = deviceOrderStates,
                AmwInfoKeyGuid = nutzeinheitOrderState.AmwInfoKeyGuid
            };
        }

        public void RemoveDeviceFromDeviceList(IDevice device)
        {
            DeviceVM currentDevicesEntryDeviceVM = ListOfCurrentDevices.Where(x => x.Device?.DeviceGuid == device.DeviceGuid).SingleOrDefault();
            if (currentDevicesEntryDeviceVM != null)
            {
                ListOfCurrentDevices.Remove(currentDevicesEntryDeviceVM);
            }

            DeviceVM allDevicesEntryDeviceVM = ListOfAllDevices.Where(x => x.Device?.DeviceGuid == device.DeviceGuid).SingleOrDefault();
            if (allDevicesEntryDeviceVM != null)
            {
                ListOfAllDevices.Remove(allDevicesEntryDeviceVM);
            }
        }

        private async void NewAppointmentExecute()
        {
            try
            {
                if (SelectedNutzeinheitVM == null)
                {
                    throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
                }

                if (!await CheckIfOverwriteSignature())
                {
                    return;
                }

                Nutzer nutzer = nutzerService.GetNutzer(SelectedNutzeinheitVM.NutzerGuid);
                DateTime? newAppointmentDate = await newAppointmentDialog.ShowNewAppointmentDialog(nutzer);

                if (newAppointmentDate != nutzer.NextAppointmentDate)
                {
                    if (SelectedNutzeinheitVM.ParentViewModel is AppointmentPageViewModel appointmentViewModel)
                    {
                        Nutzer nutzerInParentVmAppointments = appointmentViewModel.AppAppointments.SelectMany(x => x.Appointment.AppointmentNutzeinheiten)
                                                                                                  .SelectMany(x => x.Nutzeinheit.Nutzer)
                                                                                                  .Where(x => x.Guid == nutzer.Guid).SingleOrDefault();
                        nutzerInParentVmAppointments.NextAppointmentDate = newAppointmentDate;
                    }

                    nutzer.NextAppointmentDate = newAppointmentDate;
                    nutzerService.UpdateNutzer(nutzer);
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to create new appointment");
                throw;
            }
        }

        private void UpdatePhotoCount(Nutzeinheit nutzeinheit)
        {
            List<Photo> nutzeinheitPhotos = new List<Photo>();
            List<Photo> devicePhotos = new List<Photo>();
            bool hasPicturesDownloaded = true;

            if (nutzeinheit.Photos.Any())
            {
                nutzeinheitPhotos = nutzeinheit.Photos.ToList();
            }
            if (ListOfAllDevices.Any())
            {
                devicePhotos = ListOfAllDevices.SelectMany(x => x.Photos).ToList();
            }

            if (ListOfRemainingDevices.Any())
            {
                List<Photo> remainingDevicesPhotos = ListOfRemainingDevices.SelectMany(x => x.Photos).ToList();
                devicePhotos.AddRange(remainingDevicesPhotos);
            }

            List<Photo> allPhotos = new List<Photo>();
            allPhotos.AddRange(nutzeinheitPhotos);
            allPhotos.AddRange(devicePhotos);

            BadgeSetting = BadgeType.Primary;
            PhotoCount = allPhotos.Count;

            foreach (Photo photo in allPhotos)
            {
                if (string.IsNullOrEmpty(photo.Name))
                {
                    hasPicturesDownloaded = false;
                    break;
                }
                else
                {
                    hasPicturesDownloaded = true;
                }
            }

            if (!hasPicturesDownloaded)
            {
                BadgeSetting = BadgeType.Error;
            }
        }

        public async Task<bool> CheckIfNeAmwInfoKeyExist()
        {
            if (SelectedNutzeinheitVM == null)
            {
                throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
            }

            bool result = true;
            DialogResponse dialogResult = DialogResponse.Accept;
            bool wasInfoKeySelected = SelectedNutzeinheitVM.NutzeinheitUiState.AmwInfoKeyGuid.HasValue;

            if (wasInfoKeySelected)
            {
                dialogResult = await DisplayDeleteAmwInfoKeyWarning();
            }

            if (dialogResult == DialogResponse.Decline)
            {
                result = false;
            }
            else
            {
                SelectedNutzeinheitVM.NutzeinheitUiState = new NutzeinheitUiState()
                {
                    AmwInfoKeyGuid = null,
                    HasSignature = SelectedNutzeinheitVM.NutzeinheitUiState.HasSignature,
                    OrderStates = SelectedNutzeinheitVM.NutzeinheitUiState.OrderStates,
                    NutzeinheitProcessState = SelectedNutzeinheitVM.NutzeinheitUiState.NutzeinheitProcessState
                };
            }
            return result;
        }

        private async Task<DialogResponse> DisplayDeleteAmwInfoKeyWarning()
        {
            if (SelectedNutzeinheitVM == null)
            {
                throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
            }

            DialogResponse result = await dialogService.AcceptDeclineAsync("Sind Sie sicher, dass Sie fortfahren wollen ? Dies würde zu einem Löschen des Nutzeinheit-Infoschlüssels führen.", "JA", "NEIN");

            if (result == DialogResponse.Accept)
            {
                NutzeinheitOrder nutzeinheitOrder = SelectedNutzeinheitVM.NutzeinheitOrder;
                NutzeinheitOrderState nutzeinheitOrderState = nutzeinheitService.GetNutzeinheitOrderState(nutzeinheitOrder);
                nutzeinheitOrderState.AmwInfoKeyGuid = null;
                nutzeinheitOrderState.AmwInfoKey = null;

                if (nutzeinheitOrderState.ProcessState != ProcessState.Creating)
                {
                    nutzeinheitOrderState.ProcessState = ProcessState.InProgress;
                }

                nutzeinheitService.UpdateNutzeinheitOrderState(nutzeinheitOrderState);

                SetNutzeinheitStateInProgress();
            }

            return result;
        }

        private void ToggleSwitchExecute(ToggledEventArgs switchArgs)
        {
            try
            {
                if (switchArgs.Value)
                {
                    DeviceCountInfo = AllDeviceCountInto;
                }
                else
                {
                    DeviceCountInfo = CurrentDeviceCountInfo;
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to refresh listview content information!");
                throw;
            }
        }

        private async void SignatureDialogExecute()
        {
            try
            {
                if (SelectedNutzeinheitVM == null)
                {
                    throw new InvalidOperationException("SelectedNutzeinheitVM cannot be null.");
                }

                await nutzeinheitSignatureDialog.ShowNutzeinheiSignatureDialog(SelectedNutzeinheitVM);
                UpdateNutzeinheitState();

                if (IsSignatureWarningShown)
                {
                    IsSignatureWarningShown = !HasBeenSigned();
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attempting to open signature dialog!");
                throw;
            }
        }

        private void TabChangedExecute(SelectionChangedEventArgs selectedTab)
        {
            if (selectedTab == null)
            {
                return;
            }
        }

        #endregion
    }
}
