//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="PicturePreviewPageViewModel.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
//
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.MAUI.ViewModels
{
    using Eras2AmwApp.BusinessLogic.Interfaces;
    using Eras2AmwApp.BusinessLogic.Models;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Eras2AmwApp.MAUI.Interfaces;
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.IO;
    using System.Linq;
    using System.Threading.Tasks;
    using Microsoft.Maui.Controls;
    using Device = Eras2AmwApp.Domain.Eras2Amw.Models.Device;
    using Microsoft.Maui.Networking;
    using Eras2AmwApp.Domain.Eras2App.Database;
    using Eras2AmwApp.WebService.Interfaces;
    using Eras2AmwApp.MAUI.Models;
    using INutzeinheitService = Eras2AmwApp.BusinessLogic.Interfaces.INutzeinheitService;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.MAUI.Enums;
    using CommunityToolkit.Mvvm.Input;
    using CommunityToolkit.Mvvm.ComponentModel;



    public partial class PicturePreviewPageViewModel : EcViewModelBase, ISupportParentViewModel
    {
        #region fields

        private readonly IPhotoService photoService;
        private readonly IEcDialogService dialogService;
        private readonly IDomainLoader domainLoader;
        private readonly ILoginService loginService;
        private readonly IAmwWebservice amwWebService;
        private readonly ISignatureService signatureService;
        private readonly INutzeinheitService nutzeinheitService;

        [ObservableProperty]
        private bool _canDownloadPicture;

        [ObservableProperty]
        private bool _isDownloadingData;

        [ObservableProperty]
        private bool _canDeletePicture;

        [ObservableProperty]
        private List<Photo>? _photos;

        // nutzeinheitOrder should be non-nullable
        private NutzeinheitOrder nutzeinheitOrder;
        private int imageIndex;
        private int picturesToDownloadCounter;

        [ObservableProperty]
        private EcViewModelBase? _parentViewModel;

        private int photoStackIndex;
        private bool isInitValue;
        private string? nutzeinheitPictureDirectory;

        [ObservableProperty]
        private string? _photoName;

        [ObservableProperty]
        private string? _photoInformation;

        [ObservableProperty]
        private ObservableCollection<string> _stackPhotos = new();

        #endregion

        #region ctor

        public PicturePreviewPageViewModel(
            IServiceLocator serviceLocator,
            IEcNavigationService navigationService,
            IPhotoService photoService,
            IEcDialogService dialogService,
            IDomainLoader domainLoader,
            ILoginService loginService,
            IAmwWebservice amwWebService,
            ISignatureService signatureService,
            INutzeinheitService nutzeinheitService)
           : base(serviceLocator, navigationService)
        {
            this.photoService = photoService ?? throw new ArgumentNullException(nameof(photoService));
            this.dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
            this.domainLoader = domainLoader ?? throw new ArgumentNullException(nameof(domainLoader));
            this.loginService = loginService ?? throw new ArgumentNullException(nameof(loginService));
            this.amwWebService = amwWebService ?? throw new ArgumentNullException(nameof(amwWebService));
            this.signatureService = signatureService ?? throw new ArgumentNullException(nameof(signatureService));
            this.nutzeinheitService = nutzeinheitService ?? throw new ArgumentNullException(nameof(nutzeinheitService));

            InitilizeProperties();
        }

        #endregion

        #region commands

        [RelayCommand(CanExecute = nameof(CanDeletePicture))]
        private void DeleteCard() => DeleteCardExecute();

        [RelayCommand]
        private void PictureChange(CardLayoutPlaceholder popup) => PictureChangeExecute(popup);

        [RelayCommand(CanExecute = nameof(CanDownloadPicture))]
        private void DownloadPicture() => DownloadPictureExecute();

        #endregion

        #region properties



        #endregion

        #region public methods

        public override Task SetupAsync(object navigationData)
        {
            if (navigationData is NutzeinheitPageViewModel parentVM)
            {
                // When assigning nutzeinheitOrder, ensure SelectedNutzeinheitVM is not null
                if (parentVM?.SelectedNutzeinheitVM != null)
                {
                    nutzeinheitOrder = parentVM.SelectedNutzeinheitVM.NutzeinheitOrder;
                    nutzeinheitPictureDirectory = Path.Combine(appSettings.PicturesDirectory, "Photos", "Nutzeinheiten", nutzeinheitOrder.NutzeinheitGuid.ToString());
                    ParentViewModel = parentVM;
                    AssignNutzeinheitPhotos();
                }
            }
            return base.SetupAsync(navigationData);
        }

        public AppointmentPageViewModel GetParentProperty()
        {
            // This implementation returns null as we don't have a direct path to AppointmentPageViewModel
            return null;
        }

        // Helper method to get the direct parent which is a NutzeinheitPageViewModel
        private NutzeinheitPageViewModel GetNutzeinheitParent()
        {
            if (ParentViewModel is NutzeinheitPageViewModel vm)
            {
                return vm;
            }
            throw new InvalidOperationException("ParentViewModel is not a NutzeinheitPageViewModel");
        }

        #endregion

        #region private methods

        private void InitilizeProperties()
        {
            CanDownloadPicture = false;
            CanDeletePicture = false;
            Photos = new List<Photo>();
            StackPhotos = new ObservableCollection<string>();
            IsDownloadingData = false;
            isInitValue = true;
        }

        private void AssignNutzeinheitPhotos()
        {
            try
            {
                Photos = LoadDevices();

                if (Photos.Count != 0)
                {
                    foreach (Photo photo in Photos)
                    {
                        if (DoesNutzeinheitPhotoAlreadyExist(photo.NutzeinheitGuid) && string.IsNullOrEmpty(photo.Name))
                        {
                            AttemptToLoadLocalNePictures(photo, nutzeinheitPictureDirectory);
                        }
                        else if (DoesDevicePhotoAlreadyExist(photo) && string.IsNullOrEmpty(photo.Name))
                        {
                            AttemptToLoadLocalDevicePictures(photo);
                        }

                        if (string.IsNullOrEmpty(photo.Name))
                        {
                            picturesToDownloadCounter++;
                        }
                        else
                        {
                            StackPhotos.Add(photo.Path);
                        }
                    }

                    if (StackPhotos.Count != 0)
                    {
                        List<Photo> createdByAppPhotos = Photos.Where(y => y.CreatedByApp).ToList();

                        Photo lastPhoto = new Photo();
                        lastPhoto = createdByAppPhotos.Any() ? createdByAppPhotos.Last() : Photos.Last();
                        SetPhotoDetails(lastPhoto);

                        if (lastPhoto.CreatedByApp)
                        {
                            CardLayoutPlaceholder cards = new CardLayoutPlaceholder
                            {
                                VisibleCardIndex = lastPhoto.DeviceGuid != null || lastPhoto.NutzeinheitGuid != null ? Photos.Count - 1 : Photos.Count - StackPhotos.Count()
                            };

                            PictureChangeExecute(cards);
                        }
                    }

                    if (picturesToDownloadCounter > 0)
                    {
                        CanDownloadPicture = true;
                        // DownloadPictureCommand.ChangeCanExecute(); // Removed as per refactor
                    }
                }
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while attepting to load/assign pictures in this NE");
            }
        }

        private void SetPhotoDetails(Photo photo)
        {
            if (photo.Nutzeinheit != null)
            {
                PhotoInformation = string.Format($"Nutzeinheit nummer: {photo.Nutzeinheit.Number}"); //"nutzeinheit foto";
            }
            else
            {
                PhotoInformation = photo.Device != null ? "Gerätenummer: " + photo.Device.Number : "";
            }
        }

        private void AttemptToLoadLocalNePictures(Photo photo, string path)
        {
            ReassignPhotoNameAndPath(photo, path);
            ReassignPreviousVMListOfDevices(photo);
            photoService.UpdatePhoto(photo);
        }

        private void AttemptToLoadLocalDevicePictures(Photo photo)
        {
            // Add null checks for Photos before dereferencing
            if (photo.DeviceGuid != Guid.Empty && !string.IsNullOrEmpty(photo.DeviceGuid.ToString()))
            {
                string path = Path.Combine(appSettings.PicturesDirectory, "Photos", "Devices", photo.DeviceGuid.ToString());
                ReassignPhotoNameAndPath(photo, path);
                ReassignPreviousVMListOfDevices(photo);
                photoService.UpdatePhoto(photo);
            }
        }

        private string GetPhotoName(Photo photo)
        {
            DateTime recordedTime = photo.RecordedDate.ToLocalTime();
            return "Img_" + recordedTime.ToString("yyyyMMdd_HHmmss") + ".jpeg";
        }

        private bool DoesNutzeinheitPhotoAlreadyExist(Guid? neGuid)
        {
            return Directory.Exists(nutzeinheitPictureDirectory) && neGuid != null;
        }

        private bool DoesDevicePhotoAlreadyExist(Photo photo)
        {
            string devicePictureDirectory = Path.Combine(appSettings.PicturesDirectory, "Photos", "Devices", photo.DeviceGuid.ToString());
            return Directory.Exists(devicePictureDirectory) && photo.DeviceGuid != null;
        }

        private void ReassignPhotoNameAndPath(Photo photo, string path)
        {
            string photoName = GetPhotoName(photo);
            string path2 = Path.Combine(path, photo.Guid.ToString() + ".jpg");
            string path1 = Path.Combine(path, photoName);

            if (File.Exists(path1))
            {
                photo.Path = path1;
                photo.Name = photoName;
            }
            else if(File.Exists(path2))
            {
                photo.Path = path2;
                photo.Name = Path.GetFileName(path2);
            }
        }

        private void ReassignPreviousVMListOfDevices(Photo photo)
        {
            NutzeinheitPageViewModel parent = GetNutzeinheitParent();
            if (parent == null || parent.ListOfCurrentDevices == null)
            {
                return;
            }

            Photo? oldPhotos = parent.ListOfCurrentDevices.SelectMany(x => x.Photos).FirstOrDefault(y => y.Guid == photo.Guid);
            if (oldPhotos != null)
            {
                oldPhotos.Name = photo.Name;
                oldPhotos.Path = photo.Path;
            }
        }

        private List<Photo> LoadDevices()
        {
            Nutzeinheit nutzeinheit = domainLoader.LoadNutzeinheitWithDevices(nutzeinheitOrder.NutzeinheitGuid);
            List<Photo> nutzeinheitAndDevicePhotos = nutzeinheit.Photos.ToList();

            List<Device> nutzeinheitDevieces = nutzeinheit.Devices.ToList();

            foreach (Device device in nutzeinheitDevieces)
            {
                nutzeinheitAndDevicePhotos.AddRange(device.Photos);
            }
            nutzeinheitAndDevicePhotos = nutzeinheitAndDevicePhotos.OrderBy(x => x.CreatedByApp ? 1 : 0).ToList();

            return nutzeinheitAndDevicePhotos;
        }

        private async void DeleteCardExecute()
        {
            try
            {
                if (!await CheckIfCanBeSave())
                {
                    return;
                }
                DeletePhoto();
            }
            catch (Exception ex)
            {
                logger.Error(ex, "Error deleting selected image");
                throw;
            }
        }

        private async Task<bool> CheckIfCanBeSave()
        {
            //Check if there is AmwKey in Ne
            NutzeinheitPageViewModel pageViewModel = GetNutzeinheitParent();
            if (pageViewModel == null)
            {
                return false;
            }

            bool result = await pageViewModel.CheckIfNeAmwInfoKeyExist();

            if (!result)
            {
                return false;
            }

            //Check if user wants to procede and delete signature if exists
            Signature signature = GetSignatureForThisOrder();

            if (signature != null)
            {
                DialogResponse signatureDeleteWarningResult = await DisplayDeleteSignatureWarning(signature);
                if (signatureDeleteWarningResult == DialogResponse.Decline)
                {
                    return false;
                }
            }

            return true;
        }

        private Signature GetSignatureForThisOrder()
        {
            return signatureService.GetSignatureForOrder(nutzeinheitOrder.NutzeinheitGuid, nutzeinheitOrder.OrderGuid);
        }

        private async Task<DialogResponse> DisplayDeleteSignatureWarning(Signature signature)
        {
            string warning = localisationService.Get("DeleteSignatureWarning");
            var result = await dialogService.AcceptDeclineAsync(warning, "JA", "NEIN");

            if (result == DialogResponse.Decline)
            {
                return result;
            }

            DeleteSignature(signature);

            return result;
        }

        private void DeleteSignature(Signature signature)
        {
            if (signature.Path != null)
            {
                signatureService.RemoveSignatureFromDevice(signature);
            }
            signatureService.DeleteSignature(signature);
            SetNutzeinheitStateInProgress();
        }

        private void SetNutzeinheitStateInProgress()
        {
            NutzeinheitOrderState nutzeinheitOrderState = nutzeinheitService.GetNutzeinheitOrderState(nutzeinheitOrder);

            if (nutzeinheitOrderState.ProcessState == ProcessState.InProgress)
            {
                return;
            }

            nutzeinheitOrderState.ProcessState = ProcessState.InProgress;
            nutzeinheitService.UpdateNutzeinheitOrderState(nutzeinheitOrderState);
        }

        private void DeletePhoto()
        {
            // Fix for-loop on Photos to check for null first
            if (Photos != null)
            {
                for (int index = 0; index < Photos.Count; index++)
                {
                    if (index == imageIndex)
                    {
                        StackPhotos.RemoveAt(photoStackIndex);
                        File.Delete(Photos[index].Path);
                        photoService.DeletePhoto(Photos[index]);

                        //delete from previousVM list
                        if (Photos[index].DeviceGuid != null) //only needed for device photos
                        {
                            NutzeinheitPageViewModel parent = GetNutzeinheitParent();
                            if (parent == null)
                            {
                                return;
                            }

                            ObservableCollection<DeviceVM> listOfDevices = parent.ListOfAllDevices;
                            ObservableCollection<DeviceVM> listOfRemainingDevices = parent.ListOfRemainingDevices;

                            if (listOfDevices != null)
                            {
                                Photo? deletedPhoto = listOfDevices.SelectMany(x => x.Photos).FirstOrDefault(y => y.Guid == Photos[index].Guid);
                                if (deletedPhoto != null)
                                {
                                    DeviceVM? deviceVM = listOfDevices.FirstOrDefault(x => x.Photos.Contains(deletedPhoto));
                                    if (deviceVM?.Photos != null)
                                    {
                                        deviceVM.Photos.Remove(deletedPhoto);
                                    }
                                }
                                else if (listOfRemainingDevices != null)
                                {
                                    deletedPhoto = listOfRemainingDevices.SelectMany(x => x.Photos).FirstOrDefault(y => y.Guid == Photos[index].Guid);
                                    if (deletedPhoto != null)
                                    {
                                        DeviceVM? deviceVM = listOfRemainingDevices.FirstOrDefault(x => x.Photos.Contains(deletedPhoto));
                                        if (deviceVM?.Photos != null)
                                        {
                                            deviceVM.Photos.Remove(deletedPhoto);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            UpdatePhotos();
        }

        private void PictureChangeExecute(CardLayoutPlaceholder cards)
        {
            try
            {
                CanDeletePicture = CanDeleteCurrentPicture(cards);

                // DeleteCardCommand.ChangeCanExecute(); // Removed as per refactor
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while executing PictureChange Event to Command method!");
                throw;
            }
        }

        private bool CanDeleteCurrentPicture(CardLayoutPlaceholder cards)
        {
            try
            {
                bool returnValue = false;

                imageIndex = cards.VisibleCardIndex;

                if (imageIndex < 0)
                {
                    PhotoName = "";
                    PhotoInformation = "";
                    return false;
                }

                // In CanDeleteCurrentPicture and similar methods, add null and bounds checks:
                if (Photos == null || StackPhotos == null || imageIndex < 0 || imageIndex >= Photos.Count)
                {
                    return false;
                }
                Photo photo = Photos[imageIndex];

                if (isInitValue)
                {
                    photoStackIndex = StackPhotos.Count() - 1; //changed to match stack index real position
                }
                else
                {
                    if (Photos != null && StackPhotos != null && Photos.Count == StackPhotos.Count())
                    {
                        photoStackIndex = imageIndex;
                    }
                    else
                    {
                        int offset = Photos.Count - (StackPhotos?.Count() ?? 0) + imageIndex;
                        // For usages like Photos[Photos.Count - StackPhotos.Count() + imageIndex], add checks:
                        if (Photos != null && offset >= 0 && offset < Photos.Count)
                        {
                            photo = Photos[offset];
                        }
                        photoStackIndex = imageIndex;
                        imageIndex = StackPhotos.Count() - 1 + imageIndex;
                    }
                }

                SetPhotoDetails(photo);

                isInitValue = false;
                if (photo.CreatedByApp)
                {
                    returnValue = true;
                }
                return returnValue;
            }
            catch (Exception e)
            {
                logger.Error(e, "Exception occured while executing PictureChange Event to Command method!");
                throw;
            }
        }

        private void UpdatePhotos()
        {
            StackPhotos.Clear();
            Photos = LoadDevices();

            foreach (Photo photo in Photos)
            {
                if (photo.Path != null)
                {
                    StackPhotos.Add(photo.Path);
                }
                ReassignPreviousVMListOfDevices(photo);
            }

            isInitValue = true;

            // Add null checks for Photos before dereferencing
            if (Photos == null || !Photos.Any())
            {
                PhotoInformation = "";
                CanDeletePicture = false;
                return;
            }

            Photo lastPhoto = Photos.Last();

            if (lastPhoto.CreatedByApp)
            {
                CardLayoutPlaceholder cards = new CardLayoutPlaceholder
                {
                    VisibleCardIndex = lastPhoto.DeviceGuid != null || lastPhoto.NutzeinheitGuid != null ? Photos.Count - 1 : Photos.Count - StackPhotos.Count()
                };

                PictureChangeExecute(cards);
            }
            else
            {
                SetPhotoDetails(lastPhoto);
                CanDeletePicture = false;
                // DeleteCardCommand.ChangeCanExecute(); // Removed as per refactor
            }
        }

        private async void DownloadPictureExecute()
        {
            try
            {
                #if ANDROID
                loginService.NetworkAccess = Connectivity.NetworkAccess == NetworkAccess.Internet;
                #else
                loginService.NetworkAccess = true;
                #endif

                var response = (DialogResponse)await dialogService.AcceptDeclineAsync(string.Format( localisationService.Get("PhotoDownloadConfirmation"), picturesToDownloadCounter), "JA", "NEIN");

                if (response == DialogResponse.Decline)
                {
                    return;
                }

                //starts downloading data indicator
                IsDownloadingData = true;

                User user = loginService.GetAppUser();
                Guid userGuid = await amwWebService.LoginAsync(user.Name.Trim().ToLower(), user.Password.Trim());
                Nutzeinheit nutzeinheit = domainLoader.LoadNutzeinheitWithDevices(nutzeinheitOrder.NutzeinheitGuid);

                await DownloadNutzeinheitPictures(userGuid, nutzeinheit);
                await DownloadDevicePictures(userGuid, nutzeinheit);
                await amwWebService.LogoutAsync();

                UpdatePhotos();
                CanDownloadPicture = false;
                // DownloadPictureCommand.ChangeCanExecute(); // Removed as per refactor

                //finished downloading data indicator
                IsDownloadingData = false;
            }
            catch(Exception e)
            {
                logger.Error(e, "Exception occured while attempting to download picture!");
                throw;
            }
        }

        private async Task DownloadNutzeinheitPictures(Guid userGuid, Nutzeinheit nutzeinheit)
        {
            List<Photo> nutzeinheitPhotos = nutzeinheit.Photos.Where(x => !x.CreatedByApp).ToList();
            foreach (Photo photo in nutzeinheitPhotos)
            {
                await amwWebService.DownloadNutzeinheitPhotoAsync(userGuid, photo);
            }
        }

        private async Task DownloadDevicePictures(Guid userGuid, Nutzeinheit nutzeinheit)
        {
            List<Photo> devicesPhotos = nutzeinheit.Devices.SelectMany(x => x.Photos.Where(y => !y.CreatedByApp).ToList()).ToList();
            foreach (Photo photo in devicesPhotos)
            {
                await amwWebService.DownloadDevicePhotoAsync(userGuid, photo);
            }
        }

        #endregion
    }

    public class CardLayoutPlaceholder
    {
        public int VisibleCardIndex { get; set; }
    }
}