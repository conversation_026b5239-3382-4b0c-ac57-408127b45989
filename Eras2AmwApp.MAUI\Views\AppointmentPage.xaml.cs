using Eras2AmwApp.Domain.Eras2Amw.Models;
using Eras2AmwApp.MAUI.ViewModels;
using System.Diagnostics;

namespace Eras2AmwApp.MAUI.Views;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class AppointmentPage : ContentPage
{
    private AppointmentPageViewModel ViewModel => BindingContext as AppointmentPageViewModel;

    public AppointmentPage(AppointmentPageViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    // This constructor is for design-time support
    public AppointmentPage()
    {
        InitializeComponent();
        // This constructor should only be used by the XAML designer
        if (BindingContext == null && Application.Current != null)
        {
            Debug.WriteLine("Using default constructor - this should only happen at design time");
            // Don't set BindingContext here - it should be set by DI or XAML
        }
    }

    private void OnAppointmentSelected(object sender, SelectionChangedEventArgs e)
    {
        if (e.CurrentSelection.FirstOrDefault() is Appointment appointment)
        {
            ViewModel.SelectedScheduleAppointmentCommand.Execute(appointment);
            
            // Clear selection
            ((CollectionView)sender).SelectedItem = null;
        }
    }
}
