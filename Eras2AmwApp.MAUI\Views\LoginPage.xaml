<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Eras2AmwApp.MAUI.Views.LoginPage"
             Title="Login">

    <Grid BackgroundColor="White">

        <Grid.RowDefinitions>
            <RowDefinition Height="25*"></RowDefinition>
            <RowDefinition Height="15*"></RowDefinition>
            <RowDefinition Height="15*"></RowDefinition>
            <RowDefinition Height="18*"></RowDefinition>
            <RowDefinition Height="10*"></RowDefinition>
            <RowDefinition Height="10*"></RowDefinition>
            <RowDefinition Height="10*"></RowDefinition>
            <RowDefinition Height="7*"></RowDefinition>
            <RowDefinition Height="7*"></RowDefinition>
            <RowDefinition Height="9*"></RowDefinition>
        </Grid.RowDefinitions>

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="20*" />
            <ColumnDefinition Width="10*" />
            <ColumnDefinition Width="10*" />
            <ColumnDefinition Width="10*" />
            <ColumnDefinition Width="10*" />
            <ColumnDefinition Width="10*" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <Label  Text="{Binding AppTitle}"
                Grid.Row="0"
                Grid.Column="1"
                Grid.ColumnSpan="3"
                HorizontalOptions="Center"
                VerticalOptions="Center"
                TextColor="Black"
                FontSize="35"/>

        <Label  Text="Anmelden"
                HorizontalOptions="Center"
                VerticalOptions="Center"
                Grid.Row="1"
                Grid.Column="1"
                Grid.ColumnSpan="3"
                TextColor="Black"
                FontSize="32"/>

        <Entry  Text="{Binding Username}"
                Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="3"
                VerticalOptions="Center"
                Placeholder="Nutzername"
                BackgroundColor="White">
        </Entry>

        <Label  Text="{Binding UsernameErrorText}"
                Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="3"
                VerticalOptions="End"
                TextColor="Red"
                FontSize="Small"
                IsVisible="{Binding UsernameHasError}" />

        <Entry  Text="{Binding Password}"
                Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="3"
                VerticalOptions="Center"
                Placeholder="Passwort"
                IsPassword="True"
                BackgroundColor="White">
        </Entry>

        <Label  Text="{Binding PasswordErrorText}"
                Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="3"
                VerticalOptions="End"
                TextColor="Red"
                FontSize="Small"
                IsVisible="{Binding PasswordHasError}" />

        <Button Margin="5,5,5,0"
                Text="Login"
                Command="{Binding LoginCommand}"
                Grid.Row="4"
                Grid.Column="1"
                Grid.ColumnSpan="3"
                BackgroundColor="#007AFF"
                TextColor="White"
                FontSize="18"
                CornerRadius="8"
                Padding="10" />

        <Label  Text="{Binding LoginErrorText}"
                Grid.Row="5"
                Grid.Column="1"
                Grid.ColumnSpan="4"
                TextColor="Red"
                FontSize="Small" />

        <Button Margin="5,5,5,0"
                Text="Nutzer löschen"
                Command="{Binding ResetUserCommand}"
                Grid.Row="9"
                Grid.Column="4"
                Grid.ColumnSpan="3"
                BackgroundColor="#007AFF"
                TextColor="White"
                FontSize="14"
                CornerRadius="8"
                Padding="10" />

        <Button Margin="5,5,5,0"
                Text="Daten löschen"
                Command="{Binding WipeDatabaseCommand}"
                Grid.Row="9"
                Grid.Column="2"
                Grid.ColumnSpan="2"
                BackgroundColor="#007AFF"
                TextColor="White"
                FontSize="14"
                CornerRadius="8"
                Padding="10" />

        <Button Margin="5,5,5,0"
                Text="Registrieren"
                Command="{Binding RegisterCommand}"
                Grid.Row="9"
                Grid.Column="0"
                Grid.ColumnSpan="2"
                BackgroundColor="#28CD41"
                TextColor="White"
                FontSize="14"
                CornerRadius="8"
                Padding="10" />

        <Label x:Name="AppVersion"
               Margin="5,5,5,0"
               Text="{Binding AppVersion}"
               Grid.Row="7"
               Grid.Column="4"
               Grid.ColumnSpan="2"
               HorizontalOptions="End"
               VerticalOptions="End">
        </Label>

        <Label x:Name="Webservice"
               Margin="5,5,10,0"
               Text="{Binding WebserviceUrl}"
               Grid.Row="8"
               Grid.Column="4"
               Grid.ColumnSpan="3"
               FontSize="Medium"
               HorizontalTextAlignment="End"
               VerticalOptions="End">
        </Label>

    </Grid>
</ContentPage>
