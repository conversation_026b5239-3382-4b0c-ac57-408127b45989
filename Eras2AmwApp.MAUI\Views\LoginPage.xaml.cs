using Eras2AmwApp.MAUI.ViewModels;
using System.Diagnostics;

namespace Eras2AmwApp.MAUI.Views;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class LoginPage : ContentPage
{
    private LoginPageViewModel? ViewModel => BindingContext as LoginPageViewModel;

    public LoginPage(LoginPageViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    // This constructor is for design-time support
    public LoginPage()
    {
        InitializeComponent();
        // This constructor should only be used by the XAML designer
        if (BindingContext == null && Application.Current != null)
        {
            Debug.WriteLine("Using default constructor - this should only happen at design time");
            // Don't set BindingContext here - it should be set by DI or XAML
        }
    }

    protected override void OnAppearing()
    {
        base.OnAppearing();
        ViewModel?.AppearingCommand?.Execute(null);
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        ViewModel?.DisappearingCommand?.Execute(null);
    }
}
