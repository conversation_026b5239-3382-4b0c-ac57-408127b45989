<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://xamarin.com/schemas/2014/forms"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:d="http://xamarin.com/schemas/2014/forms/design"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:converter="clr-namespace:Eras2AmwApp.Converter"
             xmlns:markupExtensions="clr-namespace:Eras2AmwApp.MarkupExtensions;assembly=Eras2AmwApp"
             xmlns:behaviors="clr-namespace:Eras2AmwApp.Behaviors"
             xmlns:processStateEnum="clr-namespace:Eras2AmwApp.Domain.Eras2Amw.Enums;assembly=Eras2AmwApp.Domain"
             mc:Ignorable="d"
             x:Class="Eras2AmwApp.Pages.OrderPage"
             x:Name="OrderPageName">

    <ContentPage.Resources>
        <converter:NutzeinheitStatusConverter x:Key="NutzeinheitStatusConverter"></converter:NutzeinheitStatusConverter>
        <converter:NutzeinheitOrderStateConverter x:Key="NutzeinheitOrderStateConverter"></converter:NutzeinheitOrderStateConverter>
        <converter:NutzerKindConverter x:Key="NutzerKindConverter"></converter:NutzerKindConverter>
        <converter:OrderStateConverter x:Key="OrderStateConverter"></converter:OrderStateConverter>
        <converter:ShortTimeConverter x:Key="ShortTimeConverter"></converter:ShortTimeConverter>
        <converter:ShortDateConverter x:Key="ShortDateConverter"></converter:ShortDateConverter>
        <converter:NegateBooleanConverter x:Key="NegateBooleanConverter"></converter:NegateBooleanConverter>
        <converter:DeviceNutzeinheitOrderConverter x:Key="DeviceNutzeinheitOrderConverter"></converter:DeviceNutzeinheitOrderConverter>
    </ContentPage.Resources>

    <ContentPage.Behaviors>
        <behaviors:EventToCommandBehavior EventName="Appearing" 
                                          Command="{Binding AppearingCommand}">
        </behaviors:EventToCommandBehavior>

        <behaviors:EventToCommandBehavior EventName="Disappearing"
                                          Command="{Binding DisappearingCommand}">
        </behaviors:EventToCommandBehavior>
    </ContentPage.Behaviors>

    <ContentPage.Content>
        
        <StackLayout Spacing="0" >

            <Frame  Margin="10,2,10,2"
                    Padding="0"
                    BorderColor="LightGray"
                    CornerRadius="0"
                    HasShadow="True">

                <Frame.GestureRecognizers>
                    <TapGestureRecognizer   Command="{Binding OrderEditCommand}"
                                            CommandParameter="{Binding SelectedAppointment}"
                                            NumberOfTapsRequired="1">
                    </TapGestureRecognizer>
                </Frame.GestureRecognizers>

                <Grid RowSpacing="0" ColumnSpacing="0">

                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="Auto"></RowDefinition>
                    </Grid.RowDefinitions>

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="6"></ColumnDefinition>
                        <ColumnDefinition Width="9"></ColumnDefinition>
                        <ColumnDefinition Width="50"></ColumnDefinition>
                        <ColumnDefinition Width="60*"></ColumnDefinition>
                        <ColumnDefinition Width="105"></ColumnDefinition>
                        <ColumnDefinition Width="Auto"></ColumnDefinition>
                    </Grid.ColumnDefinitions>

                    <Label  x:Name="OrderColor"
                            Margin="0"
                            Grid.Row="0"
                            Grid.RowSpan="3"
                            Grid.Column="0"
                            BackgroundColor="{Binding OrderState, Converter={StaticResource OrderStateConverter}}">

                    </Label>

                    <Label  x:Name="DeviceColor"
                            Grid.Row="0"
                            Grid.RowSpan="3"
                            Grid.Column="1"
                            Margin="3,0,0,0"
                            BackgroundColor="{Binding DeviceNutzeinheitenState, Converter={StaticResource DeviceNutzeinheitOrderConverter}}">

                    </Label>

                    <Label  x:Name="StartTime"
                            Grid.Row="0"
                            Grid.Column="2"
                            Margin="5,5,0,0"
                            Text="{Binding StartTime, Converter={StaticResource ShortTimeConverter}}"
                            TextColor="Black"
                            FontSize="Small"
                            FontAttributes="Bold">

                    </Label>

                    <Label x:Name="EndTime"
                           Grid.Row="1"
                           Grid.Column="2"
                           Margin="5,0,0,0"
                           Text="{Binding EndTime, Converter={StaticResource ShortTimeConverter}}"
                           TextColor="Black"
                           FontSize="Small"
                           FontAttributes="Bold">

                    </Label>

                    <Label x:Name="MainText"
                           Grid.Row="0"
                           Grid.Column="3"
                           Margin="5,5,20,0"
                           Text="{Binding Address}"
                           TextColor="Black"
                           FontSize="Small"
                           FontAttributes="Bold">

                    </Label>

                    <Label  x:Name="MinorText"
                            Grid.Row="1"
                            Grid.Column="3"
                            Margin="5,0,0,5"
                            Text="{Binding OrderLabel}"
                            FontSize="Micro">

                    </Label>

                    <Label  x:Name="OrderNumber"
                            Grid.Row="0"
                            Grid.Column="5"
                            VerticalTextAlignment="Start"
                            HorizontalTextAlignment="End"
                            Margin="0,5,5,0"
                            Text="{Binding OrderNumber}"
                            FontSize="Small"
                            TextColor="Black"
                            FontAttributes="Bold">

                    </Label>

                    <Label  x:Name="BillingPeriodEnd"
                            Grid.Row="1"
                            Grid.Column="4"
                            Grid.ColumnSpan="2"
                            VerticalTextAlignment="Start"
                            HorizontalTextAlignment="End"
                            Margin="5,0,5,0"
                            Text="{Binding BillingPeriodEnd, Converter={StaticResource ShortDateConverter}}"
                            FontSize="Micro"
                            TextColor="Black">
                    </Label>

                    <Image  x:Name="infoIcon"
                            Grid.Row="0"
                            Grid.Column="4"
                            Margin="30,0,0,5"
                            HeightRequest="30"
                            WidthRequest="30"
                            VerticalOptions="Start"
                            HorizontalOptions="End"
                            Source="infoIcon.png"
                            IsEnabled="{Binding HasOrderNote}"
                            IsVisible="{Binding HasOrderNote}">

                        <Image.GestureRecognizers>
                            <TapGestureRecognizer   Command="{Binding OrderIconButtonCommand}"
                                                    CommandParameter="{Binding SelectedAppointment}"
                                                    NumberOfTapsRequired="1" />
                        </Image.GestureRecognizers>

                    </Image>
                    
                </Grid>
                
            </Frame>

            <ListView   ItemsSource="{Binding AppointmentNutzeinheiten}"
                        Margin="10,0,10,0"
                        x:Name="NutzeinheitList"
                        SelectionMode="Single"
                        SelectedItem="{Binding SelectedNutzeinheit}"
                        HasUnevenRows="True"
                        IsPullToRefreshEnabled="False"
                        CachingStrategy="RecycleElement">

                <ListView.Behaviors>
                    <behaviors:EventToCommandBehavior   EventName="ItemTapped" 
                                                        Command="{Binding ListViewItemTappedCommand}">
                    </behaviors:EventToCommandBehavior>
                </ListView.Behaviors>

                <ListView.Header>
                    
                    <Grid RowSpacing="0">
                        
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"></RowDefinition>
                            <RowDefinition Height="Auto"></RowDefinition>
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="85*"></ColumnDefinition>
                            <ColumnDefinition Width="130"></ColumnDefinition>
                        </Grid.ColumnDefinitions>

                        <Label  Text="{markupExtensions:Localisation Nutzeinheiten}"
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="5,5,5,0"
                                FontSize="Large" />

                        <Label  Text="{Binding NutzeinheitenHeaderNote}"
                                Grid.Row="1"
                                Grid.Column="0"
                                Margin="5,0,5,5"
                                FontSize="Small"
                                TextColor="LightGray" />

                        <ImageButton    Source="newIcon.png"
                                        BackgroundColor="Transparent"
                                        Margin="0,0,5,0"
                                        Grid.Row="0"
                                        Grid.RowSpan="2"
                                        HeightRequest="35"
                                        WidthRequest="35"
                                        Grid.Column="1"
                                        HorizontalOptions="End"
                                        Command="{Binding AddNutzeinheitCommand}"
                                        CommandParameter="{Binding SelectedAppointment}" />
                        
                    </Grid>
                    
                </ListView.Header>

                <ListView.ItemTemplate>
                    <DataTemplate x:Name="NutzeinheitListElement">
                        <ViewCell>
                            <Frame  Margin="2"
                                    Padding="0"
                                    BorderColor="LightGray"
                                    CornerRadius="0"
                                    HasShadow="True">

                                <Grid RowSpacing="0" ColumnSpacing="0">

                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"></RowDefinition>
                                        <RowDefinition Height="Auto"></RowDefinition>
                                        <RowDefinition Height="Auto"></RowDefinition>
                                    </Grid.RowDefinitions>

                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="6"></ColumnDefinition>
                                        <ColumnDefinition Width="55*"></ColumnDefinition>
                                        <ColumnDefinition Width="Auto"></ColumnDefinition>
                                        <ColumnDefinition Width="90"></ColumnDefinition>
                                    </Grid.ColumnDefinitions>

                                    <Label  x:Name="StatusColor"
                                            Grid.Row="0"
                                            Grid.RowSpan="3"
                                            Grid.Column="0"
                                            BackgroundColor="{Binding NutzeinheitUiState, Converter={StaticResource NutzeinheitOrderStateConverter}}">

                                    </Label>

                                    <Label  x:Name="Nutzer"
                                            Grid.Row="0"
                                            Grid.Column="1"
                                            Margin="5,0,30,0"
                                            Text="{Binding NutzerNameLocation}"
                                            TextColor="Black"
                                            FontAttributes="Bold"
                                            FontSize="Small">

                                    </Label>

                                    <Label  x:Name="NutzeinheitAddress"
                                            Grid.Row="1"
                                            Grid.Column="1"
                                            Margin="5,0,5,5"
                                            Text="{Binding NutzeinheitAddress}"
                                            FontSize="Micro">
                                    </Label>

                                    <Label  x:Name="NutzeinheitNumber"
                                            Grid.Row="0"
                                            Grid.Column="3"
                                            VerticalTextAlignment="Start"
                                            HorizontalTextAlignment="End"
                                            Margin="0,5,5,0"
                                            Text="{Binding NutzeinheitNumber}"
                                            FontSize="Small"
                                            TextColor="Black"
                                            FontAttributes="Bold">

                                    </Label>

                                    <ImageButton    Source="maintainIcon.png"
                                                    BackgroundColor="Transparent"
                                                    Grid.Row="1"
                                                    Grid.Column="3"
                                                    Margin="0,0,5,0"
                                                    HeightRequest="40"
                                                    IsVisible="{Binding IsNeLocked,Converter={StaticResource NegateBooleanConverter}}"
                                                    WidthRequest="40"
                                                    VerticalOptions="Center"
                                                    HorizontalOptions="End"
                                                    Command="{Binding Source={x:Reference OrderPageName},Path = BindingContext.NutzeinheitAmwInfoCommand}"
                                                    CommandParameter="{Binding .}" >

                                    </ImageButton>

                                    <Image  x:Name="LockImage"
                                            Grid.Row="1"
                                            Grid.Column="3"
                                            Margin="5,0,0,5"
                                            HeightRequest="40"
                                            WidthRequest="40"
                                            IsVisible="{Binding IsNeLocked}"
                                            VerticalOptions="Start"
                                            HorizontalOptions="End"
                                            Source="{Binding NutzeinheitState, Converter={StaticResource NutzeinheitStatusConverter}}">

                                        <Image.GestureRecognizers>
                                            <TapGestureRecognizer   Command="{Binding Path=BindingContext.NutzeinheitStateChangeCommand, Source={x:Reference NutzeinheitList}}"
                                                                    CommandParameter="{Binding NutzeinheitOrder}"
                                                                    NumberOfTapsRequired="1" />
                                        </Image.GestureRecognizers>
                                    </Image>

                                    <Image  x:Name="infoIcon"
                                            Grid.Row="1"
                                            Grid.Column="3"
                                            Margin="3,0,0,0"
                                            HeightRequest="30"
                                            WidthRequest="30"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start"
                                            Source="infoIcon.png">

                                        <Image.Triggers>
                                            <DataTrigger    TargetType="Image"
                                                            Binding="{Binding NutzeinheitNote,TargetNullValue=''}"
                                                            Value="">
                                                
                                                <Setter Property="IsEnabled"
                                                        Value="False"/>
                                                <Setter Property="IsVisible"
                                                        Value="False"/>
                                            </DataTrigger>
                                        </Image.Triggers>

                                        <Image.GestureRecognizers>
                                            <TapGestureRecognizer   NumberOfTapsRequired="1"
                                                                    Command="{Binding Source={x:Reference OrderPageName},Path = BindingContext.NutzeinheitIconButtonCommand}"
                                                                    CommandParameter="{Binding .}" />
                                        </Image.GestureRecognizers>

                                    </Image>

                                    <ImageButton    x:Name="NewDateButton"
                                                    Source="newDateIcon.png"
                                                    VerticalOptions="Start"
                                                    HorizontalOptions="Start"
                                                    BackgroundColor="Transparent"
                                                    Grid.Row="1"
                                                    IsEnabled="False"
                                                    IsVisible="False"
                                                    Grid.Column="2"
                                                    Margin="5,3,10,5"
                                                    HeightRequest="30"
                                                    WidthRequest="30"
                                                    BorderWidth="2"
                                                    Command="{Binding Source={x:Reference OrderPageName},Path = BindingContext.NewAppointmentCommand}"
                                                    CommandParameter="{Binding .}">
                                        
                                        <ImageButton.Triggers>
                                            <MultiTrigger TargetType="ImageButton">
                                                <MultiTrigger.Conditions>
                                                    <BindingCondition Binding="{Binding NutzeinheitState}" Value="{x:Static processStateEnum:ProcessState.Updating}"/>
                                                </MultiTrigger.Conditions>
                                                <Setter Property="IsEnabled" 
                                                        Value="True"/>
                                                <Setter Property="IsVisible"
                                                        Value="True"/>
                                            </MultiTrigger>
                                            
                                            <DataTrigger    TargetType="ImageButton"
                                                            Binding="{Binding HasNewAppointment}"
                                                            Value="True">
                                                <Setter Property="BackgroundColor"
                                                        Value="Green">
                                                        
                                                </Setter>
                                                
                                            </DataTrigger>
                                        </ImageButton.Triggers>
                                        
                                    </ImageButton>

                                </Grid>
                            </Frame>
                        </ViewCell>
                    </DataTemplate>
                </ListView.ItemTemplate>

            </ListView>

        </StackLayout>

    </ContentPage.Content>
    
</ContentPage>