<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:zxing="clr-namespace:ZXing.Net.Maui.Controls;assembly=ZXing.Net.MAUI.Controls"
             x:Class="Eras2AmwApp.MAUI.Views.QrScannerView"
             Title="QR Code Scanner">
    <Grid RowDefinitions="Auto,*,Auto">
        <Label
            Grid.Row="0"
            Text="Scan QR Code"
            FontSize="24"
            HorizontalOptions="Center"
            Margin="0,20,0,0" />

        <!-- Camera view for QR scanning -->
        <zxing:CameraBarcodeReaderView
            x:Name="barcodeReader"
            Grid.Row="1"
            BarcodesDetected="BarcodeReader_BarcodesDetected"
            IsDetecting="True"
            IsTorchOn="False"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

        </zxing:CameraBarcodeReaderView>

        <Button
            Grid.Row="2"
            Text="Cancel"
            Clicked="CancelButton_Clicked"
            HorizontalOptions="Center"
            Margin="0,0,0,20" />
    </Grid>
</ContentPage>
