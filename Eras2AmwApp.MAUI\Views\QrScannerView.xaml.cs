using System;
using System.Threading.Tasks;
using Microsoft.Maui.ApplicationModel;
using Microsoft.Maui.Controls;
using ZXing.Net.Maui;

namespace Eras2AmwApp.MAUI.Views
{
    public partial class QrScannerView : ContentPage
    {
        public event EventHandler<string>? QrCodeScanned;
        public event EventHandler? ScanCancelled;
        private bool hasScanned = false;

        public QrScannerView()
        {
            try
            {
                InitializeComponent();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing component: {ex.Message}");
            }
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            hasScanned = false;

            // Start the camera and barcode detection
            try
            {
                // Make sure the barcode reader is active
                if (barcodeReader != null)
                {
                    barcodeReader.IsDetecting = true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error starting camera: {ex.Message}");
            }
        }

        protected override void OnDisappearing()
        {
            base.OnDisappearing();

            // Stop the camera when the page disappears
            try
            {
                if (barcodeReader != null)
                {
                    barcodeReader.IsDetecting = false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error stopping camera: {ex.Message}");
            }
        }

        private void BarcodeReader_BarcodesDetected(object sender, BarcodeDetectionEventArgs e)
        {
            if (hasScanned)
                return;

            hasScanned = true;

            // Get the first detected barcode
            var barcode = e.Results[0];

            // Get the barcode value
            string result = barcode.Value;

            // Invoke the QrCodeScanned event with the result
            MainThread.BeginInvokeOnMainThread(() =>
            {
                QrCodeScanned?.Invoke(this, result);
            });
        }

        private void CancelButton_Clicked(object sender, EventArgs e)
        {
            ScanCancelled?.Invoke(this, EventArgs.Empty);
        }
    }
}

