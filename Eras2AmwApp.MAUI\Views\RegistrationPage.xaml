<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:Eras2AmwApp.MAUI.ViewModels"
             x:Class="Eras2AmwApp.MAUI.Views.RegistrationPage"
             Title="Registration">

    <!-- Uncomment this section if <PERSON><PERSON><PERSON> fails to resolve the ViewModel -->
    <!-- <ContentPage.BindingContext>
        <viewmodels:RegistrationPageViewModel />
    </ContentPage.BindingContext> -->

    <StackLayout Padding="20" Spacing="20" VerticalOptions="Center">

        <Label Text="{Binding AppTitle}"
               HorizontalOptions="Center"
               VerticalOptions="Center"
               TextColor="Black"
               FontSize="35" />

        <Label Text="Registrierung"
               HorizontalOptions="Center"
               VerticalOptions="Center"
               TextColor="Black"
               FontSize="32"
               Margin="0,0,0,20"/>

        <Button Margin="5,5,5,0"
                Text="QrCode Laden"
                Command="{Binding LoadQrCodeCommand}"
                BackgroundColor="#007AFF"
                TextColor="White"
                FontSize="18"
                CornerRadius="8"
                Padding="10" />

        <Button Margin="5,5,5,0"
                Text="Test Admin"
                Command="{Binding LoadTestAdminCommand}"
                BackgroundColor="#007AFF"
                TextColor="White"
                FontSize="18"
                CornerRadius="8"
                Padding="10" />

    </StackLayout>
</ContentPage>