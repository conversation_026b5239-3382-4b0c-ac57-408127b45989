using Eras2AmwApp.MAUI.ViewModels;
using System.Diagnostics;

namespace Eras2AmwApp.MAUI.Views;

[XamlCompilation(XamlCompilationOptions.Compile)]
public partial class RegistrationPage : ContentPage
{
    public RegistrationPage(RegistrationPageViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    // This constructor is for design-time support
    public RegistrationPage()
    {
        InitializeComponent();
        // This constructor should only be used by the XAML designer
        if (BindingContext == null && Application.Current != null)
        {
            Debug.WriteLine("Using default constructor - this should only happen at design time");
            // Don't set BindingContext here - it should be set by DI or XAML
        }
    }
}
