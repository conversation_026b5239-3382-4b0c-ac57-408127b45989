﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="BinaryDataInserter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Dal.Inserter
{
    using System;
    using System.IO;
    using System.Threading.Tasks;

    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    using Eras2AmwApp.WebService.Interfaces;

    using ServiceStack;

    public class BinaryDataInserter : IBinaryDataInserter
    {
        private readonly IResourceService resourceService;

        private readonly IDbContextFactory contextFactory;

        public BinaryDataInserter(IResourceService resourceService, IDbContextFactory contextFactory)
        {
            this.resourceService = resourceService ?? throw new ArgumentNullException(nameof(resourceService));
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
        }

        public async Task SaveNutzeinheitPhotoAsync(Photo photo, Stream stream)
        {
            if (photo == null)
            {
                throw new ArgumentNullException(nameof(photo));
            }

            if (stream == null)
            {
                throw new ArgumentNullException(nameof(stream));
            }

            if (!photo.NutzeinheitGuid.HasValue)
            {
                throw new ArgumentException(nameof(photo.DeviceGuid));
            }

            DirectoryInfo targetDirectory = resourceService.GetNutzeinheitPhotoDirectory(photo.NutzeinheitGuid.Value);
            targetDirectory.Create();

            string path = GetImageFilePath(targetDirectory, photo.Guid);

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(photo);

                photo.Path = path;
                photo.Name = Path.GetFileName(path);
                
                context.SaveChanges();
            }
            
            await SaveImageAsync(path, stream);
        }

        public async Task SaveDevicePhotoAsync(Photo photo, Stream stream)
        {
            if (photo == null)
            {
                throw new ArgumentNullException(nameof(photo));
            }

            if (stream == null)
            {
                throw new ArgumentNullException(nameof(stream));
            }

            if (!photo.DeviceGuid.HasValue)
            {
                throw new ArgumentException(nameof(photo.DeviceGuid));
            }

            DirectoryInfo targetDirectory = resourceService.GetDeviceFotoDirectory(photo.DeviceGuid.Value);
            targetDirectory.Create();

            string path = GetImageFilePath(targetDirectory, photo.Guid);

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(photo);

                photo.Path = path;
                photo.Name = Path.GetFileName(path);
                
                context.SaveChanges();
            }

            await SaveImageAsync(path, stream);
        }

        public async Task SaveNutzeinheitSignatureAsync(Signature signature, Stream stream)
        {
            if (signature == null)
            {
                throw new ArgumentNullException(nameof(signature));
            }

            if (stream == null)
            {
                throw new ArgumentNullException(nameof(stream));
            }

            DirectoryInfo targetDirectory = resourceService.GetSignatureDirectory(signature.NutzeinheitGuid);
            targetDirectory.Create();

            string path = GetImageFilePath(targetDirectory, signature.Guid);

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.Attach(signature);

                signature.Path = path;
                
                context.SaveChanges();
            }

            await SaveImageAsync(path, stream);
        }

        private string GetImageFilePath(DirectoryInfo targetDirectory, Guid guid)
        {
            if (targetDirectory == null)
            {
                throw new ArgumentNullException(nameof(targetDirectory));
            }

            return Path.Combine(targetDirectory.FullName, $"{ guid}.jpg");
        }

        private async Task SaveImageAsync(string path, Stream stream)
        {
            if (path == null)
            {
                throw new ArgumentNullException(nameof(path));
            }

            if (stream == null)
            {
                throw new ArgumentNullException(nameof(stream));
            }

            using (var ms = new FileStream(path, FileMode.Create))
            {
               await stream.WriteToAsync(ms);
            }
        }
    }
}