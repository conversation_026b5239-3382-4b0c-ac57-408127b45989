﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Configurations>Debug;Release;StandaloneDevelopment;Development;StandaloneRelease</Configurations>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='StandaloneDevelopment|AnyCPU'">
    <DefineConstants>DEVELOPMENT,STANDALONE_APP</DefineConstants>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Development|AnyCPU'">
    <DefineConstants>DEVELOPMENT</DefineConstants>
    <Optimize>true</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='StandaloneRelease|AnyCPU'">
    <Optimize>true</Optimize>
    <DefineConstants>TRACE;STANDALONE_APP</DefineConstants>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="Eras2Amw.Service.Client.App" Version="1.8.1" />
    <PackageReference Include="Eras2Amw.Service.Server.ServiceModel" Version="1.19.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Ninject" Version="3.3.6" />
    <PackageReference Include="Polly" Version="8.5.2" />
    <PackageReference Include="ServiceStack.Client" Version="8.2.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Endiancode.Utilities\Endiancode.Utilities.csproj" />
    <ProjectReference Include="..\Eras2AmwApp.Common\Eras2AmwApp.Common.csproj" />
    <ProjectReference Include="..\Eras2AmwApp.Database\Eras2AmwApp.Database.csproj" />
  </ItemGroup>

</Project>
