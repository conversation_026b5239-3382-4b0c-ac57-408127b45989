﻿namespace Eras2AmwApp.WebService.Implementations
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;
    using System.Net;
    using System.Threading.Tasks;
    using AutoMapper;

    using Common.Interfaces;

    using Dal.Inserter;

    using Database.Contexts;
    using Database.Interfaces;

    using Endiancode.Utilities.Extensions;

    using Eras2Amw.Service.Client.App;
    using Eras2Amw.Service.Client.App.Extensions;
    using Eras2Amw.Service.Client.App.Interfaces;
    using Eras2Amw.Service.Server.ServiceModel.Common;
    using Eras2Amw.Service.Server.ServiceModel.Eras2App.Add;
    using Eras2Amw.Service.Server.ServiceModel.Eras2App.Get;
    using Eras2Amw.Service.Server.ServiceModel.Eras2App.Update;
    using Eras2Amw.Service.Server.ServiceModel.Extensions;

    using Eras2AmwApp.Common.Exceptions;
    using Eras2AmwApp.Domain.Eras2Amw.Extensions;
    using Eras2AmwApp.Domain.Eras2App.Database;

    using EventArgs;

    using Interfaces;
    using Microsoft.EntityFrameworkCore;
    using Ninject;
    using Polly;
    using Polly.Timeout;
    using Serilog;

    using Services;

    using ServiceStack;

    using Dal = Eras2AmwApp.Domain.Eras2Amw.Models;

    public class AmwWebservice : IAmwWebservice, IStartable
    {
        private readonly ILogger logger;
        private readonly IMapper mapper;
        private readonly IWebserviceBackup webserviceBackup;
        private readonly IDbContextFactory contextFactory;
        private readonly IDomainLoader domainLoader;

        private readonly ISyncBroker syncBroker;

        private readonly IResourceService resourceService;
        private readonly IAbrechnungseinheitService abrechnungseinheitService;
        private readonly IOrderService orderService;
        private readonly INutzeinheitService nutzeinheitService;
        private readonly IStammdatenService stammdatenService;

        private IEras2AmwClient client;
        private string url;

        public AmwWebservice(IServiceLocator serviceLocator, IDbContextFactory contextFactory, IDomainLoader domainLoader, ISyncBroker syncBroker)
        {
            if (serviceLocator == null)
            {
                throw new ArgumentNullException(nameof(serviceLocator));
            }

            logger = serviceLocator.Logger;
            mapper = serviceLocator.Mapper;
            resourceService = serviceLocator.ResourceService;
            webserviceBackup = new WebserviceBackup(serviceLocator.AppSettings);
            this.contextFactory = contextFactory ?? throw new ArgumentNullException(nameof(contextFactory));
            this.domainLoader = domainLoader ?? throw new ArgumentNullException(nameof(domainLoader));
            this.syncBroker = syncBroker ?? throw new ArgumentNullException(nameof(syncBroker));

            abrechnungseinheitService = AbrechnungseinheitService.Create(mapper, serviceLocator.AppSettings, domainLoader);
            abrechnungseinheitService.DoBackup = BackupTransmission;
            
            nutzeinheitService = NutzeinheitService.Create(mapper, serviceLocator.AppSettings, domainLoader);
            nutzeinheitService.DoBackup = BackupTransmission;

            orderService = OrderService.Create(mapper, serviceLocator.AppSettings, domainLoader);
            orderService.DoBackup = BackupTransmission;

            stammdatenService = StammdatenService.Create(mapper, serviceLocator.AppSettings);
        }

        public event EventHandler<WebserviceEventArgs> Info;

        public event EventHandler<WebserviceEventArgs> Warning;
        
        public event EventHandler<WebserviceEventArgs> Error;

        public bool BackupTransmission { get; set; } = false;

        public bool IsServerNotAvailableStatusError(WebException exp)
        {
            return exp.Status == WebExceptionStatus.ConnectFailure ||
                   exp.Status == WebExceptionStatus.NameResolutionFailure ||
                   exp.Status == WebExceptionStatus.Timeout; 
        }

        public void Start()
        {
            try
            {
                using (Eras2AppContext context = contextFactory.CreateApp())
                {
                    Webservice webservice = context.Webservices.SingleOrDefault();

                    if (webservice == null)
                    {
                        throw new Eras2AmwException("Der Webservice kann nicht erzeugt (gestartet) werden, da kein Kunde registriert ist.");
                    }

                    url = webservice.Url;
                    client = new Eras2AmwClient(url);
                }
            }
            catch (Exception e)
            {
                logger.ForContext<AmwWebservice>().Error(e, "Webservice Startup failed.");
                throw;
            }
        }

        public void Stop()
        {
            client = null;
        }

        public async Task<Guid> LoginAsync(string username, string password)
        {
            if (username == null)
            {
                throw new ArgumentNullException(nameof(username));
            }

            if (password == null)
            {
                throw new ArgumentNullException(nameof(password));
            }

            if (client == null)
            {
                throw new InvalidOperationException(nameof(client));
            }

            return await client.LoginAsync(username, password).ConfigureAwait(false);
        }

        public async Task LogoutAsync()
        {
            await client.LogoutAsync().ConfigureAwait(false);
        }

        public async Task SyncOrdersAsync(Guid userGuid)
        {
            if (client == null)
            {
                throw new InvalidOperationException(nameof(client));
            }

            TriggerOnInfo("Starte Daten-Synchronisation...");

            try
            {
                TriggerOnInfo("Sende Daten...");
                await UploadOrdersAsync(userGuid).ConfigureAwait(false);
                RecreateDatabase();
            }
            catch (WebException e)
            {
                logger.ForContext<AmwWebservice>().Error("SyncOrders UploadOrdersAsync failed: WebException! Status: {Status} Message: {Message}", e.Status, e.Message);
                TriggerOnError("Die Daten-Synchronisation ist fehlgeschlagen. Die Daten konnten nicht verschickt werden.");
                throw;
            }
            catch (WebServiceException e)
            {
                logger.ForContext<AmwWebservice>().Error("SyncOrders UploadOrdersAsync failed: WebServiceException ! Status: {ResponseStatus} ErrorMessage: {ErrorMessage} ResponseBody: {ResponseBody} {}", e.ResponseStatus, e.ErrorMessage, e.ResponseBody);
                TriggerOnError("Die Daten-Synchronisation ist fehlgeschlagen. Die Daten konnten nicht verschickt werden.");
                throw;
            }
            catch (Exception e)
            {
                logger.ForContext<AmwWebservice>().Error(e, $"SyncOrders UploadOrdersAsync failed: Exception! : { string.Join(";", e.GetInnerExceptions().Select(x => x.Message))}");
                TriggerOnError("Die Daten-Synchronisation ist fehlgeschlagen. Die Daten konnten nicht verschickt werden.");
                throw;
            }

            
            
            try
            {
                TriggerOnInfo("Empfange Daten...");
                await DownloadOrdersAsync(userGuid).ConfigureAwait(false);
                TriggerOnInfo("Daten Synchronisation abgeschlossen.");
            }
            catch (WebException e)
            {
                logger.ForContext<AmwWebservice>().Error("SyncOrders DownloadOrdersAsync failed: WebException! Status: {Status} Message: {Message}", e.Status, e.Message);
                TriggerOnError("Die Daten-Synchronisation ist fehlgeschlagen. Die Daten konnten nicht empfangen werden.");
                throw;
            }
            catch (WebServiceException e)
            {
                logger.ForContext<AmwWebservice>().Error("SyncOrders DownloadOrdersAsync failed: WebServiceException! Status: {ResponseStatus} ErrorMessage: {ErrorMessage} ResponseBody: {ResponseBody} {}", e.ResponseStatus, e.ErrorMessage, e.ResponseBody);
                TriggerOnError("Die Daten-Synchronisation ist fehlgeschlagen. Die Daten konnten nicht empfangen werden.");
                throw;
            }
            catch (Exception e)
            {
                logger.ForContext<AmwWebservice>().Error(e, $"SyncOrders DownloadOrdersAsync failed: Inner Exceptions: { string.Join(";", e.GetInnerExceptions().Select(x => x.Message))}");
                TriggerOnError("Die Daten-Synchronisation ist fehlgeschlagen. Die Daten konnten nicht empfangen werden.");
            }
        }

        public async Task SyncFullAmwAppDatabaseAsync(string databasePath)
        {
            if (client == null)
            {
                throw new InvalidOperationException(nameof(client));
            }

            try
            {
                TriggerOnInfo("Sende Daten...");
                await UploadFullAmwAppDatabaseAsync(databasePath).ConfigureAwait(false);
            }
            catch (WebException e)
            {
                logger.ForContext<AmwWebservice>().Error("SyncFullAmwAppDatabaseAsync failed: WebException! Status: {Status} Message: {Message}", e.Status, e.Message);
                TriggerOnError("Die Datenbank-Synchronisation ist fehlgeschlagen. Die Daten konnten nicht verschickt werden.");
                throw;
            }
            catch (WebServiceException e)
            {
                logger.ForContext<AmwWebservice>().Error("SyncFullAmwAppDatabaseAsync failed: WebServiceException ! Status: {ResponseStatus} ErrorMessage: {ErrorMessage} ResponseBody: {ResponseBody} {}", e.ResponseStatus, e.ErrorMessage, e.ResponseBody);
                TriggerOnError("Die Datenbank-Synchronisation ist fehlgeschlagen. Die Daten konnten nicht verschickt werden.");
                throw;
            }
            catch (Exception e)
            {
                logger.ForContext<AmwWebservice>().Error(e, $"SyncFullAmwAppDatabaseAsync UploadOrdersAsync failed: Exception! : { string.Join(";", e.GetInnerExceptions().Select(x => x.Message))}");
                TriggerOnError("Die Datenbank-Synchronisation ist fehlgeschlagen. Die Daten konnten nicht verschickt werden.");
                throw;
            }
        }

        public async Task DownloadNutzeinheitPhotoAsync(Guid userGuid, Dal.Photo photo)
        {
            if (photo == null)
            {
                throw new ArgumentNullException(nameof(photo));
            }

            if (client == null)
            {
                throw new InvalidOperationException(nameof(client));
            }

            var getNutzeinheitPhoto = new GetNutzeinheitPhoto
            {
                Guid = photo.Guid,
                UserId = userGuid
            };

            TriggerOnInfo($"Empfange Nutzeinheit Foto {photo.Guid}");
            using (Stream stream = await client.GetNutzeinheitPhotoAsync(getNutzeinheitPhoto).ConfigureAwait(false))
            {
                var inserter = new BinaryDataInserter(resourceService, contextFactory);
                await inserter.SaveNutzeinheitPhotoAsync(photo, stream);
            }
        }

        public async Task DownloadDevicePhotoAsync(Guid userGuid, Dal.Photo photo)
        {
            if (photo == null)
            {
                throw new ArgumentNullException(nameof(photo));
            }

            if (client == null)
            {
                throw new InvalidOperationException(nameof(client));
            }

            var getDevicePhoto = new GetDevicePhoto
            {
                Guid = photo.Guid,
                UserId = userGuid
            };

            TriggerOnInfo($"Empfange Nutzeinheit Device Foto {photo.Guid}");
            using (Stream stream = await client.GetDevicePhotoAsync(getDevicePhoto).ConfigureAwait(false))
            {
                var inserter = new BinaryDataInserter(resourceService, contextFactory);
                await inserter.SaveDevicePhotoAsync(photo, stream);
            }
        }

        public async Task DownloadNutzeinheitSignatureAsync(Guid userGuid, Dal.Signature signature)
        {
            if (signature == null)
            {
                throw new ArgumentNullException(nameof(signature));
            }

            if (client == null)
            {
                throw new InvalidOperationException(nameof(client));
            }

            var getSignature = new GetSignature
                                   {
                                        Guid = signature.Guid,
                                        UserId = userGuid
                                   };

            TriggerOnInfo($"Empfange Nutzeinheit Unterschrift {signature.Guid}");
            using (Stream stream = await client.GetSignatureAsync(getSignature).ConfigureAwait(false))
            {
                var inserter = new BinaryDataInserter(resourceService, contextFactory);
                await inserter.SaveNutzeinheitSignatureAsync(signature, stream);
            }
        }

        public async Task SyncLiveNutzeinheitAsync()
        {
            if (client == null)
            {
                throw new InvalidOperationException(nameof(client));
            }

            try
            {
                User user = GetAppUser();

                AsyncTimeoutPolicy timeOutPolicy = Policy.TimeoutAsync(30, TimeoutStrategy.Pessimistic);

                await timeOutPolicy.ExecuteAsync(async () =>  await LoginAsync(user.Name, user.Password).ConfigureAwait(false));
                var ordersToUpload = new List<(Guid, Guid)>();

                foreach ((Guid , Guid) item in GetNutzeinheitReadySync())
                {
                    await SyncLiveNutzeinheitAsync(user.Guid, item.Item1, item.Item2, ordersToUpload).ConfigureAwait(false);
                }

                List<Order> alreadyUploadedOrders = new List<Order>();

                foreach ((Guid, Guid) nutzeinheitOrder in ordersToUpload)
                {
                    Order dalMainOrder = mapper.Map<Order>(domainLoader.LoadOrder(nutzeinheitOrder.Item2));

                    if (!alreadyUploadedOrders.Contains(dalMainOrder))
                    {
                        await UploadOrderAsync(user.Guid, dalMainOrder).ConfigureAwait(false);
                        alreadyUploadedOrders.Add(dalMainOrder);
                    }

                    await UploadSignaturesAsync(nutzeinheitOrder.Item2, nutzeinheitOrder.Item1, user.Guid);
                }

                await SyncLiveNotes(user.Guid).ConfigureAwait(false);;
            }
            catch (WebException e)
            {
                logger.ForContext<AmwWebservice>().Error("SyncLiveNutzeinheitAsync WebException! Status: {Status} Message: {Message}", e.Status, e.Message);
                throw;
            }
            catch (WebServiceException e)
            {
                logger.ForContext<AmwWebservice>().Error("SyncLiveNutzeinheitAsync WebServiceException ! Status: {ResponseStatus} ErrorMessage: {ErrorMessage} ResponseBody: {ResponseBody} {}", e.ResponseStatus, e.ErrorMessage, e.ResponseBody);
                throw;
            }
            catch (Exception e)
            {
                logger.ForContext<AmwWebservice>().Error(e, $"SyncLiveNutzeinheitAsync SyncLiveNotes failed: Exception! : { string.Join(";", e.GetInnerExceptions().Select(x => x.Message))}");
                throw;
            }
        }

        public async Task SyncLiveNutzerEditAsync(Guid nutzeinheitGuid)
        {
            if (client == null)
            {
                throw new InvalidOperationException(nameof(client));
            }
            try
            {
                User user = GetAppUser();

                AsyncTimeoutPolicy timeOutPolicy = Policy.TimeoutAsync(30, TimeoutStrategy.Pessimistic);

                await timeOutPolicy.ExecuteAsync(async () => await LoginAsync(user.Name, user.Password).ConfigureAwait(false));

                TriggerOnInfo("Starte Nutzeinheit Synchronisation...");
                using (Eras2AmwContext context = contextFactory.CreateAmw())
                {
                    InitServicesWithContext(context);

                    Dal.Nutzeinheit dalNutzeinheit = domainLoader.LoadNutzeinheitWithDevices(nutzeinheitGuid);
                    Nutzeinheit nutzeinheit = mapper.Map<Nutzeinheit>(dalNutzeinheit);

                    bool sent = false;
                    sent = await UploadNutzeinheitOnlyOnceAsync(user.Guid, nutzeinheit).ConfigureAwait(false);

                    TriggerOnInfo("Nutzeinheit SyncLiveDone...");

                    if (sent)
                    {
                        if (dalNutzeinheit == null)
                        {
                            throw new ArgumentNullException(nameof(dalNutzeinheit));
                        }

                        if (dalNutzeinheit.Photos.Any(x => x.CreatedByApp))
                        {
                            IEnumerable<Dal.Photo> photos = dalNutzeinheit.Photos.Where(x => x.CreatedByApp);
                            await SendNutzeinheitPhotosAsync(user.Guid, photos).ConfigureAwait(false);
                        }
                    }
                    nutzeinheitService.NutzerSyncLiveDone(nutzeinheitGuid);
                    context.SaveChanges();
                }
                TriggerOnInfo("Nutzeinheit Synchronisation abgeschlossen.");
            }
            catch (WebException e)
            {
                logger.ForContext<AmwWebservice>().Error("SyncLiveNutzerAsync WebException! Status: {Status} Message: {Message}", e.Status, e.Message);
                throw;
            }
            catch (WebServiceException e)
            {
                logger.ForContext<AmwWebservice>().Error("SyncLiveNutzerAsync WebServiceException ! Status: {ResponseStatus} ErrorMessage: {ErrorMessage} ResponseBody: {ResponseBody} {}", e.ResponseStatus, e.ErrorMessage, e.ResponseBody);
                throw;
            }
            catch (Exception e)
            {
                logger.ForContext<AmwWebservice>().Error(e, $"SyncLiveNutzerAsync SyncLiveNotes failed: Exception! : {string.Join(";", e.GetInnerExceptions().Select(x => x.Message))}");
                throw;
            }
        }

        protected virtual void OnInfo(WebserviceEventArgs e)
        {
            Info?.Invoke(this, e);
        }

        protected virtual void OnWarning(WebserviceEventArgs e)
        {
            Warning?.Invoke(this, e);
        }

        protected virtual void OnError(WebserviceEventArgs e)
        {
            Error?.Invoke(this, e);
        }

        private User GetAppUser()
        {
            using (Eras2AppContext context = contextFactory.CreateApp())
            {
                return context.Users.Single();
            }
        }

        private List<(Guid, Guid)> GetNutzeinheitReadySync()
        {
            var tuple = new List<(Guid, Guid)>();
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                foreach (Dal.NutzeinheitOrderState nutzeinheitOrderState in context.NutzeinheitOrderStates)
                {
                    if (nutzeinheitOrderState.IsCreating() || nutzeinheitOrderState.IsUpdating())
                    {
                        Guid orderGuid = nutzeinheitOrderState.OrderGuid;
                        Guid nutzeinheitGuid = nutzeinheitOrderState.NutzeinheitGuid;

                        tuple.Add((orderGuid, nutzeinheitGuid));
                    }
                }
            }

            return tuple;
        }

        private async Task SyncLiveNotes(Guid userGuid)
        {
            TriggerOnInfo("Starte Anmerkungen Synchronisation...");
            
            GetNotesResponse response = await client.GetNotesAsync(new GetNotes { UserId = userGuid }).ConfigureAwait(false);;

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                foreach (Order order in response.Orders)
                {
                    Dal.Order dalOrder = context.Orders.Find(order.Guid);
                    if (syncBroker.IsUpdateNeeded(dalOrder.LastModified, order.LastModified.Value))
                    {
                        dalOrder.Note = order.Note;
                    }
                }

                foreach (Abrechnungseinheit abrechnungseinheit in response.Abrechnungseinheiten)
                {
                    Dal.Abrechnungseinheit dalAbrechnungseinheit = context.Abrechnungseinheiten.Find(abrechnungseinheit.Guid);
                    if (syncBroker.IsUpdateNeeded(dalAbrechnungseinheit.LastModified.Value, abrechnungseinheit.LastModified.Value))
                    {
                        dalAbrechnungseinheit.Note = abrechnungseinheit.Note;
                    }
                }

                foreach (Nutzeinheit nutzeinheit in response.Nutzeinheiten)
                {
                    Dal.Nutzeinheit dalNutzeinheit = context.Nutzeinheiten.Find(nutzeinheit.Guid);
                    
                    if(dalNutzeinheit == null)
                    {
                        continue;
                    }

                    if (syncBroker.IsUpdateNeeded(dalNutzeinheit.LastModified.Value, nutzeinheit.LastModified.Value))
                    {
                        dalNutzeinheit.Note = nutzeinheit.Note;
                    }
                }

                context.SaveChanges();
            }
            
            TriggerOnInfo("Anmerkungen Synchronisation abgeschlossen.");
        }

        private async Task<Nutzeinheit> SyncLiveNutzeinheitAsync(Guid userGuid, Guid orderGuid, Guid nutzeinheitGuid, List<(Guid,Guid)> ordersToUpload)
        {
            TriggerOnInfo("Starte Nutzeinheit Synchronisation...");

            Nutzeinheit addedNutzeinheit = new Nutzeinheit();

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                InitServicesWithContext(context);

                NutzeinheitOrderState nutzeinheitOrderState = mapper.Map<NutzeinheitOrderState>(context.NutzeinheitOrderStates.Find(orderGuid, nutzeinheitGuid));

                await SendNutzeinheit(userGuid, nutzeinheitOrderState);

                TriggerOnInfo("Nutzeinheit SyncLiveDone...");
                nutzeinheitService.NutzeinheitSyncLiveDone(nutzeinheitGuid);

                ordersToUpload.Add((nutzeinheitGuid, orderGuid));

                context.SaveChanges();
            }

            TriggerOnInfo("Nutzeinheit Synchronisation abgeschlossen.");

            return addedNutzeinheit;
        }

        private async Task UploadSignaturesAsync(Guid orderGuid, Guid nutzeinheitGuid, Guid userGuid)
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                InitServicesWithContext(context);
                Dal.Signature signature = context.Signatures.SingleOrDefault(
                        x => x.OrderGuid == orderGuid && x.NutzeinheitGuid == nutzeinheitGuid && x.CreatedByApp
                    );
                if (signature != null)
                {
                    await SendSignatureAsync(userGuid, signature).ConfigureAwait(false);
                }

                TriggerOnInfo("Auftrag SyncLiveDone...");
                orderService.SyncLiveDone(orderGuid, nutzeinheitGuid);

                context.SaveChanges();
            }
        }

        private void RecreateDatabase()
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                context.RecreateDatabase();
            }
        }

        private void TriggerOnInfo(string message)
        {
            if (message == null)
            {
                throw new ArgumentNullException(nameof(message));
            }

            OnInfo(new WebserviceEventArgs(message));
        }

        private void TriggerOnWarning(string message)
        {
            if (message == null)
            {
                throw new ArgumentNullException(nameof(message));
            }

            OnWarning(new WebserviceEventArgs(message));
        }

        private void TriggerOnError(string message)
        {
            if (message == null)
            {
                throw new ArgumentNullException(nameof(message));
            }

            OnError(new WebserviceEventArgs(message));
        }

        private async Task DownloadOrdersAsync(Guid userGuid)
        {
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                InitServicesWithContext(context);

                await DownloadStammdatenAsync(userGuid).ConfigureAwait(false);
                IList<OrderDescriptor> orderDescriptors = await GetOrderDescriptorsAsync(userGuid).ConfigureAwait(false);

                foreach (OrderDescriptor orderDescriptor in orderDescriptors)
                {
                    await DownloadAbrechnungseinheitAsync(userGuid, orderDescriptor.AbrechnungseinheitGuid).ConfigureAwait(false);
                    await DownloadNutzeinheitenAsync(userGuid, orderDescriptor.NutzeinheitGuids).ConfigureAwait(false);
                    await DownloadOrderAsync(userGuid, orderDescriptor.OrderGuid).ConfigureAwait(false);
                }

                TriggerOnInfo("Die Datenbank wird erstellt ...");
                context.SaveChanges();
            }
        }

        private void InitServicesWithContext(Eras2AmwContext context)
        {
            abrechnungseinheitService.DatabaseContext = context;
            orderService.DatabaseContext = context;
            nutzeinheitService.DatabaseContext = context;
            stammdatenService.DatabaseContext = context;
        }

        private async Task UploadFullAmwAppDatabaseAsync(string databasePath)
        {
            if (client == null)
            {
                throw new InvalidOperationException(nameof(client));
            }

            FileInfo uploadFile = new FileInfo(databasePath);

            if (!uploadFile.Exists)
            {
                throw new IOException("Fehlende Datenbankdatei!!");
            }

            var eras2File = new AddFullBackup();
            await client.AddEras2AmwAppBackupAsync(eras2File, uploadFile).ConfigureAwait(false); ;
        }

        private async Task UploadOrdersAsync(Guid userGuid)
        {
            if (client == null)
            {
                throw new InvalidOperationException(nameof(client));
            }

            var abrechnungseinheitenUploaded = new List<Guid>();
            var nutzeinheitenUploaded = new List<Guid>();

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                InitServicesWithContext(context);

                List<Order> webServiceOrderList = await GetExistingWebServiceOrders(userGuid).ConfigureAwait(false);

                IEnumerable<Order> deviceOrderEnum = await orderService.LoadReadyToSendAsync().ConfigureAwait(false);
                List<Order> deviceOrderList = deviceOrderEnum.ToList();

                List<Guid> listOfExistingGuids = deviceOrderList.Select(x => x.Guid).Intersect(webServiceOrderList.Select(y => y.Guid)).ToList();
                List<Order> ordersToTransmit = new List<Order>();

                foreach (Guid guid in listOfExistingGuids)
                {
                    Order order = deviceOrderList.Where(x => x.Guid == guid).SingleOrDefault();
                    ordersToTransmit.Add(order);
                }

                foreach (Order order in ordersToTransmit)
                {
                    if (!abrechnungseinheitenUploaded.Contains(order.AbrechnungseinheitGuid))
                    {
                        await UploadAbrechnungseinheitOnlyOnceAsync(userGuid, order.AbrechnungseinheitGuid).ConfigureAwait(false);
                        abrechnungseinheitenUploaded.Add(order.AbrechnungseinheitGuid);
                    }

                    foreach (NutzeinheitOrderState nutzeinheitState in nutzeinheitService.LoadReadyToSendAsync(order))
                    {
                        if (!nutzeinheitenUploaded.Contains(nutzeinheitState.NutzeinheitGuid))
                        {
                            await SendNutzeinheit(userGuid, nutzeinheitState).ConfigureAwait(false);
                            nutzeinheitenUploaded.Add(nutzeinheitState.NutzeinheitGuid);
                        }
                    }
                    
                    bool sent = await UploadOrderAsync(userGuid, order).ConfigureAwait(false);

                    if (sent)
                    {
                        var signatures = context.Signatures.Where(x => x.OrderGuid == order.Guid && x.CreatedByApp);
                        await SendSignaturesAsync(userGuid, signatures).ConfigureAwait(false);
                    }
                }
            }
        }

        private async Task SendNutzeinheit(Guid userGuid, NutzeinheitOrderState nutzeinheitState)
        {
            if (nutzeinheitState == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheitState));
            }

            Dal.Nutzeinheit dalNutzeinheit = domainLoader.LoadNutzeinheitWithDevices(nutzeinheitState.NutzeinheitGuid);

            Nutzeinheit nutzeinheit = mapper.Map<Nutzeinheit>(dalNutzeinheit);

            bool sent = false;

            if (nutzeinheitState.IsUpdating())
            {
                sent = await UploadNutzeinheitOnlyOnceAsync(userGuid, nutzeinheit).ConfigureAwait(false);
            }
            else if (nutzeinheitState.IsCreating())
            {
                Nutzeinheit addedNutzeinheit = await AddNutzeinheitOnlyOnceAsync(userGuid, nutzeinheit);
                if(addedNutzeinheit.Guid != Guid.Empty && addedNutzeinheit.Guid != null)
                {
                    sent = true;
                }
            }

            if (sent)
            {
                await SendNutzeinheitBinaryDataAsync(userGuid, dalNutzeinheit).ConfigureAwait(false);
            }

            return;
        }

        private async Task<List<Order>> GetExistingWebServiceOrders(Guid userGuid)
        {
            TriggerOnInfo($"Empfangen Online Aufträge...");
            GetOrderListResponse webserviceOrders = await client.GetOrderListAsync(userGuid).ConfigureAwait(false);

            return webserviceOrders.Orders;
        }

        private async Task SendNutzeinheitBinaryDataAsync(Guid userGuid, Dal.Nutzeinheit dalNutzeinheit)
        {
            if (dalNutzeinheit == null)
            {
                throw new ArgumentNullException(nameof(dalNutzeinheit));
            }

            if (dalNutzeinheit.Photos.Any(x => x.CreatedByApp))
            {
                IEnumerable<Dal.Photo> photos = dalNutzeinheit.Photos.Where(x => x.CreatedByApp);
                await SendNutzeinheitPhotosAsync(userGuid, photos).ConfigureAwait(false);
            }

            if (dalNutzeinheit.Devices.Any(x => x.Photos.Any(y => y.CreatedByApp)))
            {
                IEnumerable<Dal.Photo> photos = dalNutzeinheit.Devices.SelectMany(x => x.Photos.Where(y => y.CreatedByApp));
                await SendDevicePhotosAsync(userGuid, photos).ConfigureAwait(false);
            }
        }

        private async Task SendNutzeinheitPhotosAsync(Guid userGuid, IEnumerable<Dal.Photo> photos)
        {
            if (photos == null)
            {
                throw new ArgumentNullException(nameof(photos));
            }

            foreach (Dal.Photo photo in photos)
            {
                var addNutzeinheitPhoto = new AddNutzeinheitPhoto
                                              {
                                                  Guid = photo.Guid,
                                                  UserId = userGuid
                                              };
                
                TriggerOnInfo($"Post Photo {photo.Guid}");
                await client.AddNutzeinheitPhotoAsync(addNutzeinheitPhoto, new FileInfo(photo.Path)).ConfigureAwait(false);
            }
        }

        private async Task SendDevicePhotosAsync(Guid userGuid, IEnumerable<Dal.Photo> photos)
        {
            if (photos == null)
            {
                throw new ArgumentNullException(nameof(photos));
            }

            foreach (Dal.Photo photo in photos)
            {
                var addDevicePhoto = new AddDevicePhoto()
                                              {
                                                  Guid = photo.Guid,
                                                  UserId = userGuid
                                              };

                await client.AddDevicePhotoAsync(addDevicePhoto, new FileInfo(photo.Path)).ConfigureAwait(false);
            }
        }

        private async Task SendSignaturesAsync(Guid userGuid, IEnumerable<Dal.Signature> signatures)
        {
            if (signatures == null)
            {
                throw new ArgumentNullException(nameof(signatures));
            }

            foreach (Dal.Signature signature in signatures)
            {
                await SendSignatureAsync(userGuid, signature);
            }
        }

        private async Task SendSignatureAsync(Guid userGuid, Dal.Signature signature)
        {
            if (signature == null)
            {
                throw new ArgumentNullException(nameof(signature));
            }
            
            var addSignature = new AddSignature()
            {
                Guid = signature.Guid,
                UserId = userGuid
            };

            TriggerOnInfo($"Post Signature {signature.Guid}");
            await client.AddSignatureAsync(addSignature, new FileInfo(signature.Path)).ConfigureAwait(false);
        }

        private async Task<bool> UploadNutzeinheitOnlyOnceAsync(Guid userGuid, Nutzeinheit nutzeinheit)
        {
            if (BackupTransmission)
            {
                await webserviceBackup.BackupUploadNutzeinheit(nutzeinheit).ConfigureAwait(false);
            }

            bool sent = false;
            try
            {
                TriggerOnInfo($"Put Nutzeinheit {nutzeinheit.Guid}");
                await client.UpdateNutzeinheitAsync(new UpdateNutzeinheit
                {
                    Nutzeinheit = nutzeinheit, 
                    UserId = userGuid
                }).ConfigureAwait(false);
                sent = true;
            }
            catch (WebServiceException exp) when (exp.IsNutzeinheitNotFound())
            {
                var msg = $"Die Nutzeinheit {nutzeinheit.Guid} ist dem Zielsystem nicht mehr bekannt. Die Daten dieser Nutzeinheit konnten nicht verschickt werden.";
                logger.ForContext<AmwWebservice>().Warning(msg);
                TriggerOnWarning(msg);
                TriggerOnInfo($"Post Nutzeinheit {nutzeinheit.Guid}...");
                AddNutzeinheitResponse addNutzeinheitResponse = await client.AddNutzeinheitAsync(new AddNutzeinheit
                {
                    Nutzeinheit = nutzeinheit,
                    UserId = userGuid
                });
            }
            catch (WebServiceException exp) when (exp.IsTechnicianNotAuthorized())
            {
                var msg = $"Der Monteur {userGuid} hat keinen Zugriff mehr auf die Nutzeinheit {nutzeinheit.Guid}. Die Daten dieser Nutzeinheit konnten nicht verschickt werden.";
                logger.ForContext<AmwWebservice>().Warning(msg);
                TriggerOnWarning(msg);
            }

            return sent;
        }

        private async Task<Nutzeinheit> AddNutzeinheitOnlyOnceAsync(Guid userGuid, Nutzeinheit nutzeinheit)
        {
            if (BackupTransmission)
            {
                await webserviceBackup.BackupAddNutzeinheit(nutzeinheit).ConfigureAwait(false);
            }

            Nutzeinheit addedNutzeinheit = new Nutzeinheit();
            try
            {
                TriggerOnInfo($"Post Nutzeinheit {nutzeinheit.Guid}...");
                AddNutzeinheitResponse addNutzeinheitResponse = await client.AddNutzeinheitAsync(new AddNutzeinheit
                {
                    Nutzeinheit = nutzeinheit, 
                    UserId = userGuid
                });

                addedNutzeinheit = addNutzeinheitResponse.Nutzeinheit;
            }
            catch (WebServiceException exp) when (exp.IsAbrechnungseinheitNotFound())
            {
                var msg = $"Die Abrechnungseinheit {nutzeinheit.AbrechnungseinheitGuid} der Nutzeinheit {nutzeinheit.Guid} ist dem Zielsystem nicht mehr bekannt. Die Daten dieser Nutzeinheit konnten nicht verschickt werden.";
                logger.ForContext<AmwWebservice>().Warning(msg);
                TriggerOnWarning(msg);
            }
            catch (WebServiceException exp) when (exp.IsNutzeinheitFound())
            {
                var msg = $"Die Nutzeinheit {nutzeinheit.Guid} wurde bereits angelegt und konnte nicht noch einmal verschickt werden.";
                logger.ForContext<AmwWebservice>().Warning(msg);
                TriggerOnWarning(msg);
            }
            catch (WebServiceException exp) when (exp.IsTechnicianNotAuthorized())
            {
                var msg = $"Der Monteur {userGuid} hat keinen Zugriff mehr auf die Abrechnungseinheit {nutzeinheit.AbrechnungseinheitGuid}. Die Daten dieser (Add)Nutzeinheit konnten nicht verschickt werden.";
                logger.ForContext<AmwWebservice>().Warning(msg);
                TriggerOnWarning(msg);
            }

            return addedNutzeinheit;
        }

        private async Task UploadAbrechnungseinheitOnlyOnceAsync(Guid userGuid, Guid guid)
        {
            Abrechnungseinheit abrechnungseinheit = await abrechnungseinheitService.LoadAsync(guid);

            try
            {
                TriggerOnInfo($"Sende Abrechnungseinheit {abrechnungseinheit.Guid}");
                await client.UpdateAbrechnungseinheitAsync(new UpdateAbrechnungseinheit
                {
                    Abrechnungseinheit = abrechnungseinheit, 
                    UserId = userGuid
                }).ConfigureAwait(false);
            }
            catch (WebServiceException exp) when (exp.IsAbrechnungseinheitNotFound())
            {
                var msg = $"Die Abrechnungseinheit {abrechnungseinheit.Guid} ist dem Zielsystem nicht mehr bekannt. Die Daten dieser Abrechnungseinheit konnten nicht verschickt werden.";
                logger.ForContext<AmwWebservice>().Warning(msg);
                TriggerOnWarning(msg);
            }
            catch (WebServiceException exp) when (exp.IsTechnicianNotAuthorized())
            {
                var msg = $"Der Monteur {userGuid} hat keinen Zugriff mehr auf die Abrechnungseinheit {abrechnungseinheit.Guid}. Die Daten dieser Abrechnungseinheit konnten nicht verschickt werden.";
                logger.ForContext<AmwWebservice>().Warning(msg);
                TriggerOnWarning(msg);
            }
        }

        private async Task<bool> UploadOrderAsync(Guid userGuid, Order order)
        {
            if (order == null)
            {
                throw new ArgumentNullException(nameof(order));
            }

            bool sent = false;
            try
            {
                TriggerOnInfo($"Sende Auftrag {order.Guid} ...");
                await client.UpdateOrderAsync(
                    new UpdateOrder
                        {
                            Order = order,
                            UserId = userGuid
                        }
                ).ConfigureAwait(false);
                sent = true;
            }
            catch (WebServiceException exp) when (exp.IsOrderNotFound())
            {
                var msg =
                    $"Der Auftrag {order.Guid} ist dem Zielsystem nicht mehr bekannt. Die Daten dieses Auftrags konnten nicht verschickt werden.";
                logger.ForContext<AmwWebservice>().Warning(msg);
                TriggerOnWarning(msg);
            }
            catch (WebServiceException exp) when (exp.IsNutzeinheitNotFound())
            {
                var msg =
                    $"Eine Nutzeinheit in einer NutzeinheitOrderPosition des Auftrags {order.Guid} ist dem Zielsystem nicht mehr bekannt. Die Daten diesen Auftrags konnten nicht verschickt werden.";
                logger.ForContext<AmwWebservice>().Warning(msg);
                TriggerOnWarning(msg);
            }

            return sent;
        }

        private async Task DownloadStammdatenAsync(Guid userGuid)
        {
            TriggerOnInfo("Empfange Stammdaten ...");
            GetStammdatenResponse stammdaten = await client.GetStammdatenAsync(userGuid).ConfigureAwait(false);

            TriggerOnInfo("Stammdaten Datenbank update ...");
            await stammdatenService.Insert(stammdaten);
        }

        private async Task DownloadAbrechnungseinheitAsync(Guid userGuid, Guid abrechnungseinheitGuid)
        {
            if (abrechnungseinheitService.Exists(abrechnungseinheitGuid))
            {
                return;
            }

            TriggerOnInfo($"Empfange Abrechnungseinheit {abrechnungseinheitGuid}");
            GetAbrechnungseinheitResponse abrechnungseinheitResponse = await client.GetAbrechnungseinheitAsync(userGuid, abrechnungseinheitGuid).ConfigureAwait(false);

            await abrechnungseinheitService.Insert(abrechnungseinheitResponse.Abrechnungseinheit);
        }

        private async Task DownloadNutzeinheitAsync(Guid userGuid, Guid nutzeinheitGuid)
        {
            if (nutzeinheitService.Exists(nutzeinheitGuid))
            {
                return;
            }

            TriggerOnInfo($"Empfange Nutzeinheit {nutzeinheitGuid}");
            GetNutzeinheitResponse nutzeinheitResponse = await client.GetNutzeinheitAsync(userGuid, nutzeinheitGuid).ConfigureAwait(false);
            
            await nutzeinheitService.Insert(nutzeinheitResponse.Nutzeinheit);
        }

        private async Task DownloadOrderAsync(Guid userGuid, Guid orderGuid)
        {
            TriggerOnInfo($"Empfange Auftrag {orderGuid}");
            GetOrderResponse orderResponse = await client.GetOrderAsync(userGuid, orderGuid).ConfigureAwait(false);

            await orderService.Insert(orderResponse.Order);
        }

        private async Task DownloadNutzeinheitenAsync(Guid userGuid, IEnumerable<Guid> nutzeinheitenGuids)
        {
            foreach (Guid nutzeinheitGuid in nutzeinheitenGuids)
            {
                await DownloadNutzeinheitAsync(userGuid, nutzeinheitGuid).ConfigureAwait(false);
            }
        }
        
        private async Task<IList<OrderDescriptor>> GetOrderDescriptorsAsync(Guid userGuid)
        {
            GetOrderDescriptorsResponse orderDescriptorsResponse = await client.GetOrderDescriptorsAsync(userGuid).ConfigureAwait(false);

            if (BackupTransmission)
            {
                await webserviceBackup.BackupOrderDescriptorsAsync(orderDescriptorsResponse.OrderDescriptors).ConfigureAwait(false);
            }

            return orderDescriptorsResponse.OrderDescriptors;
        }
    }
}
