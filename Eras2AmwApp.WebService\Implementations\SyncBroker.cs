﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="SyncBroker.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Implementations
{
    using System;

    using Eras2AmwApp.WebService.Extensions;
    using Eras2AmwApp.WebService.Interfaces;

    public class SyncBroker : ISyncBroker
    {
        public bool IsUpdateNeeded(DateTime server, DateTime webservice)
        {
            return webservice.TrimMilliseconds() > server.TrimMilliseconds();
        }
    }
}