﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="CommunicationBackup.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Implementations
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Threading.Tasks;
    using Common.Interfaces;
    using Eras2Amw.Service.Server.ServiceModel.Common;
    using Eras2Amw.Service.Server.ServiceModel.Eras2App.Get;
    using Interfaces;
    using Newtonsoft.Json;

    public class WebserviceBackup : IWebserviceBackup
    {
        private readonly IAppSettings appSettings;

        public WebserviceBackup(IAppSettings appSettings)
        {
            this.appSettings = appSettings ?? throw new ArgumentNullException(nameof(appSettings));
        }

        public async Task BackupStammdatenAsync(GetStammdatenResponse stammdaten)
        {
            if (stammdaten == null)
            {
                throw new ArgumentNullException(nameof(stammdaten));
            }

            using (StreamWriter writer = File.CreateText(GetBackupDownloadFilepath("Stammdaten")))
            {
                await writer.WriteAsync(JsonConvert.SerializeObject(stammdaten));
            }
        }

        public async Task BackupDownloadAbrechnungseinheit(Abrechnungseinheit abrechnungseinheit)
        {
            if (abrechnungseinheit == null)
            {
                throw new ArgumentNullException(nameof(abrechnungseinheit));
            }

            using (StreamWriter writer = File.CreateText(GetBackupDownloadFilepath("Abrechnungseinheit")))
            {
                await writer.WriteAsync(JsonConvert.SerializeObject(abrechnungseinheit));
            }
        }

        public async Task BackupUploadAbrechnungseinheit(Abrechnungseinheit abrechnungseinheit)
        {
            if (abrechnungseinheit == null)
            {
                throw new ArgumentNullException(nameof(abrechnungseinheit));
            }

            using (StreamWriter writer = File.CreateText(GetBackupUploadFilepath("Abrechnungseinheit")))
            {
                await writer.WriteAsync(JsonConvert.SerializeObject(abrechnungseinheit));
            }
        }

        public async Task BackupDownloadNutzeinheit(Nutzeinheit nutzeinheit)
        {
            if (nutzeinheit == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheit));
            }

            using (StreamWriter writer = File.CreateText(GetBackupDownloadFilepath("Nutzeinheit")))
            {
                await writer.WriteAsync(JsonConvert.SerializeObject(nutzeinheit));
            }
        }

        public async Task BackupUploadNutzeinheit(Nutzeinheit nutzeinheit)
        {
            if (nutzeinheit == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheit));
            }

            using (StreamWriter writer = File.CreateText(GetBackupUploadFilepath("Put_Nutzeinheit")))
            {
                await writer.WriteAsync(JsonConvert.SerializeObject(nutzeinheit));
            }
        }

        public async Task BackupAddNutzeinheit(Nutzeinheit nutzeinheit)
        {
            if (nutzeinheit == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheit));
            }

            using (StreamWriter writer = File.CreateText(GetBackupUploadFilepath("Post_Nutzeinheit")))
            {
                await writer.WriteAsync(JsonConvert.SerializeObject(nutzeinheit));
            }
        }

        public async Task BackupDownloadOrder(Order order)
        {
            if (order == null)
            {
                throw new ArgumentNullException(nameof(order));
            }

            using (StreamWriter writer = File.CreateText(GetBackupDownloadFilepath("Order")))
            {
                await writer.WriteAsync(JsonConvert.SerializeObject(order));
            }
        }

        public async Task BackupUploadOrderAsync(Order order)
        {
            if (order == null)
            {
                throw new ArgumentNullException(nameof(order));
            }

            using (StreamWriter writer = File.CreateText(GetBackupUploadFilepath("Order")))
            {
                await writer.WriteAsync(JsonConvert.SerializeObject(order));
            }
        }

        public async Task BackupOrderDescriptorsAsync(IEnumerable<OrderDescriptor> orderDescriptors)
        {
            if (orderDescriptors == null)
            {
                throw new ArgumentNullException(nameof(orderDescriptors));
            }

            using (StreamWriter writer = File.CreateText(GetBackupDownloadFilepath("OrderDescriptors")))
            {
                await writer.WriteAsync(JsonConvert.SerializeObject(orderDescriptors));
            }
        }

        private string GetBackupDownloadFilepath(string prefix)
        {
            if (prefix == null)
            {
                throw new ArgumentNullException(nameof(prefix));
            }

            return Path.Combine(appSettings.WebserviceDownloadDirectory, $"{DateTime.Now:yyyyMMddHHmmssfff}_{prefix}.json");
        }

        private string GetBackupUploadFilepath(string prefix)
        {
            if (prefix == null)
            {
                throw new ArgumentNullException(nameof(prefix));
            }

            return Path.Combine(appSettings.WebserviceUploadDirectory, $"{DateTime.Now:yyyyMMddHHmmssfff}_{prefix}.json");
        }
    }
}