﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IAbrechnungseinheitService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Interfaces
{
    using System;
    using System.Threading.Tasks;

    using Eras2Amw.Service.Server.ServiceModel.Common;

    using Eras2AmwApp.Database.Contexts;

    public interface IAbrechnungseinheitService
    {
        Eras2AmwContext DatabaseContext { get; set; }

        bool DoBackup { get; set; }

        bool Exists(Guid guid);

        Task Insert(Abrechnungseinheit abrechnungseinheit);

        Task<Abrechnungseinheit> LoadAsync(Guid guid);
    }
}