﻿namespace Eras2AmwApp.WebService.Interfaces
{
    using System;
    using System.Net;
    using System.Threading.Tasks;
    using WebNutzeinheit = Eras2Amw.Service.Server.ServiceModel.Common.Nutzeinheit;
    using Eras2AmwApp.Domain.Eras2Amw.Models;
    
    using EventArgs;

    public interface IAmwWebservice
    {
        event EventHandler<WebserviceEventArgs> Info;

        event EventHandler<WebserviceEventArgs> Warning;

        event EventHandler<WebserviceEventArgs> Error;

        bool BackupTransmission { get; set; }

        bool IsServerNotAvailableStatusError(WebException exp);

        Task<Guid> LoginAsync(string username, string password);

        Task LogoutAsync();

        Task SyncOrdersAsync(Guid userGuid);

        Task SyncFullAmwAppDatabaseAsync(string databasePath);

        Task DownloadNutzeinheitPhotoAsync(Guid userGuid, Photo photo);

        Task DownloadDevicePhotoAsync(Guid userGuid, Photo photo);

        Task DownloadNutzeinheitSignatureAsync(Guid userGuid, Signature signature);

        Task SyncLiveNutzeinheitAsync();

        Task SyncLiveNutzerEditAsync(Guid nutzeinheit);
    }
}
