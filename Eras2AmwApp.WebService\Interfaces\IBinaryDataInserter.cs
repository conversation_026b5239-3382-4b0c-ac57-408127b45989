﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IBinaryDataInserter.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Interfaces
{
    using System.IO;
    using System.Threading.Tasks;

    using Eras2AmwApp.Domain.Eras2Amw.Models;

    public interface IBinaryDataInserter
    {
        Task SaveNutzeinheitPhotoAsync(Photo photo, Stream stream);

        Task SaveDevicePhotoAsync(Photo photo, Stream stream);

        Task SaveNutzeinheitSignatureAsync(Signature signature, Stream stream);
    }
}