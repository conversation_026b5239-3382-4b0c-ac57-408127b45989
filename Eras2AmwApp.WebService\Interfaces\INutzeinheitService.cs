﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="INutzeinheitService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Interfaces
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;

    using Eras2Amw.Service.Server.ServiceModel.Common;

    using Eras2AmwApp.Database.Contexts;

    public interface INutzeinheitService
    {
        Eras2AmwContext DatabaseContext { get; set; }

        bool DoBackup { get; set; }

        bool Exists(Guid guid);

        Task Insert(Nutzeinheit nutzeinheit);

        IEnumerable<NutzeinheitOrderState> LoadReadyToSendAsync(Order order);

        /**
         * Nur wenn alle Geräte, für die ein Auftrag existiert (und somit ein Eintrag in DeviceOrderState) einer Nutzeinheit bearbeitet
         * (momentan im Zustand: Nicht in Progress (Also Updating/Creating/Completed)) gilt die Nutzeinheit als "bearbeitet" (auf der App abgeschlossen).
         */
        bool AreAllDeviceOrderStatesOfNutzeinheitReady(Guid orderGuid, Guid nutzeinheitGuid);

        void NutzeinheitSyncLiveDone(Guid nutzeinheitGuid);

        void NutzerSyncLiveDone(Guid nutzeinheitGuid);
    }
}