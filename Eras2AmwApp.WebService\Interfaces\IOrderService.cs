﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IOrderService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Interfaces
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;

    using Database.Contexts;

    using Eras2Amw.Service.Server.ServiceModel.Common;

    public interface IOrderService
    {
        Eras2AmwContext DatabaseContext { get; set; }

        bool DoBackup { get; set; }

        Task Insert(Order order);

        Task<IEnumerable<Order>> LoadReadyToSendAsync();

        void SyncLiveDone(Guid orderGuid, Guid nutzeinheitGuid);
    }
}