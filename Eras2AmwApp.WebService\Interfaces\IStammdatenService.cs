﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="IStammdatenService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Interfaces
{
    using System.Threading.Tasks;
    using Eras2Amw.Service.Server.ServiceModel.Eras2App.Get;
    using Eras2AmwApp.Database.Contexts;

    public interface IStammdatenService
    {
        Eras2AmwContext DatabaseContext { get; set; }

        bool DoBackup { get; set; }

        Task Insert(GetStammdatenResponse stammdaten);
    }
}