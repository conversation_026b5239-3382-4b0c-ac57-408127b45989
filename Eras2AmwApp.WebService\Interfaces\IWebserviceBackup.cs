﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="dfsdf.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Interfaces
{
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using Eras2Amw.Service.Server.ServiceModel.Common;
    using Eras2Amw.Service.Server.ServiceModel.Eras2App.Get;

    public interface IWebserviceBackup
    {
        Task BackupStammdatenAsync(GetStammdatenResponse stammdaten);

        Task BackupDownloadAbrechnungseinheit(Abrechnungseinheit abrechnungseinheit);

        Task BackupUploadAbrechnungseinheit(Abrechnungseinheit abrechnungseinheit);
       
        Task BackupDownloadNutzeinheit(Nutzeinheit nutzeinheit);
       
        Task BackupUploadNutzeinheit(Nutzeinheit nutzeinheit);

        Task BackupAddNutzeinheit(Nutzeinheit nutzeinheit);
       
        Task BackupDownloadOrder(Order order);
       
        Task BackupUploadOrderAsync(Order order);
        
        Task BackupOrderDescriptorsAsync(IEnumerable<OrderDescriptor> orderDescriptors);
    }
}