﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AbrechnungseinheitAfterMap.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Mapper.AfterMap.ToDal
{
    using System;
    using System.Collections.Generic;
    using Domain.Eras2Amw.Models;

    public class AbrechnungseinheitAfterMap
    {
        public Dictionary<string, Salutation> Salutations { get; set; }

        public string RemoteIp { get; set; }

        public void Start(object src, object dest)
        {
            if (src == null)
            {
                throw new ArgumentNullException(nameof(src));
            }

            if (dest == null)
            {
                throw new ArgumentNullException(nameof(dest));
            }

            var sourceAbrechnungseinheit = (Eras2Amw.Service.Server.ServiceModel.Common.Abrechnungseinheit) src;
            var destAbrechnungseinheit = (Abrechnungseinheit) dest;

            SalutationMapping(sourceAbrechnungseinheit.Customer.Salutation, destAbrechnungseinheit.Customer);

            destAbrechnungseinheit.IPAddress = RemoteIp;
        }

        private void SalutationMapping(string salutationLabel, Customer customer)
        {
            if (salutationLabel == null)
            {
                throw new ArgumentNullException(nameof(salutationLabel));
            }

            if (customer == null)
            {
                throw new ArgumentNullException(nameof(customer));
            }

            if (!Salutations.TryGetValue(salutationLabel, out Salutation salutation))
            {
                salutation = new Salutation { Label = salutationLabel };
                Salutations.Add(salutationLabel, salutation);
            }

            customer.Salutation = salutation;
        }
    }
}