﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzerAfterMap.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Mapper.AfterMap.ToDal
{
    using System;
    using System.Collections.Generic;
    using System.Linq;

    using AutoMapper;

    using Eras2Amw.Service.Server.ServiceModel.Common;

    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;

    using Serilog;

    using Dal = Domain.Eras2Amw.Models;

    public class NutzerAfterMap : IMappingAction<Nutzer, Dal.Nutzer>
    {
        private readonly ILogger logger;

        private readonly Dictionary<string, Dal.Salutation> salutations;
        private readonly Dictionary<string, Dal.Title> titles;

        public NutzerAfterMap(IDbContextFactory contextFactory, ILogger logger)
        {
            this.logger = logger ?? throw new ArgumentNullException(nameof(logger));

            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                salutations = context.Salutations.ToDictionary(x => x.Label, x => x);
                titles = context.Titles.ToDictionary(x => x.Label, x => x);
            }
        }

        public void Process(Nutzer source, Dal.Nutzer destination, ResolutionContext context)
        {
            try
            {
                SalutationMapping(source.Salutation, destination);
                if (!string.IsNullOrEmpty(source.Title))
                {
                    TitleMapping(source.Title, destination);
                }
            }
            catch (Exception e)
            {
                logger.Error(e, e.Message);
                throw;
            }
        }

        private void SalutationMapping(string salutationLabel, Dal.Nutzer nutzer)
        {
            if (salutationLabel == null)
            {
                throw new ArgumentNullException(nameof(salutationLabel));
            }

            if (nutzer == null)
            {
                throw new ArgumentNullException(nameof(nutzer));
            }

            if (!salutations.TryGetValue(salutationLabel, out Dal.Salutation salutation))
            {
                salutation = new Dal.Salutation { Label = salutationLabel };
                salutations.Add(salutationLabel, salutation);
            }

            nutzer.Salutation = salutation;
        }

        private void TitleMapping(string titleLabel, Dal.Nutzer nutzer)
        {
            if (titleLabel == null)
            {
                throw new ArgumentNullException(nameof(titleLabel));
            }

            if (nutzer == null)
            {
                throw new ArgumentNullException(nameof(nutzer));
            }

            if (!titles.TryGetValue(titleLabel, out Dal.Title title))
            {
                title = new Dal.Title { Label = titleLabel };
                titles.Add(titleLabel, title);
            }

            nutzer.Title = title;
        }
    }
}