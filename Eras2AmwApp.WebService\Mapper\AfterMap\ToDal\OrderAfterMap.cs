﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="OrderAfterMap.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Mapper.AfterMap.ToDal
{
    using System;
    using System.Collections.Generic;
    using Eras2Amw.Service.Server.ServiceModel.Common;
    using Dal = Domain.Eras2Amw.Models;
    
    public class OrderAfterMap
    {
        public Dictionary<string, Dal.Salutation> Salutations { get; set; }

        public Dictionary<string, Dal.Title> Titles { get; set; }

        public void Start(object source, object destination)
        {
            if (source == null)
            {
                throw new ArgumentNullException(nameof(source));
            }

            if (destination == null)
            {
                throw new ArgumentNullException(nameof(destination));
            }

            var sourceOrder = (Order) source;
            var destinationOrder = (Dal.Order) destination;

            SalutationMapping(sourceOrder.Orderer.Person.Salutation, destinationOrder.Orderer.Person);

            if (!string.IsNullOrEmpty(sourceOrder.Orderer.Person.Title))
            {
                TitleMapping(sourceOrder.Orderer.Person.Title, destinationOrder.Orderer.Person);
            }
        }

        private void SalutationMapping(string salutationLabel, Dal.Person person)
        {
            if (salutationLabel == null)
            {
                throw new ArgumentNullException(nameof(salutationLabel));
            }

            if (person == null)
            {
                throw new ArgumentNullException(nameof(person));
            }

            if (!Salutations.TryGetValue(salutationLabel, out Dal.Salutation salutation))
            {
                salutation = new Dal.Salutation { Label = salutationLabel };
                Salutations.Add(salutationLabel, salutation);
            }

            person.Salutation = salutation;
        }

        private void TitleMapping(string titleLabel, Dal.Person person)
        {
            if (titleLabel == null)
            {
                throw new ArgumentNullException(nameof(titleLabel));
            }

            if (person == null)
            {
                throw new ArgumentNullException(nameof(person));
            }

            if (!Titles.TryGetValue(titleLabel, out Dal.Title title))
            {
                title = new Dal.Title { Label = titleLabel };
                Titles.Add(titleLabel, title);
            }

            person.Title = title;
        }
    }
}