﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="PersonsAfterMap.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Mapper.AfterMap.ToDal
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using Domain.Eras2Amw.Models;

    public class PersonsAfterMap
    {
        public Dictionary<string, Salutation> Salutations { get; set; }

        public Dictionary<string, Title> Titles { get; set; }

        public void Start(object src, object dest)
        {
            if (src == null)
            {
                throw new ArgumentNullException(nameof(src));
            }

            if (dest == null)
            {
                throw new ArgumentNullException(nameof(dest));
            }

            var sourcePersons = (IEnumerable<Eras2Amw.Service.Server.ServiceModel.Common.Person>) src;
            var destPersons = (IEnumerable<Person>) dest;

            var items = sourcePersons.Zip(destPersons, (x, y) => new { Source = x, Destination = y });
            foreach (var item in items)
            {
                SalutationMapping(item.Source.Salutation, item.Destination);
                if (!string.IsNullOrEmpty(item.Source.Title))
                {
                    TitleMapping(item.Source.Title, item.Destination);
                }
            }
        }

        private void SalutationMapping(string salutationLabel, Person person)
        {
            if (salutationLabel == null)
            {
                throw new ArgumentNullException(nameof(salutationLabel));
            }

            if (person == null)
            {
                throw new ArgumentNullException(nameof(person));
            }

            if (!Salutations.TryGetValue(salutationLabel, out Salutation salutation))
            {
                salutation = new Salutation { Label = salutationLabel };
                Salutations.Add(salutationLabel, salutation);
            }

            person.Salutation = salutation;
        }

        private void TitleMapping(string titleLabel, Person person)
        {
            if (titleLabel == null)
            {
                throw new ArgumentNullException(nameof(titleLabel));
            }

            if (person == null)
            {
                throw new ArgumentNullException(nameof(person));
            }

            if (!Titles.TryGetValue(titleLabel, out Title title))
            {
                title = new Title { Label = titleLabel };
                Titles.Add(titleLabel, title);
            }

            person.Title = title;
        }
    }
}