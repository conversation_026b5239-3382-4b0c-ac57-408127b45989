﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="OrderAfterMap.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Mapper.AfterMap.ToWebservices
{
    using System;
    using System.Collections.Generic;
    using System.Linq;

    using AutoMapper;

    using Eras2Amw.Service.Server.ServiceModel.Common;

    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    
    using Dal = Domain.Eras2Amw.Models;

    public class OrderAfterMap : IMappingAction<Dal.Order, Order>
    {
        private readonly IDbContextFactory contextFactory;

        public OrderAfterMap(IDbContextFactory contextFactory)
        {
            this.contextFactory = contextFactory;
        }

        public void Process(Dal.Order source, Order destination, ResolutionContext resolutionContext)
        {
            if (source == null)
            {
                throw new ArgumentNullException(nameof(source));
            }

            if (destination == null)
            {
                throw new ArgumentNullException(nameof(destination));
            }

            RemoveDeviceOrderStatesWithoutNutzeinheitOrderState(destination);
        }

        /**
         * DeviceOrderStates und NutzeinheitOrderStates werden nur dann übertragen, wenn sie im Zustand Creating oder Updating sind.
         * Die App erzeugt aber auch Zustaände in denen OrderStates auf Updating oder Creating sind und die NutzeinheitOrderState auf InProgress gesetzt wird.
         * In diesem Fall würde DeviceOrderStates übertragen werden, für die keine NutzeinheitOrderState innerhalb dieser Übertragung existiert.
         * Der Webservice würde die Annahme dieser Daten verweigern. 
         * Lösung: Hier werden DeviceOrderStates ohne NutzeinheitOrderStates entfernt.
         */
        private void RemoveDeviceOrderStatesWithoutNutzeinheitOrderState(Order destination)
        {
            if (destination == null)
            {
                throw new ArgumentNullException(nameof(destination));
            }

            var deviceOrderStates = new List<DeviceOrderState>();
            using (Eras2AmwContext context = contextFactory.CreateAmw())
            {
                foreach (DeviceOrderState deviceOrderState in destination.DeviceOrderStates)
                {
                    Dal.Nutzeinheit nutzeinheit = context.Nutzeinheiten.Single(x => x.Devices.Select(y => y.Guid).Contains(deviceOrderState.DeviceGuid));

                    if (destination.NutzeinheitOrderStates.Any(x => x.NutzeinheitGuid == nutzeinheit.Guid))
                    {
                        deviceOrderStates.Add(deviceOrderState);
                    }
                }

                destination.DeviceOrderStates = deviceOrderStates;
            }
        }
    }
}