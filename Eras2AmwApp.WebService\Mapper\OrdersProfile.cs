﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AwmProfile.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Mapper
{
    using System.Linq;

    using AutoMapper;

    using Domain.Eras2Amw.Extensions;

    using Eras2Amw.Service.Server.ServiceModel.Common;

    using Eras2AmwApp.WebService.Mapper.AfterMap.ToDal;

    using Dal = Eras2AmwApp.Domain.Eras2Amw.Models;
    using ToWebservices = Eras2AmwApp.WebService.Mapper.AfterMap.ToWebservices;

    public class OrdersProfile : Profile
    {
        public OrdersProfile()
        {
            MapDatabaseToWebservice();

            MapWebserviceToDatabase();            
        }

        private void MapWebserviceToDatabase()
        {
            CreateMap<Abrechnungseinheit, Dal.Abrechnungseinheit>()
                .ForMember(x => x.Nutzeinheiten, opt => opt.Ignore())
                .ForMember(x => x.Orders, opt => opt.Ignore())
                .ForMember(x => x.IPAddress, opt => opt.Ignore());

            CreateMap<Customer, Dal.Customer>()
                .ForMember(x => x.Salutation, opt => opt.Ignore())
                .ForMember(x => x.SalutationId, opt => opt.Ignore())
                .ForMember(x => x.Abrechnungseinheiten, opt => opt.Ignore());

            CreateMap<Address, Dal.Address>()
                .ForMember(x => x.Abrechnungseinheiten, opt => opt.Ignore())
                .ForMember(x => x.Nutzeinheiten, opt => opt.Ignore());

            CreateMap<Person, Dal.Person>()
                .ForMember(x => x.Orderers, opt => opt.Ignore())
                .ForMember(x => x.PersonAbrechnungseinheiten, opt => opt.Ignore())
                .ForMember(x => x.Salutation, opt => opt.Ignore())
                .ForMember(x => x.SalutationId, opt => opt.Ignore())
                .ForMember(x => x.Title, opt => opt.Ignore())
                .ForMember(x => x.TitleId, opt => opt.Ignore());

            CreateMap<PersonCommunication, Dal.PersonCommunication>()
                .ForMember(x => x.CommunicationFeature, opt => opt.Ignore())
                .ForMember(x => x.Person, opt => opt.Ignore())
                .ForMember(x => x.PersonGuid, opt => opt.Ignore());

            CreateMap<PersonAbrechnungseinheit, Dal.PersonAbrechnungseinheit>()
                .ForMember(x => x.Abrechnungseinheit, opt => opt.Ignore())
                .ForMember(x => x.AbrechnungseinheitGuid, opt => opt.Ignore())
                .ForMember(x => x.Person, opt => opt.Ignore())
                .ForMember(x => x.IsCreatedByApp, opt => opt.Ignore());

            CreateMap<Nutzeinheit, Dal.Nutzeinheit>()
                .ForMember(x => x.OrderStates, opt => opt.Ignore())
                .ForMember(x => x.Abrechnungseinheit, opt => opt.Ignore())
                .ForMember(x => x.NutzeinheitOrderPositions, opt => opt.Ignore())
                .ForMember(x => x.IPAddress, opt => opt.Ignore())
                .ForMember(x => x.AppointmentNutzeinheiten, opt => opt.Ignore())
                .ForMember(x => x.IsCreatedByApp, opt => opt.Ignore())
                .ForMember(x => x.Signatures, opt => opt.Ignore());

            CreateMap<Nutzer, Dal.Nutzer>()
                .ForMember(x => x.Nutzeinheit, opt => opt.Ignore())
                .ForMember(x => x.NutzeinheitGuid, opt => opt.Ignore())
                .ForMember(x => x.Salutation, opt => opt.Ignore())
                .ForMember(x => x.SalutationId, opt => opt.Ignore())
                .ForMember(x => x.Title, opt => opt.Ignore())
                .ForMember(x => x.TitleId, opt => opt.Ignore())
                .AfterMap<NutzerAfterMap>();

            CreateMap<Device, Dal.Device>()
                .ForMember(x => x.OrderStates, opt => opt.Ignore())
                .ForMember(x => x.Nutzeinheit, opt => opt.Ignore())
                .ForMember(x => x.NutzeinheitGuid, opt => opt.Ignore())
                .ForMember(x => x.DeviceCatalog, opt => opt.Ignore())
                .ForMember(x => x.Room, opt => opt.Ignore())
                .ForMember(x => x.IsCreatedByApp, opt => opt.Ignore());
               
            CreateMap<DeviceConsumption, Dal.DeviceConsumption>()
                .ForMember(x => x.Device, opt => opt.Ignore())
                .ForMember(x => x.DeviceGuid, opt => opt.Ignore())
                .ForMember(x => x.ReadingKind, opt => opt.Ignore());

            CreateMap<NutzerCommunication, Dal.NutzerCommunication>()
                .ForMember(x => x.CommunicationFeature, opt => opt.Ignore())
                .ForMember(x => x.Nutzer, opt => opt.Ignore())
                .ForMember(x => x.NutzerGuid, opt => opt.Ignore());

            CreateMap<NutzerCoOwnership, Dal.NutzerCoOwnership>()
                .ForMember(x => x.Nutzer, opt => opt.Ignore())
                .ForMember(x => x.NutzerGuid, opt => opt.Ignore());

            CreateMap<NutzerPersonen, Dal.NutzerPersonen>()
                .ForMember(x => x.Nutzer, opt => opt.Ignore())
                .ForMember(x => x.NutzerGuid, opt => opt.Ignore());

            CreateMap<NutzerQuadratmeter, Dal.NutzerQuadratmeter>()
                .ForMember(x => x.Nutzer, opt => opt.Ignore())
                .ForMember(x => x.NutzerGuid, opt => opt.Ignore());

            CreateMap<DeviceAdditionalArticle, Dal.DeviceAdditionalArticle>()
                .ForMember(x => x.Device, opt => opt.Ignore())
                .ForMember(x => x.DeviceGuid, opt => opt.Ignore())
                .ForMember(x => x.AdditionalArticle, opt => opt.Ignore());

            CreateMap<Photo, Dal.Photo>()
                .ForMember(x => x.Name, opt => opt.Ignore())
                .ForMember(x => x.Path, opt => opt.Ignore())
                .ForMember(x => x.DeviceGuid, opt => opt.Ignore())
                .ForMember(x => x.Device, opt => opt.Ignore())
                .ForMember(x => x.NutzeinheitGuid, opt => opt.Ignore())
                .ForMember(x => x.Nutzeinheit, opt => opt.Ignore());
                
            CreateMap<Signature, Dal.Signature>()
                .ForMember(x => x.Path, opt => opt.Ignore())
                .ForMember(x => x.Nutzeinheit, opt => opt.Ignore())
                .ForMember(x => x.Order, opt => opt.Ignore())
                .ForMember(x => x.OrderGuid, opt => opt.Ignore());

            CreateMap<Appointment, Dal.Appointment>()
                .ForMember(x => x.OrderGuid, opt => opt.Ignore())
                .ForMember(x => x.Order, opt => opt.Ignore());
                
            CreateMap<Order, Dal.Order>()
                .ForMember(x => x.Abrechnungseinheit, opt => opt.Ignore())
                .ForMember(x => x.IPAddress, opt => opt.Ignore());
                
            CreateMap<OrderState, Dal.OrderState>()
                .ForMember(x => x.Order, opt => opt.Ignore())
                .ForMember(x => x.OrderGuid, opt => opt.Ignore());

            CreateMap<OrderPosition, Dal.OrderPosition>()
                .ForMember(x => x.Order, opt => opt.Ignore())
                .ForMember(x => x.OrderGuid, opt => opt.Ignore())
                .ForMember(x => x.Article, opt => opt.Ignore());

            CreateMap<NutzeinheitOrderPosition, Dal.NutzeinheitOrderPosition>()
                .ForMember(x => x.OrderPosition, opt => opt.Ignore())
                .ForMember(x => x.Nutzeinheit, opt => opt.Ignore())
                .ForMember(x => x.OrderPositionGuid, opt => opt.Ignore());

            CreateMap<NutzeinheitOrderState, Dal.NutzeinheitOrderState>()
                .ForMember(x => x.Nutzeinheit, opt => opt.Ignore())
                .ForMember(x => x.Order, opt => opt.Ignore())
                .ForMember(x => x.OrderGuid, opt => opt.Ignore());

            CreateMap<DeviceOrderState, Dal.DeviceOrderState>()
                .ForMember(x => x.Device, opt => opt.Ignore())
                .ForMember(x => x.Order, opt => opt.Ignore())
                .ForMember(x => x.OrderGuid, opt => opt.Ignore());

            CreateMap<Orderer, Dal.Orderer>()
                .ForMember(x => x.Orders, opt => opt.Ignore());

            CreateMap<AppointmentStoreUser, Dal.AppointmentTechnician>()
                .ForMember(x => x.Appointment, opt => opt.Ignore())
                .ForMember(x => x.AppointmentGuid, opt => opt.Ignore())
                .ForMember(x => x.TechnicianGuid, opt => opt.MapFrom(x => x.StoreUserId));

            CreateMap<AppointmentNutzeinheit, Dal.AppointmentNutzeinheit>()
                .ForMember(x => x.Appointment, opt => opt.Ignore())
                .ForMember(x => x.Nutzeinheit, opt => opt.Ignore())
                .ForMember(x => x.AppointmentGuid, opt => opt.Ignore());
        }

        private void MapDatabaseToWebservice()
        {
            CreateMap<Dal.Person, Person>()
                .ForMember(x => x.Salutation, opt => opt.MapFrom(s => s.Salutation.Label))
                .ForMember(x => x.Title, opt => opt.MapFrom(s => s.Title.Label));

            CreateMap<Dal.PersonCommunication, PersonCommunication>();

            CreateMap<Dal.PersonAbrechnungseinheit, PersonAbrechnungseinheit>();

            CreateMap<Dal.Abrechnungseinheit, Abrechnungseinheit>()
                .ForMember(x => x.Persons, opt => opt.MapFrom(s => s.PersonAbrechnungseinheiten.Select(x => x.Person)));

            CreateMap<Dal.Address, Address>();

            CreateMap<Dal.Customer, Customer>()
                .ForMember(x => x.Salutation, opt => opt.MapFrom(s => s.Salutation.Label));

            CreateMap<Dal.Nutzeinheit, Nutzeinheit>()
                .ForMember(x => x.Photos, opt => opt.MapFrom(src => src.Photos.Where(x => x.CreatedByApp)));

            CreateMap<Dal.Nutzer, Nutzer>()
                .ForMember(x => x.Salutation, opt => opt.MapFrom(s => s.Salutation.Label))
                .ForMember(x => x.Title, opt => opt.MapFrom(s => s.Title.Label));

            CreateMap<Dal.NutzerCommunication, NutzerCommunication>();

            CreateMap<Dal.NutzerPersonen, NutzerPersonen>();

            CreateMap<Dal.NutzerCoOwnership, NutzerCoOwnership>();

            CreateMap<Dal.NutzerQuadratmeter, NutzerQuadratmeter>();

            CreateMap<Dal.Device, Device>()
                .ForMember(x => x.Photos, opt => opt.MapFrom(src => src.Photos.Where(x => x.CreatedByApp)));

            CreateMap<Dal.DeviceAdditionalArticle, DeviceAdditionalArticle>();
                
            CreateMap<Dal.DeviceConsumption, DeviceConsumption>(); 

            CreateMap<Dal.Photo, Photo>();

            CreateMap<Dal.Signature, Signature>();
                
            CreateMap<Dal.Order, Order>()
                .ForMember(x => x.NutzeinheitOrderStates, opt => opt.MapFrom(src => src.NutzeinheitOrderStates.Where(x => x.ProcessState.IsUpdating() || x.ProcessState.IsCreating())))
                .ForMember(x => x.DeviceOrderStates, opt => opt.MapFrom(src => src.DeviceOrderStates.Where(x => x.ProcessState.IsUpdating() || x.ProcessState.IsCreating())))
                .ForMember(x => x.Signatures, opt => opt.MapFrom(src => src.Signatures.Where(x => x.CreatedByApp))).AfterMap<ToWebservices.OrderAfterMap>();
            
            CreateMap<Dal.Orderer, Orderer>();

            CreateMap<Dal.OrderState, OrderState>();

            CreateMap<Dal.DeviceOrderState, DeviceOrderState>();

            CreateMap<Dal.NutzeinheitOrderState, NutzeinheitOrderState>();

            CreateMap<Dal.OrderPosition, OrderPosition>();

            CreateMap<Dal.NutzeinheitOrderPosition, NutzeinheitOrderPosition>();

            CreateMap<Dal.Appointment, Appointment>();

            CreateMap<Dal.AppointmentTechnician, AppointmentStoreUser>()
                .ForMember(x => x.StoreUserId, opt => opt.MapFrom(s => s.TechnicianGuid.ToString()));

            CreateMap<Dal.AppointmentNutzeinheit, AppointmentNutzeinheit>();
        }
    }
}