﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="WebserviceProfile.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Mapper
{
    using AutoMapper;
    using Eras2Amw.Service.Server.ServiceModel.Common;
    using Dal = Domain.Eras2Amw.Models;

    public class StammdatenProfile : Profile
    {
        public StammdatenProfile()
        {
            MapWebserviceToDatabase();
        }

        private void MapWebserviceToDatabase()
        {
            CreateMap<CommunicationFeature, Dal.CommunicationFeature>()
                .ForMember(x => x.NutzerCommunications, opt => opt.Ignore())
                .ForMember(x => x.PersonCommunications, opt => opt.Ignore());

            CreateMap<DeviceKind, Dal.DeviceKind>()
                .ForMember(x => x.DeviceCatalogs, opt => opt.Ignore());

            CreateMap<Article, Dal.Article>()
                .ForMember(x => x.OrderPositions, opt => opt.Ignore());

            CreateMap<DeviceCatalog, Dal.DeviceCatalog>()
                .ForMember(x => x.DeviceKind, opt => opt.Ignore())
                .ForMember(x => x.Manufacturer, opt => opt.Ignore()).ForMember(x => x.Devices, opt => opt.Ignore());

            CreateMap<Manufacturer, Dal.Manufacturer>()
                .ForMember(x => x.DeviceCatalogs, opt => opt.Ignore());

            CreateMap<ReadingKind, Dal.ReadingKind>()
                .ForMember(x => x.DeviceConsumptions, opt => opt.Ignore());

            CreateMap<AmwInfoKey, Dal.AmwInfoKey>()
                .ForMember(x => x.DeviceOrderStates, opt => opt.Ignore())
                .ForMember(x => x.NutzeinheitOrderStates, opt => opt.Ignore());

            CreateMap<AdditionalArticle, Dal.AdditionalArticle>()
                .ForMember(x => x.DeviceAdditionalArticles, opt => opt.Ignore());

            CreateMap<Room, Dal.Room>()
                .ForMember(x => x.Devices, opt => opt.Ignore());
        }
    }
}