﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="AbrechnungseinheitService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Services
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using AutoMapper;

    using Eras2Amw.Service.Server.ServiceModel.Common;

    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.WebService.Implementations;
    using Eras2AmwApp.WebService.Interfaces;
    using Eras2AmwApp.WebService.Mapper.AfterMap.ToDal;

    using Dal = Domain.Eras2Amw.Models;

    public class AbrechnungseinheitService : IAbrechnungseinheitService
    {
        private readonly IMapper mapper;

        private readonly IWebserviceBackup webserviceBackup;

        private readonly IDomainLoader domainLoader;

        private Eras2AmwContext context;

        private AbrechnungseinheitService(IMapper mapper, IAppSettings appSettings, IDomainLoader domainLoader)
        {
            this.mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            this.webserviceBackup = new WebserviceBackup(appSettings);
            this.domainLoader = domainLoader ?? throw new ArgumentNullException(nameof(domainLoader));
        }

        public bool DoBackup { get; set; } = true;

        public Eras2AmwContext DatabaseContext
        {
            get
            {
                return context;
            }

            set
            {
                context = value;
            }
        }

        public string IpAddress { get; set; }

        public static IAbrechnungseinheitService Create(IMapper mapper, IAppSettings appSettings, IDomainLoader domainLoader)
        {
            return new AbrechnungseinheitService(mapper, appSettings, domainLoader);
        }

        public bool Exists(Guid guid) => context.Abrechnungseinheiten.Find(guid) != null;

        public async Task Insert(Abrechnungseinheit abrechnungseinheit)
        {
            if (abrechnungseinheit == null)
            {
                throw new ArgumentNullException(nameof(abrechnungseinheit));
            }

            if (DoBackup)
            {
                await webserviceBackup.BackupDownloadAbrechnungseinheit(abrechnungseinheit).ConfigureAwait(false);
            }

            Dictionary<string, Dal.Salutation> salutations = FindSalutations();
            Dictionary<string, Dal.Title> titles = FindTitles();

            var personsAfterMap = new PersonsAfterMap
            {
                Salutations = salutations,
                Titles = titles
            };
                
            var persons = mapper.Map<IEnumerable<Dal.Person>>(abrechnungseinheit.Persons, opts => opts.AfterMap(personsAfterMap.Start));
            
            var abrechnungseinheitAfterMap = new AbrechnungseinheitAfterMap
            {
                Salutations = salutations,
                RemoteIp = IpAddress ?? string.Empty
            };

            var dalAbrechnungseinheit = mapper.Map<Dal.Abrechnungseinheit>(abrechnungseinheit, opts => opts.AfterMap(abrechnungseinheitAfterMap.Start));

            AssignPersonsToAbrechnungseinheit(dalAbrechnungseinheit, persons);

            InsertAbrechnungseinheit(dalAbrechnungseinheit);
        }

        public async Task<Abrechnungseinheit> LoadAsync(Guid guid)
        {
            Dal.Abrechnungseinheit dalAbrechnungseinheit = domainLoader.LoadAbrechnungseinheit(guid);

            var abrechnungseinheit = mapper.Map<Abrechnungseinheit>(dalAbrechnungseinheit);

            if (DoBackup)
            {
                await webserviceBackup.BackupUploadAbrechnungseinheit(abrechnungseinheit).ConfigureAwait(false);
            }

            return abrechnungseinheit;
        }

        private Dictionary<string, Dal.Salutation> FindSalutations()
        {
            return context.Salutations.ToDictionary(x => x.Label, x => x);
        }

        private Dictionary<string, Dal.Title> FindTitles()
        {
            return context.Titles.ToDictionary(x => x.Label, x => x);
        }
        
        private void AssignPersonsToAbrechnungseinheit(Dal.Abrechnungseinheit abrechnungseinheit, IEnumerable<Dal.Person> persons)
        {
            if (abrechnungseinheit == null)
            {
                throw new ArgumentNullException(nameof(abrechnungseinheit));
            }

            if (persons == null)
            {
                throw new ArgumentNullException(nameof(persons));
            }

            foreach (Dal.PersonAbrechnungseinheit personAbrechnungseinheit in abrechnungseinheit.PersonAbrechnungseinheiten)
            {
                personAbrechnungseinheit.Person = persons.Single(x => x.Guid == personAbrechnungseinheit.PersonGuid);
            }
        }

        private void InsertAbrechnungseinheit(Dal.Abrechnungseinheit abrechnungseinheit)
        {
            if (abrechnungseinheit == null)
            {
                throw new ArgumentNullException(nameof(abrechnungseinheit));
            }

            context.Abrechnungseinheiten.Add(abrechnungseinheit);
        }
    }
}