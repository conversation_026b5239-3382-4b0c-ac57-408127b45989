﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="NutzeinheitService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Services
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using AutoMapper;
    using Eras2Amw.Service.Server.ServiceModel.Common;
    using Eras2Amw.Service.Server.ServiceModel.Enums;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Extensions;
    using Eras2AmwApp.WebService.Implementations;
    using Eras2AmwApp.WebService.Interfaces;
    using Microsoft.EntityFrameworkCore;
    using Dal = Domain.Eras2Amw.Models;

    public class NutzeinheitService : INutzeinheitService
    {
        private readonly IMapper mapper;

        private readonly IWebserviceBackup webserviceBackup;

        private NutzeinheitService(IMapper mapper, IAppSettings appSettings)
        {
            this.mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            webserviceBackup = new WebserviceBackup(appSettings);
        }

        public bool DoBackup { get; set; } = true;

        public Eras2AmwContext DatabaseContext { get; set; }

        public static INutzeinheitService Create(IMapper mapper, IAppSettings appSettings, IDomainLoader domainLoader)
        {
            return new NutzeinheitService(mapper, appSettings);
        }

        public bool Exists(Guid guid) => DatabaseContext.Nutzeinheiten.Find(guid) != null;

        public async Task Insert(Nutzeinheit nutzeinheit)
        {
            if (nutzeinheit == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheit));
            }

            if (DoBackup)
            {
                await webserviceBackup.BackupDownloadNutzeinheit(nutzeinheit).ConfigureAwait(false);
            }

            var dalNutzeinheit = mapper.Map<Dal.Nutzeinheit>(nutzeinheit);

            AddNutzeinheit(dalNutzeinheit);
        }
        
        public IEnumerable<NutzeinheitOrderState> LoadReadyToSendAsync(Order order)
        {
            if (order == null)
            {
                throw new ArgumentNullException(nameof(order));
            }

            List<NutzeinheitOrderState> nutzeinheitOrderStates = order.NutzeinheitOrderStates.Where(x => x.ProcessState == ProcessState.Updating || x.ProcessState == ProcessState.Creating).ToList();
            
            return nutzeinheitOrderStates;
        }

        /**
         * Nur wenn alle Geräte, für die ein Auftrag existiert (und somit ein Eintrag in DeviceOrderState) einer Nutzeinheit bearbeitet
         * (momentan im Zustand: Nicht in Progress (Also Updating/Creating/Completed)) gilt die Nutzeinheit als "bearbeitet" (auf der App abgeschlossen).
         */
        public bool AreAllDeviceOrderStatesOfNutzeinheitReady(Guid orderGuid, Guid nutzeinheitGuid)
        {
            Dal.NutzeinheitOrderState dalNutzeinheitOrderState = 
                DatabaseContext.NutzeinheitOrderStates.Where(x => x.OrderGuid == orderGuid && x.NutzeinheitGuid == nutzeinheitGuid)
                    .Include(x => x.Nutzeinheit)
                    .Include(x => x.Nutzeinheit).ThenInclude(x => x.Devices).Single();
          
            IEnumerable<Guid> deviceGuids = dalNutzeinheitOrderState.Nutzeinheit.Devices.Select(y => y.Guid);

            return !DatabaseContext.DeviceOrderStates.Where(x => x.OrderGuid == orderGuid && deviceGuids.Contains(x.DeviceGuid)).Any(x => x.InProgress());
        }

        public void NutzeinheitSyncLiveDone(Guid nutzeinheitGuid)
        {
            Dal.Nutzeinheit nutzeinheit = DatabaseContext.Nutzeinheiten
                .Include(x => x.Photos)
                .Include(x => x.Devices)
                .ThenInclude(x => x.Photos)
                .Include(x => x.Nutzer)
                .ThenInclude(x => x.NutzerPersonen)
                .Include(x => x.Nutzer)
                .ThenInclude(x => x.NutzerQuadratmeter)
                .Include(x => x.Nutzer)
                .ThenInclude(x => x.NutzerCoOwnership)
                .Single(x => x.Guid == nutzeinheitGuid);

            nutzeinheit.IsCreatedByApp = false;

            foreach (Dal.Device device in nutzeinheit.Devices)
            {
                device.IsCreatedByApp = false;
            }

            foreach (Dal.Photo photo in nutzeinheit.Photos.Where(x => x.CreatedByApp))
            {
                photo.CreatedByApp = false;
            }

            foreach (Dal.Photo photo in nutzeinheit.Devices.SelectMany(x => x.Photos).Where(x => x.CreatedByApp))
            {
                photo.CreatedByApp = false;
            }

            foreach (Dal.NutzerPersonen nutzerPersonen in nutzeinheit.Nutzer.SelectMany(x => x.NutzerPersonen))
            {
                nutzerPersonen.IsCreatedByApp = false;
            }

            foreach (Dal.NutzerQuadratmeter nutzerQuadratmeter in nutzeinheit.Nutzer.SelectMany(x => x.NutzerQuadratmeter))
            {
                nutzerQuadratmeter.IsCreatedByApp = false;
            }

            foreach (Dal.NutzerCoOwnership nutzerCoOwnership in nutzeinheit.Nutzer.SelectMany(x => x.NutzerCoOwnership))
            {
                nutzerCoOwnership.IsCreatedByApp = false;
            }
        }

        public void NutzerSyncLiveDone(Guid nutzeinheitGuid)
        {
            Dal.Nutzeinheit nutzeinheit = DatabaseContext.Nutzeinheiten
                .Include(x => x.Photos)
                .Include(x => x.Nutzer)
                .ThenInclude(x => x.NutzerPersonen)
                .Include(x => x.Nutzer)
                .ThenInclude(x => x.NutzerQuadratmeter)
                .Include(x => x.Nutzer)
                .ThenInclude(x => x.NutzerCoOwnership)
                .Single(x => x.Guid == nutzeinheitGuid);

            nutzeinheit.IsCreatedByApp = false;

            foreach (Dal.NutzerPersonen nutzerPersonen in nutzeinheit.Nutzer.SelectMany(x=> x.NutzerPersonen))
            {
                nutzerPersonen.IsCreatedByApp = false;
            }

            foreach (Dal.NutzerQuadratmeter nutzerQuadratmeter in nutzeinheit.Nutzer.SelectMany(x => x.NutzerQuadratmeter))
            {
                nutzerQuadratmeter.IsCreatedByApp = false;
            }

            foreach (Dal.NutzerCoOwnership nutzerCoOwnership in nutzeinheit.Nutzer.SelectMany(x => x.NutzerCoOwnership))
            {
                nutzerCoOwnership.IsCreatedByApp = false;
            }

            foreach (Dal.Photo photo in nutzeinheit.Photos.Where(x => x.CreatedByApp))
            {
                photo.CreatedByApp = false;
            }
        }

        private void AddNutzeinheit(Dal.Nutzeinheit nutzeinheit)
        {
            if (nutzeinheit == null)
            {
                throw new ArgumentNullException(nameof(nutzeinheit));
            }

            DatabaseContext.Nutzeinheiten.Add(nutzeinheit);
        }
    }
}