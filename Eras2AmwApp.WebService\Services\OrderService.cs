﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="OrderService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Services
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using AutoMapper;
    using Database.Contexts;
    using Eras2Amw.Service.Server.ServiceModel.Common;
    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Database.Interfaces;
    using Eras2AmwApp.Domain.Eras2Amw.Enums;
    using Eras2AmwApp.Domain.Eras2Amw.Extensions;
    using Implementations;
    using Interfaces;
    using Mapper.AfterMap.ToDal;
    using Microsoft.EntityFrameworkCore;
    using Dal = Domain.Eras2Amw.Models;

    public class OrderService : IOrderService
    {
        private readonly IMapper mapper;

        private readonly IWebserviceBackup webserviceBackup;

        private readonly IDomainLoader domainLoader;

        private OrderService(IMapper mapper, IAppSettings appSettings, IDomainLoader domainLoader)
        {
            this.mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            this.domainLoader = domainLoader ?? throw new ArgumentNullException(nameof(domainLoader));
            webserviceBackup = new WebserviceBackup(appSettings);
        }

        public bool DoBackup { get; set; } = true;

        public Eras2AmwContext DatabaseContext { get; set; }

        public static IOrderService Create(IMapper mapper, IAppSettings appSettings, IDomainLoader domainLoader)
        {
            return new OrderService(mapper, appSettings, domainLoader);
        }

        public async Task Insert(Order order)
        {
            if (order == null)
            {
                throw new ArgumentNullException(nameof(order));
            }

            if (DoBackup)
            {
                await webserviceBackup.BackupDownloadOrder(order).ConfigureAwait(false);
            }

            Dictionary<string, Dal.Salutation> salutations = FindSalutations();
            Dictionary<string, Dal.Title> titles = FindTitles();

            var orderAfterMap = new OrderAfterMap()
            {
                Salutations = salutations,
                Titles = titles
            };

            var dalOrder = mapper.Map<Dal.Order>(order, opts => opts.AfterMap(orderAfterMap.Start));

            InsertOrder(dalOrder);
        }

        public async Task<IEnumerable<Order>> LoadReadyToSendAsync()
        {
            //IEnumerable<Order> orders = await DatabaseContext.Orders
            //    .Where(x => x.OrderState.InProgress() || x.OrderState.IsUpdating())
            //    .Select(x => mapper.Map<Order>(domainLoader.LoadOrder(x.Guid)))
            //    .ToListAsync();

            IEnumerable<Order> orders = DatabaseContext.Orders
                .Include(x => x.OrderState)
                .ToList() // Bring Orders and their OrderStates to memory
                .Where(x => x.OrderState != null && (x.OrderState.InProgress() || x.OrderState.IsUpdating()))
                .Select(x => mapper.Map<Order>(domainLoader.LoadOrder(x.Guid))) // Still inefficient LoadOrder
                .ToList();

            foreach (Order order in orders)
            {
                RemoveDeviceOrderStatesWithoutNutzeinheitOrderState(order);
            }

            if (DoBackup)
            {
                foreach (Order order in orders)
                {
                    await webserviceBackup.BackupUploadOrderAsync(order).ConfigureAwait(false);
                }
            }
            
            return orders;
        }

        public void SyncLiveDone(Guid orderGuid, Guid nutzeinheitGuid)
        {
            Dal.Nutzeinheit nutzeinheit = DatabaseContext.Nutzeinheiten.Include(x => x.Devices).Include(x => x.OrderStates).Single(x => x.Guid == nutzeinheitGuid);

            Dal.NutzeinheitOrderState nutzeinheitState = nutzeinheit.OrderStates.Single(x => x.OrderGuid == orderGuid);
            nutzeinheitState.ProcessState = ProcessState.Completed;

            Dal.Signature signature = DatabaseContext.Signatures.SingleOrDefault(x => x.OrderGuid == orderGuid && x.NutzeinheitGuid == nutzeinheitGuid && x.CreatedByApp);
            if (signature != null)
            {
                signature.CreatedByApp = false;
            }

            IQueryable<Dal.DeviceOrderState> deviceOrderStates = DatabaseContext.DeviceOrderStates.Where(x => x.OrderGuid == orderGuid && (x.IsUpdating() || x.IsCreating()) && nutzeinheit.Devices.Select(y => y.Guid).Contains(x.DeviceGuid));
            foreach (Dal.DeviceOrderState deviceOrderState in deviceOrderStates)
            {
                deviceOrderState.ProcessState = ProcessState.Completed;
            }
        }

        private void RemoveDeviceOrderStatesWithoutNutzeinheitOrderState(Order order)
        {
            if (order == null)
            {
                throw new ArgumentNullException(nameof(order));
            }

            var extendedDeviceOrderStates = order.DeviceOrderStates.Select(
                deviceOrderState => new
                {
                    OrderDeviceState = deviceOrderState,
                    DatabaseContext.Devices.Find(deviceOrderState.DeviceGuid).NutzeinheitGuid
                }
            ).ToList();

            IEnumerable<Guid> nutzeinheitOrderStatesGuids = order.NutzeinheitOrderStates.Select(x => x.NutzeinheitGuid);
            IList<DeviceOrderState> deviceOrderStates = new List<DeviceOrderState>();
            foreach (var extendedDeviceOrderState in extendedDeviceOrderStates)
            {
                if (nutzeinheitOrderStatesGuids.Contains(extendedDeviceOrderState.NutzeinheitGuid))
                {
                    deviceOrderStates.Add(extendedDeviceOrderState.OrderDeviceState);
                }
            }

            order.DeviceOrderStates = deviceOrderStates;
        }

        private void InsertOrder(Dal.Order order)
        {
            if (order == null)
            {
                throw new ArgumentNullException(nameof(order));
            }

            DatabaseContext.Orders.Add(order);
        }

        private Dictionary<string, Dal.Salutation> FindSalutations()
        {
            return DatabaseContext.Salutations.ToDictionary(x => x.Label, x => x);
        }

        private Dictionary<string, Dal.Title> FindTitles()
        {
            return DatabaseContext.Titles.ToDictionary(x => x.Label, x => x);
        }
    }
}