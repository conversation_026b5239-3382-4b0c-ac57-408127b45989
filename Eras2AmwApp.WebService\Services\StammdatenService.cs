﻿//  --------------------------------------------------------------------------------------------------------------------
//  <copyright file="StammdatenService.cs" company="Endian Code GmbH">
//  </copyright>
//  <summary>
// 
//  </summary>
//  --------------------------------------------------------------------------------------------------------------------

namespace Eras2AmwApp.WebService.Services
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;

    using AutoMapper;

    using Eras2Amw.Service.Server.ServiceModel.Eras2App.Get;

    using Eras2AmwApp.Common.Interfaces;
    using Eras2AmwApp.Database.Contexts;
    using Eras2AmwApp.WebService.Implementations;
    using Eras2AmwApp.WebService.Interfaces;

    using Dal = Domain.Eras2Amw.Models;

    public class StammdatenService : IStammdatenService
    {
        private readonly IMapper mapper;

        private readonly IWebserviceBackup webserviceBackup;

        private StammdatenService(IMapper mapper, IAppSettings appSettings)
        {
            this.mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            webserviceBackup = new WebserviceBackup(appSettings);
        }

        public Eras2AmwContext DatabaseContext { get; set; }

        public bool DoBackup { get; set; } = true;

        public static IStammdatenService Create(IMapper mapper, IAppSettings appSettings)
        {
            return new StammdatenService(mapper, appSettings);
        }

        public async Task Insert(GetStammdatenResponse stammdaten)
        {
            if (stammdaten == null)
            {
                throw new ArgumentNullException(nameof(stammdaten));
            }

            if (DoBackup)
            {
                await webserviceBackup.BackupStammdatenAsync(stammdaten).ConfigureAwait(false);
            }

            DatabaseContext.Articles.AddRange(mapper.Map<IEnumerable<Dal.Article>>(stammdaten.Articles));
            DatabaseContext.CommunicationFeatures.AddRange(mapper.Map<IEnumerable<Dal.CommunicationFeature>>(stammdaten.CommunicationFeatures));
            DatabaseContext.DeviceCatalog.AddRange(mapper.Map<IEnumerable<Dal.DeviceCatalog>>(stammdaten.DeviceCatalog));
            DatabaseContext.Manufacturers.AddRange(mapper.Map<IEnumerable<Dal.Manufacturer>>(stammdaten.Manufacturers));
            DatabaseContext.DeviceKinds.AddRange(mapper.Map<IEnumerable<Dal.DeviceKind>>(stammdaten.DeviceKinds));
            DatabaseContext.ReadingKinds.AddRange(mapper.Map<IEnumerable<Dal.ReadingKind>>(stammdaten.ReadingKinds));
            DatabaseContext.AmwInfoKeys.AddRange(mapper.Map<IEnumerable<Dal.AmwInfoKey>>(stammdaten.AmwInfoKeys));
            DatabaseContext.AdditionalArticles.AddRange(mapper.Map<IEnumerable<Dal.AdditionalArticle>>(stammdaten.AdditionalArticles));
            DatabaseContext.Rooms.AddRange(mapper.Map<IEnumerable<Dal.Room>>(stammdaten.Rooms));
        }
    }
}