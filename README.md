# Einführung
TODO: Bieten Sie eine kurze Einführung in Ihr Projekt. In diesem Abschnitt erläutern Sie die Ziele oder die Motivation für dieses Projekt. 

# Erste Schritte
TODO: Bieten Sie den Benutzern eine Anleitung, wie diese Ihren Code auf ihren eigenen Systemen einrichten und verwenden. In diesem Abschnitt können Sie folgende Themen behandeln:
1.	Installationsvorgang
2.	Softwareabhängigkeiten
3.	Neueste Releases
4.	API-Referenzen

# Build und Test
TODO: Beschreiben und zeigen Sie, wie Ihr Code kompiliert wird und wie die Tests ausgeführt werden. 

 #Beitragen
TODO: Erläutern Sie, wie andere Benutzer und Entwickler zu einer Verbesserung Ihres Codes beitragen können. 

Weitere Informationen zum Erstellen hilfreicher Infodateien finden Sie im folgenden [Leitfaden](https://docs.microsoft.com/de-de/azure/devops/repos/git/create-a-readme?view=azure-devops). Weitere Anregungen finden Sie zudem in den folgenden Infodateien:
– [ASP.NET Core](https://github.com/aspnet/Home)
– [Visual Studio Code](https://github.com/Microsoft/vscode)
– [Chakra Core](https://github.com/Microsoft/ChakraCore)